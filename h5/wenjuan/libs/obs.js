const ak = "9V5DYF5Y2KVIKCODXCHJ";
const sk = "jwiuAaQBRetr6kglPcfhPklrznBIW8j0qCNeRRhP";
const server = "obs.cn-east-3.myhuaweicloud.com";
const bucket = "zhihuituoyupt";
const windowUpload = (file, path, cb, newname, text) => {
  const obsClient = new ObsClient({
    access_key_id: ak,
    secret_access_key: sk,
    server: server,
    timeout: 60 * 5,
  });

  return new Promise((resolve, reject) => {
    let key = "web/" + path + "/"+ new Date().getTime() + file.name.replace(/,/g, "");

    obsClient.putObject(
      {
        Bucket: bucket,
        Key: key,
        SourceFile: file,
        ProgressCallback:
          cb ||
          ((e) => {
            // console.log(e, file.size, loadingInstance);
          }),
      },
      function (err, result) {
        if (err) {
          reject("");
        } else {
          if (result.CommonMsg.Status < 300) {
            console.log("Create object successfully!");
            resolve("https://" + bucket + "." + server + "/" + key);
          } else {
            console.log("Code-->", result.CommonMsg.Code);
            console.log("Message-->", result.CommonMsg.Message);
            reject("");
          }
        }
      }
    );
  });
};
