/*! For license information please see esdk-obs-browserjs.3.22.3.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ObsClient=t():e.ObsClient=t()}(self,(function(){return function(){var e,t,r={8392:function(e,t,r){"use strict";r(824),r(303),r(9265),r(706),r(6753),r(730),r(4906),r(4525),r(3618),r(1844),r(6371),r(1446),r(70),r(6028)},2990:function(e,t,r){e.exports=r(7408)},7648:function(e,t,r){"use strict";var n=r(610),o=r(2334),i=r(4942),a=r(3210),s=r(5755),c=r(3741),u=r(9912),l=r(5080),p=r(1306),d=r(5092);e.exports=function(e){return new Promise((function(t,r){var h,f=e.data,m=e.headers,y=e.responseType;function g(){e.cancelToken&&e.cancelToken.unsubscribe(h),e.signal&&e.signal.removeEventListener("abort",h)}n.isFormData(f)&&delete m["Content-Type"];var A=new XMLHttpRequest;if(e.auth){var v=e.auth.username||"",x=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";m.Authorization="Basic "+btoa(v+":"+x)}var b=s(e.baseURL,e.url);function w(){if(A){var n="getAllResponseHeaders"in A?c(A.getAllResponseHeaders()):null,i={data:y&&"text"!==y&&"json"!==y?A.response:A.responseText,status:A.status,statusText:A.statusText,headers:n,config:e,request:A};o((function(e){t(e),g()}),(function(e){r(e),g()}),i),A=null}}if(A.open(e.method.toUpperCase(),a(b,e.params,e.paramsSerializer),!0),A.timeout=e.timeout,"onloadend"in A?A.onloadend=w:A.onreadystatechange=function(){A&&4===A.readyState&&(0!==A.status||A.responseURL&&0===A.responseURL.indexOf("file:"))&&setTimeout(w)},A.onabort=function(){A&&(r(l("Request aborted",e,"ECONNABORTED",A)),A=null)},A.onerror=function(){r(l("Network Error",e,null,A)),A=null},A.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||p;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(l(t,e,n.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",A)),A=null},n.isStandardBrowserEnv()){var P=(e.withCredentials||u(b))&&e.xsrfCookieName?i.read(e.xsrfCookieName):void 0;P&&(m[e.xsrfHeaderName]=P)}"setRequestHeader"in A&&n.forEach(m,(function(e,t){void 0===f&&"content-type"===t.toLowerCase()?delete m[t]:A.setRequestHeader(t,e)})),n.isUndefined(e.withCredentials)||(A.withCredentials=!!e.withCredentials),y&&"json"!==y&&(A.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&A.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&A.upload&&A.upload.addEventListener("progress",e.onUploadProgress),(e.cancelToken||e.signal)&&(h=function(e){A&&(r(!e||e&&e.type?new d("canceled"):e),A.abort(),A=null)},e.cancelToken&&e.cancelToken.subscribe(h),e.signal&&(e.signal.aborted?h():e.signal.addEventListener("abort",h))),f||(f=null),A.send(f)}))}},7408:function(e,t,r){"use strict";var n=r(610),o=r(3824),i=r(1030),a=r(8261),s=function e(t){var r=new i(t),s=o(i.prototype.request,r);return n.extend(s,i.prototype,r),n.extend(s,r),s.create=function(r){return e(a(t,r))},s}(r(479));s.Axios=i,s.Cancel=r(5092),s.CancelToken=r(1667),s.isCancel=r(2969),s.VERSION=r(9815).version,s.all=function(e){return Promise.all(e)},s.spread=r(8629),s.isAxiosError=r(2101),e.exports=s,e.exports.default=s},5092:function(e){"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},1667:function(e,t,r){"use strict";var n=r(5092);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;this.promise.then((function(e){if(r._listeners){var t,n=r._listeners.length;for(t=0;t<n;t++)r._listeners[t](e);r._listeners=null}})),this.promise.then=function(e){var t,n=new Promise((function(e){r.subscribe(e),t=e})).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e((function(e){r.reason||(r.reason=new n(e),t(r.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},o.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},2969:function(e){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},1030:function(e,t,r){"use strict";var n=r(610),o=r(3210),i=r(3952),a=r(7544),s=r(8261),c=r(6660),u=c.validators;function l(e){this.defaults=e,this.interceptors={request:new i,response:new i}}l.prototype.request=function(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},(t=s(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var r=t.transitional;void 0!==r&&c.assertOptions(r,{silentJSONParsing:u.transitional(u.boolean),forcedJSONParsing:u.transitional(u.boolean),clarifyTimeoutError:u.transitional(u.boolean)},!1);var n=[],o=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,n.unshift(e.fulfilled,e.rejected))}));var i,l=[];if(this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)})),!o){var p=[a,void 0];for(Array.prototype.unshift.apply(p,n),p=p.concat(l),i=Promise.resolve(t);p.length;)i=i.then(p.shift(),p.shift());return i}for(var d=t;n.length;){var h=n.shift(),f=n.shift();try{d=h(d)}catch(e){f(e);break}}try{i=a(d)}catch(e){return Promise.reject(e)}for(;l.length;)i=i.then(l.shift(),l.shift());return i},l.prototype.getUri=function(e){return e=s(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],(function(e){l.prototype[e]=function(t,r){return this.request(s(r||{},{method:e,url:t,data:(r||{}).data}))}})),n.forEach(["post","put","patch"],(function(e){l.prototype[e]=function(t,r,n){return this.request(s(n||{},{method:e,url:t,data:r}))}})),e.exports=l},3952:function(e,t,r){"use strict";var n=r(610);function o(){this.handlers=[]}o.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){n.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},5755:function(e,t,r){"use strict";var n=r(7204),o=r(117);e.exports=function(e,t){return e&&!n(t)?o(e,t):t}},5080:function(e,t,r){"use strict";var n=r(4980);e.exports=function(e,t,r,o,i){var a=new Error(e);return n(a,t,r,o,i)}},7544:function(e,t,r){"use strict";var n=r(610),o=r(8622),i=r(2969),a=r(479),s=r(5092);function c(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new s("canceled")}e.exports=function(e){return c(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return c(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(c(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},4980:function(e){"use strict";e.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},e}},8261:function(e,t,r){"use strict";var n=r(610);e.exports=function(e,t){t=t||{};var r={};function o(e,t){return n.isPlainObject(e)&&n.isPlainObject(t)?n.merge(e,t):n.isPlainObject(t)?n.merge({},t):n.isArray(t)?t.slice():t}function i(r){return n.isUndefined(t[r])?n.isUndefined(e[r])?void 0:o(void 0,e[r]):o(e[r],t[r])}function a(e){if(!n.isUndefined(t[e]))return o(void 0,t[e])}function s(r){return n.isUndefined(t[r])?n.isUndefined(e[r])?void 0:o(void 0,e[r]):o(void 0,t[r])}function c(r){return r in t?o(e[r],t[r]):r in e?o(void 0,e[r]):void 0}var u={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:c};return n.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=u[e]||i,o=t(e);n.isUndefined(o)&&t!==c||(r[e]=o)})),r}},2334:function(e,t,r){"use strict";var n=r(5080);e.exports=function(e,t,r){var o=r.config.validateStatus;r.status&&o&&!o(r.status)?t(n("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},8622:function(e,t,r){"use strict";var n=r(610),o=r(479);e.exports=function(e,t,r){var i=this||o;return n.forEach(r,(function(r){e=r.call(i,e,t)})),e}},479:function(e,t,r){"use strict";var n=r(610),o=r(928),i=r(4980),a=r(1306),s={"Content-Type":"application/x-www-form-urlencoded"};function c(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var u,l={transitional:a,adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(u=r(7648)),u),transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(c(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)||t&&"application/json"===t["Content-Type"]?(c(t,"application/json"),function(e,t,r){if(n.isString(e))try{return(0,JSON.parse)(e),n.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||l.transitional,r=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,a=!r&&"json"===this.responseType;if(a||o&&n.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(a){if("SyntaxError"===e.name)throw i(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};n.forEach(["delete","get","head"],(function(e){l.headers[e]={}})),n.forEach(["post","put","patch"],(function(e){l.headers[e]=n.merge(s)})),e.exports=l},1306:function(e){"use strict";e.exports={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},9815:function(e){e.exports={version:"0.26.1"}},3824:function(e){"use strict";e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},3210:function(e,t,r){"use strict";var n=r(610);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(n.isURLSearchParams(t))i=t.toString();else{var a=[];n.forEach(t,(function(e,t){null!=e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,(function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))})))})),i=a.join("&")}if(i){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},117:function(e){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},4942:function(e,t,r){"use strict";var n=r(610);e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,o,i,a){var s=[];s.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(o)&&s.push("path="+o),n.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},7204:function(e){"use strict";e.exports=function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}},2101:function(e,t,r){"use strict";var n=r(610);e.exports=function(e){return n.isObject(e)&&!0===e.isAxiosError}},9912:function(e,t,r){"use strict";var n=r(610);e.exports=n.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=o(window.location.href),function(t){var r=n.isString(t)?o(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},928:function(e,t,r){"use strict";var n=r(610);e.exports=function(e,t){n.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))}},3741:function(e,t,r){"use strict";var n=r(610),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,i,a={};return e?(n.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=n.trim(e.substr(0,i)).toLowerCase(),r=n.trim(e.substr(i+1)),t){if(a[t]&&o.indexOf(t)>=0)return;a[t]="set-cookie"===t?(a[t]?a[t]:[]).concat([r]):a[t]?a[t]+", "+r:r}})),a):a}},8629:function(e){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},6660:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=r(9815).version,i={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){i[e]=function(r){return n(r)===e||"a"+(t<1?"n ":" ")+e}}));var a={};i.transitional=function(e,t,r){function n(e,t){return"[Axios v"+o+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(r,o,i){if(!1===e)throw new Error(n(o," has been removed"+(t?" in "+t:"")));return t&&!a[o]&&(a[o]=!0,console.warn(n(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,o,i)}},e.exports={assertOptions:function(e,t,r){if("object"!==n(e))throw new TypeError("options must be an object");for(var o=Object.keys(e),i=o.length;i-- >0;){var a=o[i],s=t[a];if(s){var c=e[a],u=void 0===c||s(c,a,e);if(!0!==u)throw new TypeError("option "+a+" must be "+u)}else if(!0!==r)throw Error("Unknown option "+a)}},validators:i}},610:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=r(3824),i=Object.prototype.toString;function a(e){return Array.isArray(e)}function s(e){return void 0===e}function c(e){return"[object ArrayBuffer]"===i.call(e)}function u(e){return null!==e&&"object"===n(e)}function l(e){if("[object Object]"!==i.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function p(e){return"[object Function]"===i.call(e)}function d(e,t){if(null!=e)if("object"!==n(e)&&(e=[e]),a(e))for(var r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:a,isArrayBuffer:c,isBuffer:function(e){return null!==e&&!s(e)&&null!==e.constructor&&!s(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"[object FormData]"===i.call(e)},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&c(e.buffer)},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:u,isPlainObject:l,isUndefined:s,isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:p,isStream:function(e){return u(e)&&p(e.pipe)},isURLSearchParams:function(e){return"[object URLSearchParams]"===i.call(e)},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:d,merge:function e(){var t={};function r(r,n){l(t[n])&&l(r)?t[n]=e(t[n],r):l(r)?t[n]=e({},r):a(r)?t[n]=r.slice():t[n]=r}for(var n=0,o=arguments.length;n<o;n++)d(arguments[n],r);return t},extend:function(e,t,r){return d(t,(function(t,n){e[n]=r&&"function"==typeof t?o(t,r):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},5917:function(e,t,r){var n;!function(o){"use strict";function i(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function a(e,t,r,n,o,a){return i((s=i(i(t,e),i(n,a)))<<(c=o)|s>>>32-c,r);var s,c}function s(e,t,r,n,o,i,s){return a(t&r|~t&n,e,t,o,i,s)}function c(e,t,r,n,o,i,s){return a(t&n|r&~n,e,t,o,i,s)}function u(e,t,r,n,o,i,s){return a(t^r^n,e,t,o,i,s)}function l(e,t,r,n,o,i,s){return a(r^(t|~n),e,t,o,i,s)}function p(e,t){var r,n,o,a,p;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var d=1732584193,h=-271733879,f=-1732584194,m=271733878;for(r=0;r<e.length;r+=16)n=d,o=h,a=f,p=m,d=s(d,h,f,m,e[r],7,-680876936),m=s(m,d,h,f,e[r+1],12,-389564586),f=s(f,m,d,h,e[r+2],17,606105819),h=s(h,f,m,d,e[r+3],22,-1044525330),d=s(d,h,f,m,e[r+4],7,-176418897),m=s(m,d,h,f,e[r+5],12,1200080426),f=s(f,m,d,h,e[r+6],17,-1473231341),h=s(h,f,m,d,e[r+7],22,-45705983),d=s(d,h,f,m,e[r+8],7,1770035416),m=s(m,d,h,f,e[r+9],12,-1958414417),f=s(f,m,d,h,e[r+10],17,-42063),h=s(h,f,m,d,e[r+11],22,-1990404162),d=s(d,h,f,m,e[r+12],7,1804603682),m=s(m,d,h,f,e[r+13],12,-40341101),f=s(f,m,d,h,e[r+14],17,-1502002290),d=c(d,h=s(h,f,m,d,e[r+15],22,1236535329),f,m,e[r+1],5,-165796510),m=c(m,d,h,f,e[r+6],9,-1069501632),f=c(f,m,d,h,e[r+11],14,643717713),h=c(h,f,m,d,e[r],20,-373897302),d=c(d,h,f,m,e[r+5],5,-701558691),m=c(m,d,h,f,e[r+10],9,38016083),f=c(f,m,d,h,e[r+15],14,-660478335),h=c(h,f,m,d,e[r+4],20,-405537848),d=c(d,h,f,m,e[r+9],5,568446438),m=c(m,d,h,f,e[r+14],9,-1019803690),f=c(f,m,d,h,e[r+3],14,-187363961),h=c(h,f,m,d,e[r+8],20,1163531501),d=c(d,h,f,m,e[r+13],5,-1444681467),m=c(m,d,h,f,e[r+2],9,-51403784),f=c(f,m,d,h,e[r+7],14,1735328473),d=u(d,h=c(h,f,m,d,e[r+12],20,-1926607734),f,m,e[r+5],4,-378558),m=u(m,d,h,f,e[r+8],11,-2022574463),f=u(f,m,d,h,e[r+11],16,1839030562),h=u(h,f,m,d,e[r+14],23,-35309556),d=u(d,h,f,m,e[r+1],4,-1530992060),m=u(m,d,h,f,e[r+4],11,1272893353),f=u(f,m,d,h,e[r+7],16,-155497632),h=u(h,f,m,d,e[r+10],23,-1094730640),d=u(d,h,f,m,e[r+13],4,681279174),m=u(m,d,h,f,e[r],11,-358537222),f=u(f,m,d,h,e[r+3],16,-722521979),h=u(h,f,m,d,e[r+6],23,76029189),d=u(d,h,f,m,e[r+9],4,-640364487),m=u(m,d,h,f,e[r+12],11,-421815835),f=u(f,m,d,h,e[r+15],16,530742520),d=l(d,h=u(h,f,m,d,e[r+2],23,-995338651),f,m,e[r],6,-198630844),m=l(m,d,h,f,e[r+7],10,1126891415),f=l(f,m,d,h,e[r+14],15,-1416354905),h=l(h,f,m,d,e[r+5],21,-57434055),d=l(d,h,f,m,e[r+12],6,1700485571),m=l(m,d,h,f,e[r+3],10,-1894986606),f=l(f,m,d,h,e[r+10],15,-1051523),h=l(h,f,m,d,e[r+1],21,-2054922799),d=l(d,h,f,m,e[r+8],6,1873313359),m=l(m,d,h,f,e[r+15],10,-30611744),f=l(f,m,d,h,e[r+6],15,-1560198380),h=l(h,f,m,d,e[r+13],21,1309151649),d=l(d,h,f,m,e[r+4],6,-145523070),m=l(m,d,h,f,e[r+11],10,-1120210379),f=l(f,m,d,h,e[r+2],15,718787259),h=l(h,f,m,d,e[r+9],21,-343485551),d=i(d,n),h=i(h,o),f=i(f,a),m=i(m,p);return[d,h,f,m]}function d(e){var t,r="",n=32*e.length;for(t=0;t<n;t+=8)r+=String.fromCharCode(e[t>>5]>>>t%32&255);return r}function h(e){var t,r=[];for(r[(e.length>>2)-1]=void 0,t=0;t<r.length;t+=1)r[t]=0;var n=8*e.length;for(t=0;t<n;t+=8)r[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return r}function f(e){var t,r,n="0123456789abcdef",o="";for(r=0;r<e.length;r+=1)t=e.charCodeAt(r),o+=n.charAt(t>>>4&15)+n.charAt(15&t);return o}function m(e){return unescape(encodeURIComponent(e))}function y(e){return function(e){return d(p(h(e),8*e.length))}(m(e))}function g(e,t){return function(e,t){var r,n,o=h(e),i=[],a=[];for(i[15]=a[15]=void 0,o.length>16&&(o=p(o,8*e.length)),r=0;r<16;r+=1)i[r]=909522486^o[r],a[r]=1549556828^o[r];return n=p(i.concat(h(t)),512+8*t.length),d(p(a.concat(n),640))}(m(e),m(t))}function A(e,t,r){return t?r?g(t,e):f(g(t,e)):r?y(e):f(y(e))}void 0===(n=function(){return A}.call(t,r,t,e))||(e.exports=n)}()},824:function(e,t,r){r(2353),r(3719),r(7279),r(6462),r(5048),r(3085),r(8149),r(1862),r(4151),r(1422),r(4289),r(7247),r(2490),r(513),r(5471),r(8848),r(7004),r(7678),r(4060),r(5283),r(1764),r(6831),r(5318),r(6597),r(6865),r(2750),r(7879),r(5878),r(2825),r(580),r(475),r(2346),r(1877),r(7156),r(6630),r(7494),r(8781),r(7533),r(3301),r(422),r(9705),r(711),r(6160),r(5387),r(4386),r(3568),r(6739),r(156),r(7022),r(4132),r(8463),r(3695),r(4795),r(7367),r(3184),r(6331),r(6617),r(2793),r(688),r(7793),r(5297),r(1937),r(7613),r(6270),r(7056),r(2307),r(3391),r(7770),r(8812),r(4518),r(5554),r(1986),r(7572),r(3844),r(8185),r(3375),r(5824),r(6103),r(2173),r(9752),r(8471),r(1655),r(2336),r(7909),r(760),r(5742),r(4358),r(9574),r(6167),r(7243),r(7084),r(9152),r(6674),r(2125),r(197),r(7536),r(3144),r(1094),r(7378),r(3774),r(9721),r(5488),r(9021),r(5743),r(7600),r(2953),r(3024),r(7977),r(5164),r(7679),r(900),r(3313),r(959),r(9511),r(6708),r(3443),r(5558),r(9159),r(8095),r(5977),r(5187),r(7444),r(925),r(7865),r(7053),r(3122),r(56),r(2162),r(1078),r(8981),r(9160),r(4082),r(1723),r(8324),r(7372),r(8490),r(7454),r(4728),e.exports=r(7619)},9265:function(e,t,r){r(8465),e.exports=r(7619).Array.flatMap},303:function(e,t,r){r(1248),e.exports=r(7619).Array.includes},6371:function(e,t,r){r(847),e.exports=r(7619).Object.entries},3618:function(e,t,r){r(3947),e.exports=r(7619).Object.getOwnPropertyDescriptors},1844:function(e,t,r){r(5378),e.exports=r(7619).Object.values},1446:function(e,t,r){"use strict";r(5164),r(6002),e.exports=r(7619).Promise.finally},6753:function(e,t,r){r(4234),e.exports=r(7619).String.padEnd},706:function(e,t,r){r(7087),e.exports=r(7619).String.padStart},4906:function(e,t,r){r(4834),e.exports=r(7619).String.trimRight},730:function(e,t,r){r(4931),e.exports=r(7619).String.trimLeft},4525:function(e,t,r){r(6177),e.exports=r(185).f("asyncIterator")},9269:function(e,t,r){r(8440),e.exports=r(2467).global},4732:function(e){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},4921:function(e,t,r){var n=r(8096);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},2467:function(e){var t=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=t)},8510:function(e,t,r){var n=r(4732);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,o){return e.call(t,r,n,o)}}return function(){return e.apply(t,arguments)}}},825:function(e,t,r){e.exports=!r(7435)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},4676:function(e,t,r){var n=r(8096),o=r(1554).document,i=n(o)&&n(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},5685:function(e,t,r){var n=r(1554),o=r(2467),i=r(8510),a=r(4609),s=r(1056),c=function e(t,r,c){var u,l,p,d=t&e.F,h=t&e.G,f=t&e.S,m=t&e.P,y=t&e.B,g=t&e.W,A=h?o:o[r]||(o[r]={}),v=A.prototype,x=h?n:f?n[r]:(n[r]||{}).prototype;for(u in h&&(c=r),c)(l=!d&&x&&void 0!==x[u])&&s(A,u)||(p=l?x[u]:c[u],A[u]=h&&"function"!=typeof x[u]?c[u]:y&&l?i(p,n):g&&x[u]==p?function(e){var t=function(t,r,n){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,r)}return new e(t,r,n)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(p):m&&"function"==typeof p?i(Function.call,p):p,m&&((A.virtual||(A.virtual={}))[u]=p,t&e.R&&v&&!v[u]&&a(v,u,p)))};c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},7435:function(e){e.exports=function(e){try{return!!e()}catch(e){return!0}}},1554:function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)},1056:function(e){var t={}.hasOwnProperty;e.exports=function(e,r){return t.call(e,r)}},4609:function(e,t,r){var n=r(8052),o=r(3625);e.exports=r(825)?function(e,t,r){return n.f(e,t,o(1,r))}:function(e,t,r){return e[t]=r,e}},5481:function(e,t,r){e.exports=!r(825)&&!r(7435)((function(){return 7!=Object.defineProperty(r(4676)("div"),"a",{get:function(){return 7}}).a}))},8096:function(e){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}e.exports=function(e){return"object"===t(e)?null!==e:"function"==typeof e}},8052:function(e,t,r){var n=r(4921),o=r(5481),i=r(5497),a=Object.defineProperty;t.f=r(825)?Object.defineProperty:function(e,t,r){if(n(e),t=i(t,!0),n(r),o)try{return a(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},3625:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},5497:function(e,t,r){var n=r(8096);e.exports=function(e,t){if(!n(e))return e;var r,o;if(t&&"function"==typeof(r=e.toString)&&!n(o=r.call(e)))return o;if("function"==typeof(r=e.valueOf)&&!n(o=r.call(e)))return o;if(!t&&"function"==typeof(r=e.toString)&&!n(o=r.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},8440:function(e,t,r){var n=r(5685);n(n.G,{global:r(1554)})},3679:function(e){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},5801:function(e,t,r){var n=r(1647);e.exports=function(e,t){if("number"!=typeof e&&"Number"!=n(e))throw TypeError(t);return+e}},3680:function(e,t,r){var n=r(3567)("unscopables"),o=Array.prototype;null==o[n]&&r(6879)(o,n,{}),e.exports=function(e){o[n][e]=!0}},1501:function(e,t,r){"use strict";var n=r(1692)(!0);e.exports=function(e,t,r){return t+(r?n(e,t).length:1)}},2450:function(e){e.exports=function(e,t,r,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(r+": incorrect invocation!");return e}},8069:function(e,t,r){var n=r(3506);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},2670:function(e,t,r){"use strict";var n=r(4674),o=r(8413),i=r(1284);e.exports=[].copyWithin||function(e,t){var r=n(this),a=i(r.length),s=o(e,a),c=o(t,a),u=arguments.length>2?arguments[2]:void 0,l=Math.min((void 0===u?a:o(u,a))-c,a-s),p=1;for(c<s&&s<c+l&&(p=-1,c+=l-1,s+=l-1);l-- >0;)c in r?r[s]=r[c]:delete r[s],s+=p,c+=p;return r}},73:function(e,t,r){"use strict";var n=r(4674),o=r(8413),i=r(1284);e.exports=function(e){for(var t=n(this),r=i(t.length),a=arguments.length,s=o(a>1?arguments[1]:void 0,r),c=a>2?arguments[2]:void 0,u=void 0===c?r:o(c,r);u>s;)t[s++]=e;return t}},6329:function(e,t,r){var n=r(8670),o=r(1284),i=r(8413);e.exports=function(e){return function(t,r,a){var s,c=n(t),u=o(c.length),l=i(a,u);if(e&&r!=r){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===r)return e||l||0;return!e&&-1}}},3828:function(e,t,r){var n=r(7863),o=r(7751),i=r(4674),a=r(1284),s=r(5026);e.exports=function(e,t){var r=1==e,c=2==e,u=3==e,l=4==e,p=6==e,d=5==e||p,h=t||s;return function(t,s,f){for(var m,y,g=i(t),A=o(g),v=n(s,f,3),x=a(A.length),b=0,w=r?h(t,x):c?h(t,0):void 0;x>b;b++)if((d||b in A)&&(y=v(m=A[b],b,g),e))if(r)w[b]=y;else if(y)switch(e){case 3:return!0;case 5:return m;case 6:return b;case 2:w.push(m)}else if(l)return!1;return p?-1:u||l?l:w}}},9222:function(e,t,r){var n=r(3679),o=r(4674),i=r(7751),a=r(1284);e.exports=function(e,t,r,s,c){n(t);var u=o(e),l=i(u),p=a(u.length),d=c?p-1:0,h=c?-1:1;if(r<2)for(;;){if(d in l){s=l[d],d+=h;break}if(d+=h,c?d<0:p<=d)throw TypeError("Reduce of empty array with no initial value")}for(;c?d>=0:p>d;d+=h)d in l&&(s=t(s,l[d],d,u));return s}},2475:function(e,t,r){var n=r(3506),o=r(8151),i=r(3567)("species");e.exports=function(e){var t;return o(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!o(t.prototype)||(t=void 0),n(t)&&null===(t=t[i])&&(t=void 0)),void 0===t?Array:t}},5026:function(e,t,r){var n=r(2475);e.exports=function(e,t){return new(n(e))(t)}},6059:function(e,t,r){"use strict";var n=r(3679),o=r(3506),i=r(4709),a=[].slice,s={},c=function(e,t,r){if(!(t in s)){for(var n=[],o=0;o<t;o++)n[o]="a["+o+"]";s[t]=Function("F,a","return new F("+n.join(",")+")")}return s[t](e,r)};e.exports=Function.bind||function(e){var t=n(this),r=a.call(arguments,1),s=function n(){var o=r.concat(a.call(arguments));return this instanceof n?c(t,o.length,o):i(t,o,e)};return o(t.prototype)&&(s.prototype=t.prototype),s}},9736:function(e,t,r){var n=r(1647),o=r(3567)("toStringTag"),i="Arguments"==n(function(){return arguments}());e.exports=function(e){var t,r,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),o))?r:i?n(t):"Object"==(a=n(t))&&"function"==typeof t.callee?"Arguments":a}},1647:function(e){var t={}.toString;e.exports=function(e){return t.call(e).slice(8,-1)}},5019:function(e,t,r){"use strict";var n=r(5395).f,o=r(1693),i=r(4112),a=r(7863),s=r(2450),c=r(8302),u=r(2117),l=r(3337),p=r(9629),d=r(508),h=r(3797).fastKey,f=r(1364),m=d?"_s":"size",y=function(e,t){var r,n=h(t);if("F"!==n)return e._i[n];for(r=e._f;r;r=r.n)if(r.k==t)return r};e.exports={getConstructor:function(e,t,r,u){var l=e((function(e,n){s(e,l,t,"_i"),e._t=t,e._i=o(null),e._f=void 0,e._l=void 0,e[m]=0,null!=n&&c(n,r,e[u],e)}));return i(l.prototype,{clear:function(){for(var e=f(this,t),r=e._i,n=e._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete r[n.i];e._f=e._l=void 0,e[m]=0},delete:function(e){var r=f(this,t),n=y(r,e);if(n){var o=n.n,i=n.p;delete r._i[n.i],n.r=!0,i&&(i.n=o),o&&(o.p=i),r._f==n&&(r._f=o),r._l==n&&(r._l=i),r[m]--}return!!n},forEach:function(e){f(this,t);for(var r,n=a(e,arguments.length>1?arguments[1]:void 0,3);r=r?r.n:this._f;)for(n(r.v,r.k,this);r&&r.r;)r=r.p},has:function(e){return!!y(f(this,t),e)}}),d&&n(l.prototype,"size",{get:function(){return f(this,t)[m]}}),l},def:function(e,t,r){var n,o,i=y(e,t);return i?i.v=r:(e._l=i={i:o=h(t,!0),k:t,v:r,p:n=e._l,n:void 0,r:!1},e._f||(e._f=i),n&&(n.n=i),e[m]++,"F"!==o&&(e._i[o]=i)),e},getEntry:y,setStrong:function(e,t,r){u(e,t,(function(e,r){this._t=f(e,t),this._k=r,this._l=void 0}),(function(){for(var e=this,t=e._k,r=e._l;r&&r.r;)r=r.p;return e._t&&(e._l=r=r?r.n:e._t._f)?l(0,"keys"==t?r.k:"values"==t?r.v:[r.k,r.v]):(e._t=void 0,l(1))}),r?"entries":"values",!r,!0),p(t)}}},8888:function(e,t,r){"use strict";var n=r(4112),o=r(3797).getWeak,i=r(8069),a=r(3506),s=r(2450),c=r(8302),u=r(3828),l=r(4307),p=r(1364),d=u(5),h=u(6),f=0,m=function(e){return e._l||(e._l=new y)},y=function(){this.a=[]},g=function(e,t){return d(e.a,(function(e){return e[0]===t}))};y.prototype={get:function(e){var t=g(this,e);if(t)return t[1]},has:function(e){return!!g(this,e)},set:function(e,t){var r=g(this,e);r?r[1]=t:this.a.push([e,t])},delete:function(e){var t=h(this.a,(function(t){return t[0]===e}));return~t&&this.a.splice(t,1),!!~t}},e.exports={getConstructor:function(e,t,r,i){var u=e((function(e,n){s(e,u,t,"_i"),e._t=t,e._i=f++,e._l=void 0,null!=n&&c(n,r,e[i],e)}));return n(u.prototype,{delete:function(e){if(!a(e))return!1;var r=o(e);return!0===r?m(p(this,t)).delete(e):r&&l(r,this._i)&&delete r[this._i]},has:function(e){if(!a(e))return!1;var r=o(e);return!0===r?m(p(this,t)).has(e):r&&l(r,this._i)}}),u},def:function(e,t,r){var n=o(i(t),!0);return!0===n?m(e).set(t,r):n[e._i]=r,e},ufstore:m}},1107:function(e,t,r){"use strict";var n=r(3349),o=r(3276),i=r(5911),a=r(4112),s=r(3797),c=r(8302),u=r(2450),l=r(3506),p=r(4277),d=r(2569),h=r(481),f=r(672);e.exports=function(e,t,r,m,y,g){var A=n[e],v=A,x=y?"set":"add",b=v&&v.prototype,w={},P=function(e){var t=b[e];i(b,e,"delete"==e||"has"==e?function(e){return!(g&&!l(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return g&&!l(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,r){return t.call(this,0===e?0:e,r),this})};if("function"==typeof v&&(g||b.forEach&&!p((function(){(new v).entries().next()})))){var k=new v,S=k[x](g?{}:-0,1)!=k,C=p((function(){k.has(1)})),T=d((function(e){new v(e)})),E=!g&&p((function(){for(var e=new v,t=5;t--;)e[x](t,t);return!e.has(-0)}));T||((v=t((function(t,r){u(t,v,e);var n=f(new A,t,v);return null!=r&&c(r,y,n[x],n),n}))).prototype=b,b.constructor=v),(C||E)&&(P("delete"),P("has"),y&&P("get")),(E||S)&&P(x),g&&b.clear&&delete b.clear}else v=m.getConstructor(t,e,y,x),a(v.prototype,r),s.NEED=!0;return h(v,e),w[e]=v,o(o.G+o.W+o.F*(v!=A),w),g||m.setStrong(v,e,y),v}},7619:function(e){var t=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=t)},5094:function(e,t,r){"use strict";var n=r(5395),o=r(432);e.exports=function(e,t,r){t in e?n.f(e,t,o(0,r)):e[t]=r}},7863:function(e,t,r){var n=r(3679);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,o){return e.call(t,r,n,o)}}return function(){return e.apply(t,arguments)}}},5325:function(e,t,r){"use strict";var n=r(4277),o=Date.prototype.getTime,i=Date.prototype.toISOString,a=function(e){return e>9?e:"0"+e};e.exports=n((function(){return"0385-07-25T07:06:39.999Z"!=i.call(new Date(-50000000000001))}))||!n((function(){i.call(new Date(NaN))}))?function(){if(!isFinite(o.call(this)))throw RangeError("Invalid time value");var e=this,t=e.getUTCFullYear(),r=e.getUTCMilliseconds(),n=t<0?"-":t>9999?"+":"";return n+("00000"+Math.abs(t)).slice(n?-6:-4)+"-"+a(e.getUTCMonth()+1)+"-"+a(e.getUTCDate())+"T"+a(e.getUTCHours())+":"+a(e.getUTCMinutes())+":"+a(e.getUTCSeconds())+"."+(r>99?r:"0"+a(r))+"Z"}:i},2556:function(e,t,r){"use strict";var n=r(8069),o=r(2950),i="number";e.exports=function(e){if("string"!==e&&e!==i&&"default"!==e)throw TypeError("Incorrect hint");return o(n(this),e!=i)}},6022:function(e){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},508:function(e,t,r){e.exports=!r(4277)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},8931:function(e,t,r){var n=r(3506),o=r(3349).document,i=n(o)&&n(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},5148:function(e){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},2337:function(e,t,r){var n=r(4190),o=r(4128),i=r(2224);e.exports=function(e){var t=n(e),r=o.f;if(r)for(var a,s=r(e),c=i.f,u=0;s.length>u;)c.call(e,a=s[u++])&&t.push(a);return t}},3276:function(e,t,r){var n=r(3349),o=r(7619),i=r(6879),a=r(5911),s=r(7863),c=function e(t,r,c){var u,l,p,d,h=t&e.F,f=t&e.G,m=t&e.P,y=t&e.B,g=f?n:t&e.S?n[r]||(n[r]={}):(n[r]||{}).prototype,A=f?o:o[r]||(o[r]={}),v=A.prototype||(A.prototype={});for(u in f&&(c=r),c)p=((l=!h&&g&&void 0!==g[u])?g:c)[u],d=y&&l?s(p,n):m&&"function"==typeof p?s(Function.call,p):p,g&&a(g,u,p,t&e.U),A[u]!=p&&i(A,u,d),m&&v[u]!=p&&(v[u]=p)};n.core=o,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},8834:function(e,t,r){var n=r(3567)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,!"/./"[e](t)}catch(e){}}return!0}},4277:function(e){e.exports=function(e){try{return!!e()}catch(e){return!0}}},5844:function(e,t,r){"use strict";r(5488);var n=r(5911),o=r(6879),i=r(4277),a=r(6022),s=r(3567),c=r(1796),u=s("species"),l=!i((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),p=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var r="ab".split(e);return 2===r.length&&"a"===r[0]&&"b"===r[1]}();e.exports=function(e,t,r){var d=s(e),h=!i((function(){var t={};return t[d]=function(){return 7},7!=""[e](t)})),f=h?!i((function(){var t=!1,r=/a/;return r.exec=function(){return t=!0,null},"split"===e&&(r.constructor={},r.constructor[u]=function(){return r}),r[d](""),!t})):void 0;if(!h||!f||"replace"===e&&!l||"split"===e&&!p){var m=/./[d],y=r(a,d,""[e],(function(e,t,r,n,o){return t.exec===c?h&&!o?{done:!0,value:m.call(t,r,n)}:{done:!0,value:e.call(r,t,n)}:{done:!1}})),g=y[0],A=y[1];n(String.prototype,e,g),o(RegExp.prototype,d,2==t?function(e,t){return A.call(e,this,t)}:function(e){return A.call(e,this)})}}},5056:function(e,t,r){"use strict";var n=r(8069);e.exports=function(){var e=n(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},1835:function(e,t,r){"use strict";var n=r(8151),o=r(3506),i=r(1284),a=r(7863),s=r(3567)("isConcatSpreadable");e.exports=function e(t,r,c,u,l,p,d,h){for(var f,m,y=l,g=0,A=!!d&&a(d,h,3);g<u;){if(g in c){if(f=A?A(c[g],g,r):c[g],m=!1,o(f)&&(m=void 0!==(m=f[s])?!!m:n(f)),m&&p>0)y=e(t,r,f,i(f.length),y,p-1)-1;else{if(y>=9007199254740991)throw TypeError();t[y]=f}y++}g++}return y}},8302:function(e,t,r){var n=r(7863),o=r(5684),i=r(6136),a=r(8069),s=r(1284),c=r(3613),u={},l={},p=e.exports=function(e,t,r,p,d){var h,f,m,y,g=d?function(){return e}:c(e),A=n(r,p,t?2:1),v=0;if("function"!=typeof g)throw TypeError(e+" is not iterable!");if(i(g)){for(h=s(e.length);h>v;v++)if((y=t?A(a(f=e[v])[0],f[1]):A(e[v]))===u||y===l)return y}else for(m=g.call(e);!(f=m.next()).done;)if((y=o(m,A,f.value,t))===u||y===l)return y};p.BREAK=u,p.RETURN=l},3699:function(e,t,r){e.exports=r(1375)("native-function-to-string",Function.toString)},3349:function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)},4307:function(e){var t={}.hasOwnProperty;e.exports=function(e,r){return t.call(e,r)}},6879:function(e,t,r){var n=r(5395),o=r(432);e.exports=r(508)?function(e,t,r){return n.f(e,t,o(1,r))}:function(e,t,r){return e[t]=r,e}},9684:function(e,t,r){var n=r(3349).document;e.exports=n&&n.documentElement},7581:function(e,t,r){e.exports=!r(508)&&!r(4277)((function(){return 7!=Object.defineProperty(r(8931)("div"),"a",{get:function(){return 7}}).a}))},672:function(e,t,r){var n=r(3506),o=r(4106).set;e.exports=function(e,t,r){var i,a=t.constructor;return a!==r&&"function"==typeof a&&(i=a.prototype)!==r.prototype&&n(i)&&o&&o(e,i),e}},4709:function(e){e.exports=function(e,t,r){var n=void 0===r;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},7751:function(e,t,r){var n=r(1647);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},6136:function(e,t,r){var n=r(9746),o=r(3567)("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||i[o]===e)}},8151:function(e,t,r){var n=r(1647);e.exports=Array.isArray||function(e){return"Array"==n(e)}},3830:function(e,t,r){var n=r(3506),o=Math.floor;e.exports=function(e){return!n(e)&&isFinite(e)&&o(e)===e}},3506:function(e){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}e.exports=function(e){return"object"===t(e)?null!==e:"function"==typeof e}},2741:function(e,t,r){var n=r(3506),o=r(1647),i=r(3567)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[i])?!!t:"RegExp"==o(e))}},5684:function(e,t,r){var n=r(8069);e.exports=function(e,t,r,o){try{return o?t(n(r)[0],r[1]):t(r)}catch(t){var i=e.return;throw void 0!==i&&n(i.call(e)),t}}},4076:function(e,t,r){"use strict";var n=r(1693),o=r(432),i=r(481),a={};r(6879)(a,r(3567)("iterator"),(function(){return this})),e.exports=function(e,t,r){e.prototype=n(a,{next:o(1,r)}),i(e,t+" Iterator")}},2117:function(e,t,r){"use strict";var n=r(5437),o=r(3276),i=r(5911),a=r(6879),s=r(9746),c=r(4076),u=r(481),l=r(8300),p=r(3567)("iterator"),d=!([].keys&&"next"in[].keys()),h="keys",f="values",m=function(){return this};e.exports=function(e,t,r,y,g,A,v){c(r,t,y);var x,b,w,P=function(e){if(!d&&e in T)return T[e];switch(e){case h:case f:return function(){return new r(this,e)}}return function(){return new r(this,e)}},k=t+" Iterator",S=g==f,C=!1,T=e.prototype,E=T[p]||T["@@iterator"]||g&&T[g],M=E||P(g),O=g?S?P("entries"):M:void 0,B="Array"==t&&T.entries||E;if(B&&(w=l(B.call(new e)))!==Object.prototype&&w.next&&(u(w,k,!0),n||"function"==typeof w[p]||a(w,p,m)),S&&E&&E.name!==f&&(C=!0,M=function(){return E.call(this)}),n&&!v||!d&&!C&&T[p]||a(T,p,M),s[t]=M,s[k]=m,g)if(x={values:S?M:P(f),keys:A?M:P(h),entries:O},v)for(b in x)b in T||i(T,b,x[b]);else o(o.P+o.F*(d||C),t,x);return x}},2569:function(e,t,r){var n=r(3567)("iterator"),o=!1;try{var i=[7][n]();i.return=function(){o=!0},Array.from(i,(function(){throw 2}))}catch(e){}e.exports=function(e,t){if(!t&&!o)return!1;var r=!1;try{var i=[7],a=i[n]();a.next=function(){return{done:r=!0}},i[n]=function(){return a},e(i)}catch(e){}return r}},3337:function(e){e.exports=function(e,t){return{value:t,done:!!e}}},9746:function(e){e.exports={}},5437:function(e){e.exports=!1},2998:function(e){var t=Math.expm1;e.exports=!t||t(10)>22025.465794806718||t(10)<22025.465794806718||-2e-17!=t(-2e-17)?function(e){return 0==(e=+e)?e:e>-1e-6&&e<1e-6?e+e*e/2:Math.exp(e)-1}:t},4619:function(e,t,r){var n=r(5596),o=Math.pow,i=o(2,-52),a=o(2,-23),s=o(2,127)*(2-a),c=o(2,-126);e.exports=Math.fround||function(e){var t,r,o=Math.abs(e),u=n(e);return o<c?u*(o/c/a+1/i-1/i)*c*a:(r=(t=(1+a/i)*o)-(t-o))>s||r!=r?u*(1/0):u*r}},5242:function(e){e.exports=Math.log1p||function(e){return(e=+e)>-1e-8&&e<1e-8?e-e*e/2:Math.log(1+e)}},5596:function(e){e.exports=Math.sign||function(e){return 0==(e=+e)||e!=e?e:e<0?-1:1}},3797:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=r(7398)("meta"),i=r(3506),a=r(4307),s=r(5395).f,c=0,u=Object.isExtensible||function(){return!0},l=!r(4277)((function(){return u(Object.preventExtensions({}))})),p=function(e){s(e,o,{value:{i:"O"+ ++c,w:{}}})},d=e.exports={KEY:o,NEED:!1,fastKey:function(e,t){if(!i(e))return"symbol"==n(e)?e:("string"==typeof e?"S":"P")+e;if(!a(e,o)){if(!u(e))return"F";if(!t)return"E";p(e)}return e[o].i},getWeak:function(e,t){if(!a(e,o)){if(!u(e))return!0;if(!t)return!1;p(e)}return e[o].w},onFreeze:function(e){return l&&d.NEED&&u(e)&&!a(e,o)&&p(e),e}}},2739:function(e,t,r){var n=r(3349),o=r(2007).set,i=n.MutationObserver||n.WebKitMutationObserver,a=n.process,s=n.Promise,c="process"==r(1647)(a);e.exports=function(){var e,t,r,u=function(){var n,o;for(c&&(n=a.domain)&&n.exit();e;){o=e.fn,e=e.next;try{o()}catch(n){throw e?r():t=void 0,n}}t=void 0,n&&n.enter()};if(c)r=function(){a.nextTick(u)};else if(!i||n.navigator&&n.navigator.standalone)if(s&&s.resolve){var l=s.resolve(void 0);r=function(){l.then(u)}}else r=function(){o.call(n,u)};else{var p=!0,d=document.createTextNode("");new i(u).observe(d,{characterData:!0}),r=function(){d.data=p=!p}}return function(n){var o={fn:n,next:void 0};t&&(t.next=o),e||(e=o,r()),t=o}}},8238:function(e,t,r){"use strict";var n=r(3679);function o(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n})),this.resolve=n(t),this.reject=n(r)}e.exports.f=function(e){return new o(e)}},4236:function(e,t,r){"use strict";var n=r(508),o=r(4190),i=r(4128),a=r(2224),s=r(4674),c=r(7751),u=Object.assign;e.exports=!u||r(4277)((function(){var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!=u({},e)[r]||Object.keys(u({},t)).join("")!=n}))?function(e,t){for(var r=s(e),u=arguments.length,l=1,p=i.f,d=a.f;u>l;)for(var h,f=c(arguments[l++]),m=p?o(f).concat(p(f)):o(f),y=m.length,g=0;y>g;)h=m[g++],n&&!d.call(f,h)||(r[h]=f[h]);return r}:u},1693:function(e,t,r){var n=r(8069),o=r(4553),i=r(5148),a=r(7967)("IE_PROTO"),s=function(){},c=function(){var e,t=r(8931)("iframe"),n=i.length;for(t.style.display="none",r(9684).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;n--;)delete c.prototype[i[n]];return c()};e.exports=Object.create||function(e,t){var r;return null!==e?(s.prototype=n(e),r=new s,s.prototype=null,r[a]=e):r=c(),void 0===t?r:o(r,t)}},5395:function(e,t,r){var n=r(8069),o=r(7581),i=r(2950),a=Object.defineProperty;t.f=r(508)?Object.defineProperty:function(e,t,r){if(n(e),t=i(t,!0),n(r),o)try{return a(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},4553:function(e,t,r){var n=r(5395),o=r(8069),i=r(4190);e.exports=r(508)?Object.defineProperties:function(e,t){o(e);for(var r,a=i(t),s=a.length,c=0;s>c;)n.f(e,r=a[c++],t[r]);return e}},833:function(e,t,r){var n=r(2224),o=r(432),i=r(8670),a=r(2950),s=r(4307),c=r(7581),u=Object.getOwnPropertyDescriptor;t.f=r(508)?u:function(e,t){if(e=i(e),t=a(t,!0),c)try{return u(e,t)}catch(e){}if(s(e,t))return o(!n.f.call(e,t),e[t])}},645:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=r(8670),i=r(1676).f,a={}.toString,s="object"==("undefined"==typeof window?"undefined":n(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"[object Window]"==a.call(e)?function(e){try{return i(e)}catch(e){return s.slice()}}(e):i(o(e))}},1676:function(e,t,r){var n=r(9593),o=r(5148).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,o)}},4128:function(e,t){t.f=Object.getOwnPropertySymbols},8300:function(e,t,r){var n=r(4307),o=r(4674),i=r(7967)("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),n(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},9593:function(e,t,r){var n=r(4307),o=r(8670),i=r(6329)(!1),a=r(7967)("IE_PROTO");e.exports=function(e,t){var r,s=o(e),c=0,u=[];for(r in s)r!=a&&n(s,r)&&u.push(r);for(;t.length>c;)n(s,r=t[c++])&&(~i(u,r)||u.push(r));return u}},4190:function(e,t,r){var n=r(9593),o=r(5148);e.exports=Object.keys||function(e){return n(e,o)}},2224:function(e,t){t.f={}.propertyIsEnumerable},139:function(e,t,r){var n=r(3276),o=r(7619),i=r(4277);e.exports=function(e,t){var r=(o.Object||{})[e]||Object[e],a={};a[e]=t(r),n(n.S+n.F*i((function(){r(1)})),"Object",a)}},4399:function(e,t,r){var n=r(508),o=r(4190),i=r(8670),a=r(2224).f;e.exports=function(e){return function(t){for(var r,s=i(t),c=o(s),u=c.length,l=0,p=[];u>l;)r=c[l++],n&&!a.call(s,r)||p.push(e?[r,s[r]]:s[r]);return p}}},456:function(e,t,r){var n=r(1676),o=r(4128),i=r(8069),a=r(3349).Reflect;e.exports=a&&a.ownKeys||function(e){var t=n.f(i(e)),r=o.f;return r?t.concat(r(e)):t}},3135:function(e,t,r){var n=r(3349).parseFloat,o=r(2829).trim;e.exports=1/n(r(3945)+"-0")!=-1/0?function(e){var t=o(String(e),3),r=n(t);return 0===r&&"-"==t.charAt(0)?-0:r}:n},2134:function(e,t,r){var n=r(3349).parseInt,o=r(2829).trim,i=r(3945),a=/^[-+]?0[xX]/;e.exports=8!==n(i+"08")||22!==n(i+"0x16")?function(e,t){var r=o(String(e),3);return n(r,t>>>0||(a.test(r)?16:10))}:n},5243:function(e){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},5750:function(e,t,r){var n=r(8069),o=r(3506),i=r(8238);e.exports=function(e,t){if(n(e),o(t)&&t.constructor===e)return t;var r=i.f(e);return(0,r.resolve)(t),r.promise}},432:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4112:function(e,t,r){var n=r(5911);e.exports=function(e,t,r){for(var o in t)n(e,o,t[o],r);return e}},5911:function(e,t,r){var n=r(3349),o=r(6879),i=r(4307),a=r(7398)("src"),s=r(3699),c="toString",u=(""+s).split(c);r(7619).inspectSource=function(e){return s.call(e)},(e.exports=function(e,t,r,s){var c="function"==typeof r;c&&(i(r,"name")||o(r,"name",t)),e[t]!==r&&(c&&(i(r,a)||o(r,a,e[t]?""+e[t]:u.join(String(t)))),e===n?e[t]=r:s?e[t]?e[t]=r:o(e,t,r):(delete e[t],o(e,t,r)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[a]||s.call(this)}))},3059:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=r(9736),i=RegExp.prototype.exec;e.exports=function(e,t){var r=e.exec;if("function"==typeof r){var a=r.call(e,t);if("object"!==n(a))throw new TypeError("RegExp exec method returned something other than an Object or null");return a}if("RegExp"!==o(e))throw new TypeError("RegExp#exec called on incompatible receiver");return i.call(e,t)}},1796:function(e,t,r){"use strict";var n,o,i=r(5056),a=RegExp.prototype.exec,s=String.prototype.replace,c=a,u=(n=/a/,o=/b*/g,a.call(n,"a"),a.call(o,"a"),0!==n.lastIndex||0!==o.lastIndex),l=void 0!==/()??/.exec("")[1];(u||l)&&(c=function(e){var t,r,n,o,c=this;return l&&(r=new RegExp("^"+c.source+"$(?!\\s)",i.call(c))),u&&(t=c.lastIndex),n=a.call(c,e),u&&n&&(c.lastIndex=c.global?n.index+n[0].length:t),l&&n&&n.length>1&&s.call(n[0],r,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(n[o]=void 0)})),n}),e.exports=c},5681:function(e){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}},4106:function(e,t,r){var n=r(3506),o=r(8069),i=function(e,t){if(o(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=r(7863)(Function.call,r(833).f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return i(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:i}},9629:function(e,t,r){"use strict";var n=r(3349),o=r(5395),i=r(508),a=r(3567)("species");e.exports=function(e){var t=n[e];i&&t&&!t[a]&&o.f(t,a,{configurable:!0,get:function(){return this}})}},481:function(e,t,r){var n=r(5395).f,o=r(4307),i=r(3567)("toStringTag");e.exports=function(e,t,r){e&&!o(e=r?e:e.prototype,i)&&n(e,i,{configurable:!0,value:t})}},7967:function(e,t,r){var n=r(1375)("keys"),o=r(7398);e.exports=function(e){return n[e]||(n[e]=o(e))}},1375:function(e,t,r){var n=r(7619),o=r(3349),i="__core-js_shared__",a=o[i]||(o[i]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:r(5437)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},5168:function(e,t,r){var n=r(8069),o=r(3679),i=r(3567)("species");e.exports=function(e,t){var r,a=n(e).constructor;return void 0===a||null==(r=n(a)[i])?t:o(r)}},4433:function(e,t,r){"use strict";var n=r(4277);e.exports=function(e,t){return!!e&&n((function(){t?e.call(null,(function(){}),1):e.call(null)}))}},1692:function(e,t,r){var n=r(8591),o=r(6022);e.exports=function(e){return function(t,r){var i,a,s=String(o(t)),c=n(r),u=s.length;return c<0||c>=u?e?"":void 0:(i=s.charCodeAt(c))<55296||i>56319||c+1===u||(a=s.charCodeAt(c+1))<56320||a>57343?e?s.charAt(c):i:e?s.slice(c,c+2):a-56320+(i-55296<<10)+65536}}},2445:function(e,t,r){var n=r(2741),o=r(6022);e.exports=function(e,t,r){if(n(t))throw TypeError("String#"+r+" doesn't accept regex!");return String(o(e))}},591:function(e,t,r){var n=r(3276),o=r(4277),i=r(6022),a=/"/g,s=function(e,t,r,n){var o=String(i(e)),s="<"+t;return""!==r&&(s+=" "+r+'="'+String(n).replace(a,"&quot;")+'"'),s+">"+o+"</"+t+">"};e.exports=function(e,t){var r={};r[e]=t(s),n(n.P+n.F*o((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3})),"String",r)}},2713:function(e,t,r){var n=r(1284),o=r(5067),i=r(6022);e.exports=function(e,t,r,a){var s=String(i(e)),c=s.length,u=void 0===r?" ":String(r),l=n(t);if(l<=c||""==u)return s;var p=l-c,d=o.call(u,Math.ceil(p/u.length));return d.length>p&&(d=d.slice(0,p)),a?d+s:s+d}},5067:function(e,t,r){"use strict";var n=r(8591),o=r(6022);e.exports=function(e){var t=String(o(this)),r="",i=n(e);if(i<0||i==1/0)throw RangeError("Count can't be negative");for(;i>0;(i>>>=1)&&(t+=t))1&i&&(r+=t);return r}},2829:function(e,t,r){var n=r(3276),o=r(6022),i=r(4277),a=r(3945),s="["+a+"]",c=RegExp("^"+s+s+"*"),u=RegExp(s+s+"*$"),l=function(e,t,r){var o={},s=i((function(){return!!a[e]()||"​"!="​"[e]()})),c=o[e]=s?t(p):a[e];r&&(o[r]=c),n(n.P+n.F*s,"String",o)},p=l.trim=function(e,t){return e=String(o(e)),1&t&&(e=e.replace(c,"")),2&t&&(e=e.replace(u,"")),e};e.exports=l},3945:function(e){e.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},2007:function(e,t,r){var n,o,i,a=r(7863),s=r(4709),c=r(9684),u=r(8931),l=r(3349),p=l.process,d=l.setImmediate,h=l.clearImmediate,f=l.MessageChannel,m=l.Dispatch,y=0,g={},A=function(){var e=+this;if(g.hasOwnProperty(e)){var t=g[e];delete g[e],t()}},v=function(e){A.call(e.data)};d&&h||(d=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return g[++y]=function(){s("function"==typeof e?e:Function(e),t)},n(y),y},h=function(e){delete g[e]},"process"==r(1647)(p)?n=function(e){p.nextTick(a(A,e,1))}:m&&m.now?n=function(e){m.now(a(A,e,1))}:f?(i=(o=new f).port2,o.port1.onmessage=v,n=a(i.postMessage,i,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(n=function(e){l.postMessage(e+"","*")},l.addEventListener("message",v,!1)):n="onreadystatechange"in u("script")?function(e){c.appendChild(u("script")).onreadystatechange=function(){c.removeChild(this),A.call(e)}}:function(e){setTimeout(a(A,e,1),0)}),e.exports={set:d,clear:h}},8413:function(e,t,r){var n=r(8591),o=Math.max,i=Math.min;e.exports=function(e,t){return(e=n(e))<0?o(e+t,0):i(e,t)}},5568:function(e,t,r){var n=r(8591),o=r(1284);e.exports=function(e){if(void 0===e)return 0;var t=n(e),r=o(t);if(t!==r)throw RangeError("Wrong length!");return r}},8591:function(e){var t=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:t)(e)}},8670:function(e,t,r){var n=r(7751),o=r(6022);e.exports=function(e){return n(o(e))}},1284:function(e,t,r){var n=r(8591),o=Math.min;e.exports=function(e){return e>0?o(n(e),9007199254740991):0}},4674:function(e,t,r){var n=r(6022);e.exports=function(e){return Object(n(e))}},2950:function(e,t,r){var n=r(3506);e.exports=function(e,t){if(!n(e))return e;var r,o;if(t&&"function"==typeof(r=e.toString)&&!n(o=r.call(e)))return o;if("function"==typeof(r=e.valueOf)&&!n(o=r.call(e)))return o;if(!t&&"function"==typeof(r=e.toString)&&!n(o=r.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},6500:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}if(r(508)){var o=r(5437),i=r(3349),a=r(4277),s=r(3276),c=r(6303),u=r(2428),l=r(7863),p=r(2450),d=r(432),h=r(6879),f=r(4112),m=r(8591),y=r(1284),g=r(5568),A=r(8413),v=r(2950),x=r(4307),b=r(9736),w=r(3506),P=r(4674),k=r(6136),S=r(1693),C=r(8300),T=r(1676).f,E=r(3613),M=r(7398),O=r(3567),B=r(3828),R=r(6329),I=r(5168),D=r(3774),j=r(9746),L=r(2569),_=r(9629),q=r(73),N=r(2670),F=r(5395),U=r(833),K=F.f,G=U.f,H=i.RangeError,z=i.TypeError,W=i.Uint8Array,V="ArrayBuffer",Q="SharedArrayBuffer",Y="BYTES_PER_ELEMENT",X=Array.prototype,$=u.ArrayBuffer,J=u.DataView,Z=B(0),ee=B(2),te=B(3),re=B(4),ne=B(5),oe=B(6),ie=R(!0),ae=R(!1),se=D.values,ce=D.keys,ue=D.entries,le=X.lastIndexOf,pe=X.reduce,de=X.reduceRight,he=X.join,fe=X.sort,me=X.slice,ye=X.toString,ge=X.toLocaleString,Ae=O("iterator"),ve=O("toStringTag"),xe=M("typed_constructor"),be=M("def_constructor"),we=c.CONSTR,Pe=c.TYPED,ke=c.VIEW,Se="Wrong length!",Ce=B(1,(function(e,t){return Be(I(e,e[be]),t)})),Te=a((function(){return 1===new W(new Uint16Array([1]).buffer)[0]})),Ee=!!W&&!!W.prototype.set&&a((function(){new W(1).set({})})),Me=function(e,t){var r=m(e);if(r<0||r%t)throw H("Wrong offset!");return r},Oe=function(e){if(w(e)&&Pe in e)return e;throw z(e+" is not a typed array!")},Be=function(e,t){if(!w(e)||!(xe in e))throw z("It is not a typed array constructor!");return new e(t)},Re=function(e,t){return Ie(I(e,e[be]),t)},Ie=function(e,t){for(var r=0,n=t.length,o=Be(e,n);n>r;)o[r]=t[r++];return o},De=function(e,t,r){K(e,t,{get:function(){return this._d[r]}})},je=function(e){var t,r,n,o,i,a,s=P(e),c=arguments.length,u=c>1?arguments[1]:void 0,p=void 0!==u,d=E(s);if(null!=d&&!k(d)){for(a=d.call(s),n=[],t=0;!(i=a.next()).done;t++)n.push(i.value);s=n}for(p&&c>2&&(u=l(u,arguments[2],2)),t=0,r=y(s.length),o=Be(this,r);r>t;t++)o[t]=p?u(s[t],t):s[t];return o},Le=function(){for(var e=0,t=arguments.length,r=Be(this,t);t>e;)r[e]=arguments[e++];return r},_e=!!W&&a((function(){ge.call(new W(1))})),qe=function(){return ge.apply(_e?me.call(Oe(this)):Oe(this),arguments)},Ne={copyWithin:function(e,t){return N.call(Oe(this),e,t,arguments.length>2?arguments[2]:void 0)},every:function(e){return re(Oe(this),e,arguments.length>1?arguments[1]:void 0)},fill:function(e){return q.apply(Oe(this),arguments)},filter:function(e){return Re(this,ee(Oe(this),e,arguments.length>1?arguments[1]:void 0))},find:function(e){return ne(Oe(this),e,arguments.length>1?arguments[1]:void 0)},findIndex:function(e){return oe(Oe(this),e,arguments.length>1?arguments[1]:void 0)},forEach:function(e){Z(Oe(this),e,arguments.length>1?arguments[1]:void 0)},indexOf:function(e){return ae(Oe(this),e,arguments.length>1?arguments[1]:void 0)},includes:function(e){return ie(Oe(this),e,arguments.length>1?arguments[1]:void 0)},join:function(e){return he.apply(Oe(this),arguments)},lastIndexOf:function(e){return le.apply(Oe(this),arguments)},map:function(e){return Ce(Oe(this),e,arguments.length>1?arguments[1]:void 0)},reduce:function(e){return pe.apply(Oe(this),arguments)},reduceRight:function(e){return de.apply(Oe(this),arguments)},reverse:function(){for(var e,t=this,r=Oe(t).length,n=Math.floor(r/2),o=0;o<n;)e=t[o],t[o++]=t[--r],t[r]=e;return t},some:function(e){return te(Oe(this),e,arguments.length>1?arguments[1]:void 0)},sort:function(e){return fe.call(Oe(this),e)},subarray:function(e,t){var r=Oe(this),n=r.length,o=A(e,n);return new(I(r,r[be]))(r.buffer,r.byteOffset+o*r.BYTES_PER_ELEMENT,y((void 0===t?n:A(t,n))-o))}},Fe=function(e,t){return Re(this,me.call(Oe(this),e,t))},Ue=function(e){Oe(this);var t=Me(arguments[1],1),r=this.length,n=P(e),o=y(n.length),i=0;if(o+t>r)throw H(Se);for(;i<o;)this[t+i]=n[i++]},Ke={entries:function(){return ue.call(Oe(this))},keys:function(){return ce.call(Oe(this))},values:function(){return se.call(Oe(this))}},Ge=function(e,t){return w(e)&&e[Pe]&&"symbol"!=n(t)&&t in e&&String(+t)==String(t)},He=function(e,t){return Ge(e,t=v(t,!0))?d(2,e[t]):G(e,t)},ze=function(e,t,r){return!(Ge(e,t=v(t,!0))&&w(r)&&x(r,"value"))||x(r,"get")||x(r,"set")||r.configurable||x(r,"writable")&&!r.writable||x(r,"enumerable")&&!r.enumerable?K(e,t,r):(e[t]=r.value,e)};we||(U.f=He,F.f=ze),s(s.S+s.F*!we,"Object",{getOwnPropertyDescriptor:He,defineProperty:ze}),a((function(){ye.call({})}))&&(ye=ge=function(){return he.call(this)});var We=f({},Ne);f(We,Ke),h(We,Ae,Ke.values),f(We,{slice:Fe,set:Ue,constructor:function(){},toString:ye,toLocaleString:qe}),De(We,"buffer","b"),De(We,"byteOffset","o"),De(We,"byteLength","l"),De(We,"length","e"),K(We,ve,{get:function(){return this[Pe]}}),e.exports=function(e,t,r,n){var u=e+((n=!!n)?"Clamped":"")+"Array",l="get"+e,d="set"+e,f=i[u],m=f||{},A=f&&C(f),v=!f||!c.ABV,x={},P=f&&f.prototype,k=function(e,r){K(e,r,{get:function(){return function(e,r){var n=e._d;return n.v[l](r*t+n.o,Te)}(this,r)},set:function(e){return function(e,r,o){var i=e._d;n&&(o=(o=Math.round(o))<0?0:o>255?255:255&o),i.v[d](r*t+i.o,o,Te)}(this,r,e)},enumerable:!0})};v?(f=r((function(e,r,n,o){p(e,f,u,"_d");var i,a,s,c,l=0,d=0;if(w(r)){if(!(r instanceof $||(c=b(r))==V||c==Q))return Pe in r?Ie(f,r):je.call(f,r);i=r,d=Me(n,t);var m=r.byteLength;if(void 0===o){if(m%t)throw H(Se);if((a=m-d)<0)throw H(Se)}else if((a=y(o)*t)+d>m)throw H(Se);s=a/t}else s=g(r),i=new $(a=s*t);for(h(e,"_d",{b:i,o:d,l:a,e:s,v:new J(i)});l<s;)k(e,l++)})),P=f.prototype=S(We),h(P,"constructor",f)):a((function(){f(1)}))&&a((function(){new f(-1)}))&&L((function(e){new f,new f(null),new f(1.5),new f(e)}),!0)||(f=r((function(e,r,n,o){var i;return p(e,f,u),w(r)?r instanceof $||(i=b(r))==V||i==Q?void 0!==o?new m(r,Me(n,t),o):void 0!==n?new m(r,Me(n,t)):new m(r):Pe in r?Ie(f,r):je.call(f,r):new m(g(r))})),Z(A!==Function.prototype?T(m).concat(T(A)):T(m),(function(e){e in f||h(f,e,m[e])})),f.prototype=P,o||(P.constructor=f));var E=P[Ae],M=!!E&&("values"==E.name||null==E.name),O=Ke.values;h(f,xe,!0),h(P,Pe,u),h(P,ke,!0),h(P,be,f),(n?new f(1)[ve]==u:ve in P)||K(P,ve,{get:function(){return u}}),x[u]=f,s(s.G+s.W+s.F*(f!=m),x),s(s.S,u,{BYTES_PER_ELEMENT:t}),s(s.S+s.F*a((function(){m.of.call(f,1)})),u,{from:je,of:Le}),Y in P||h(P,Y,t),s(s.P,u,Ne),_(u),s(s.P+s.F*Ee,u,{set:Ue}),s(s.P+s.F*!M,u,Ke),o||P.toString==ye||(P.toString=ye),s(s.P+s.F*a((function(){new f(1).slice()})),u,{slice:Fe}),s(s.P+s.F*(a((function(){return[1,2].toLocaleString()!=new f([1,2]).toLocaleString()}))||!a((function(){P.toLocaleString.call([1,2])}))),u,{toLocaleString:qe}),j[u]=M?E:O,o||M||h(P,Ae,O)}}else e.exports=function(){}},2428:function(e,t,r){"use strict";var n=r(3349),o=r(508),i=r(5437),a=r(6303),s=r(6879),c=r(4112),u=r(4277),l=r(2450),p=r(8591),d=r(1284),h=r(5568),f=r(1676).f,m=r(5395).f,y=r(73),g=r(481),A="ArrayBuffer",v="DataView",x="Wrong index!",b=n.ArrayBuffer,w=n.DataView,P=n.Math,k=n.RangeError,S=n.Infinity,C=b,T=P.abs,E=P.pow,M=P.floor,O=P.log,B=P.LN2,R="buffer",I="byteLength",D="byteOffset",j=o?"_b":R,L=o?"_l":I,_=o?"_o":D;function q(e,t,r){var n,o,i,a=new Array(r),s=8*r-t-1,c=(1<<s)-1,u=c>>1,l=23===t?E(2,-24)-E(2,-77):0,p=0,d=e<0||0===e&&1/e<0?1:0;for((e=T(e))!=e||e===S?(o=e!=e?1:0,n=c):(n=M(O(e)/B),e*(i=E(2,-n))<1&&(n--,i*=2),(e+=n+u>=1?l/i:l*E(2,1-u))*i>=2&&(n++,i/=2),n+u>=c?(o=0,n=c):n+u>=1?(o=(e*i-1)*E(2,t),n+=u):(o=e*E(2,u-1)*E(2,t),n=0));t>=8;a[p++]=255&o,o/=256,t-=8);for(n=n<<t|o,s+=t;s>0;a[p++]=255&n,n/=256,s-=8);return a[--p]|=128*d,a}function N(e,t,r){var n,o=8*r-t-1,i=(1<<o)-1,a=i>>1,s=o-7,c=r-1,u=e[c--],l=127&u;for(u>>=7;s>0;l=256*l+e[c],c--,s-=8);for(n=l&(1<<-s)-1,l>>=-s,s+=t;s>0;n=256*n+e[c],c--,s-=8);if(0===l)l=1-a;else{if(l===i)return n?NaN:u?-S:S;n+=E(2,t),l-=a}return(u?-1:1)*n*E(2,l-t)}function F(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]}function U(e){return[255&e]}function K(e){return[255&e,e>>8&255]}function G(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]}function H(e){return q(e,52,8)}function z(e){return q(e,23,4)}function W(e,t,r){m(e.prototype,t,{get:function(){return this[r]}})}function V(e,t,r,n){var o=h(+r);if(o+t>e[L])throw k(x);var i=e[j]._b,a=o+e[_],s=i.slice(a,a+t);return n?s:s.reverse()}function Q(e,t,r,n,o,i){var a=h(+r);if(a+t>e[L])throw k(x);for(var s=e[j]._b,c=a+e[_],u=n(+o),l=0;l<t;l++)s[c+l]=u[i?l:t-l-1]}if(a.ABV){if(!u((function(){b(1)}))||!u((function(){new b(-1)}))||u((function(){return new b,new b(1.5),new b(NaN),b.name!=A}))){for(var Y,X=(b=function(e){return l(this,b),new C(h(e))}).prototype=C.prototype,$=f(C),J=0;$.length>J;)(Y=$[J++])in b||s(b,Y,C[Y]);i||(X.constructor=b)}var Z=new w(new b(2)),ee=w.prototype.setInt8;Z.setInt8(0,2147483648),Z.setInt8(1,2147483649),!Z.getInt8(0)&&Z.getInt8(1)||c(w.prototype,{setInt8:function(e,t){ee.call(this,e,t<<24>>24)},setUint8:function(e,t){ee.call(this,e,t<<24>>24)}},!0)}else b=function(e){l(this,b,A);var t=h(e);this._b=y.call(new Array(t),0),this[L]=t},w=function(e,t,r){l(this,w,v),l(e,b,v);var n=e[L],o=p(t);if(o<0||o>n)throw k("Wrong offset!");if(o+(r=void 0===r?n-o:d(r))>n)throw k("Wrong length!");this[j]=e,this[_]=o,this[L]=r},o&&(W(b,I,"_l"),W(w,R,"_b"),W(w,I,"_l"),W(w,D,"_o")),c(w.prototype,{getInt8:function(e){return V(this,1,e)[0]<<24>>24},getUint8:function(e){return V(this,1,e)[0]},getInt16:function(e){var t=V(this,2,e,arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=V(this,2,e,arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return F(V(this,4,e,arguments[1]))},getUint32:function(e){return F(V(this,4,e,arguments[1]))>>>0},getFloat32:function(e){return N(V(this,4,e,arguments[1]),23,4)},getFloat64:function(e){return N(V(this,8,e,arguments[1]),52,8)},setInt8:function(e,t){Q(this,1,e,U,t)},setUint8:function(e,t){Q(this,1,e,U,t)},setInt16:function(e,t){Q(this,2,e,K,t,arguments[2])},setUint16:function(e,t){Q(this,2,e,K,t,arguments[2])},setInt32:function(e,t){Q(this,4,e,G,t,arguments[2])},setUint32:function(e,t){Q(this,4,e,G,t,arguments[2])},setFloat32:function(e,t){Q(this,4,e,z,t,arguments[2])},setFloat64:function(e,t){Q(this,8,e,H,t,arguments[2])}});g(b,A),g(w,v),s(w.prototype,a.VIEW,!0),t.ArrayBuffer=b,t.DataView=w},6303:function(e,t,r){for(var n,o=r(3349),i=r(6879),a=r(7398),s=a("typed_array"),c=a("view"),u=!(!o.ArrayBuffer||!o.DataView),l=u,p=0,d="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");p<9;)(n=o[d[p++]])?(i(n.prototype,s,!0),i(n.prototype,c,!0)):l=!1;e.exports={ABV:u,CONSTR:l,TYPED:s,VIEW:c}},7398:function(e){var t=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++t+r).toString(36))}},3115:function(e,t,r){var n=r(3349).navigator;e.exports=n&&n.userAgent||""},1364:function(e,t,r){var n=r(3506);e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},5490:function(e,t,r){var n=r(3349),o=r(7619),i=r(5437),a=r(185),s=r(5395).f;e.exports=function(e){var t=o.Symbol||(o.Symbol=i?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:a.f(e)})}},185:function(e,t,r){t.f=r(3567)},3567:function(e,t,r){var n=r(1375)("wks"),o=r(7398),i=r(3349).Symbol,a="function"==typeof i;(e.exports=function(e){return n[e]||(n[e]=a&&i[e]||(a?i:o)("Symbol."+e))}).store=n},3613:function(e,t,r){var n=r(9736),o=r(3567)("iterator"),i=r(9746);e.exports=r(7619).getIteratorMethod=function(e){if(null!=e)return e[o]||e["@@iterator"]||i[n(e)]}},197:function(e,t,r){var n=r(3276);n(n.P,"Array",{copyWithin:r(2670)}),r(3680)("copyWithin")},7243:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(4);n(n.P+n.F*!r(4433)([].every,!0),"Array",{every:function(e){return o(this,e,arguments[1])}})},7536:function(e,t,r){var n=r(3276);n(n.P,"Array",{fill:r(73)}),r(3680)("fill")},9574:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(2);n(n.P+n.F*!r(4433)([].filter,!0),"Array",{filter:function(e){return o(this,e,arguments[1])}})},1094:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(6),i="findIndex",a=!0;i in[]&&Array(1)[i]((function(){a=!1})),n(n.P+n.F*a,"Array",{findIndex:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),r(3680)(i)},3144:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(5),i="find",a=!0;i in[]&&Array(1).find((function(){a=!1})),n(n.P+n.F*a,"Array",{find:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),r(3680)(i)},5742:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(0),i=r(4433)([].forEach,!0);n(n.P+n.F*!i,"Array",{forEach:function(e){return o(this,e,arguments[1])}})},8471:function(e,t,r){"use strict";var n=r(7863),o=r(3276),i=r(4674),a=r(5684),s=r(6136),c=r(1284),u=r(5094),l=r(3613);o(o.S+o.F*!r(2569)((function(e){Array.from(e)})),"Array",{from:function(e){var t,r,o,p,d=i(e),h="function"==typeof this?this:Array,f=arguments.length,m=f>1?arguments[1]:void 0,y=void 0!==m,g=0,A=l(d);if(y&&(m=n(m,f>2?arguments[2]:void 0,2)),null==A||h==Array&&s(A))for(r=new h(t=c(d.length));t>g;g++)u(r,g,y?m(d[g],g):d[g]);else for(p=A.call(d),r=new h;!(o=p.next()).done;g++)u(r,g,y?a(p,m,[o.value,g],!0):o.value);return r.length=g,r}})},6674:function(e,t,r){"use strict";var n=r(3276),o=r(6329)(!1),i=[].indexOf,a=!!i&&1/[1].indexOf(1,-0)<0;n(n.P+n.F*(a||!r(4433)(i)),"Array",{indexOf:function(e){return a?i.apply(this,arguments)||0:o(this,e,arguments[1])}})},9752:function(e,t,r){var n=r(3276);n(n.S,"Array",{isArray:r(8151)})},3774:function(e,t,r){"use strict";var n=r(3680),o=r(3337),i=r(9746),a=r(8670);e.exports=r(2117)(Array,"Array",(function(e,t){this._t=a(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,o(1)):o(0,"keys"==t?r:"values"==t?e[r]:[r,e[r]])}),"values"),i.Arguments=i.Array,n("keys"),n("values"),n("entries")},2336:function(e,t,r){"use strict";var n=r(3276),o=r(8670),i=[].join;n(n.P+n.F*(r(7751)!=Object||!r(4433)(i)),"Array",{join:function(e){return i.call(o(this),void 0===e?",":e)}})},2125:function(e,t,r){"use strict";var n=r(3276),o=r(8670),i=r(8591),a=r(1284),s=[].lastIndexOf,c=!!s&&1/[1].lastIndexOf(1,-0)<0;n(n.P+n.F*(c||!r(4433)(s)),"Array",{lastIndexOf:function(e){if(c)return s.apply(this,arguments)||0;var t=o(this),r=a(t.length),n=r-1;for(arguments.length>1&&(n=Math.min(n,i(arguments[1]))),n<0&&(n=r+n);n>=0;n--)if(n in t&&t[n]===e)return n||0;return-1}})},4358:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(1);n(n.P+n.F*!r(4433)([].map,!0),"Array",{map:function(e){return o(this,e,arguments[1])}})},1655:function(e,t,r){"use strict";var n=r(3276),o=r(5094);n(n.S+n.F*r(4277)((function(){function e(){}return!(Array.of.call(e)instanceof e)})),"Array",{of:function(){for(var e=0,t=arguments.length,r=new("function"==typeof this?this:Array)(t);t>e;)o(r,e,arguments[e++]);return r.length=t,r}})},9152:function(e,t,r){"use strict";var n=r(3276),o=r(9222);n(n.P+n.F*!r(4433)([].reduceRight,!0),"Array",{reduceRight:function(e){return o(this,e,arguments.length,arguments[1],!0)}})},7084:function(e,t,r){"use strict";var n=r(3276),o=r(9222);n(n.P+n.F*!r(4433)([].reduce,!0),"Array",{reduce:function(e){return o(this,e,arguments.length,arguments[1],!1)}})},7909:function(e,t,r){"use strict";var n=r(3276),o=r(9684),i=r(1647),a=r(8413),s=r(1284),c=[].slice;n(n.P+n.F*r(4277)((function(){o&&c.call(o)})),"Array",{slice:function(e,t){var r=s(this.length),n=i(this);if(t=void 0===t?r:t,"Array"==n)return c.call(this,e,t);for(var o=a(e,r),u=a(t,r),l=s(u-o),p=new Array(l),d=0;d<l;d++)p[d]="String"==n?this.charAt(o+d):this[o+d];return p}})},6167:function(e,t,r){"use strict";var n=r(3276),o=r(3828)(3);n(n.P+n.F*!r(4433)([].some,!0),"Array",{some:function(e){return o(this,e,arguments[1])}})},760:function(e,t,r){"use strict";var n=r(3276),o=r(3679),i=r(4674),a=r(4277),s=[].sort,c=[1,2,3];n(n.P+n.F*(a((function(){c.sort(void 0)}))||!a((function(){c.sort(null)}))||!r(4433)(s)),"Array",{sort:function(e){return void 0===e?s.call(i(this)):s.call(i(this),o(e))}})},7378:function(e,t,r){r(9629)("Array")},8185:function(e,t,r){var n=r(3276);n(n.S,"Date",{now:function(){return(new Date).getTime()}})},5824:function(e,t,r){var n=r(3276),o=r(5325);n(n.P+n.F*(Date.prototype.toISOString!==o),"Date",{toISOString:o})},3375:function(e,t,r){"use strict";var n=r(3276),o=r(4674),i=r(2950);n(n.P+n.F*r(4277)((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})})),"Date",{toJSON:function(e){var t=o(this),r=i(t);return"number"!=typeof r||isFinite(r)?t.toISOString():null}})},2173:function(e,t,r){var n=r(3567)("toPrimitive"),o=Date.prototype;n in o||r(6879)(o,n,r(2556))},6103:function(e,t,r){var n=Date.prototype,o="Invalid Date",i=n.toString,a=n.getTime;new Date(NaN)+""!=o&&r(5911)(n,"toString",(function(){var e=a.call(this);return e==e?i.call(this):o}))},4060:function(e,t,r){var n=r(3276);n(n.P,"Function",{bind:r(6059)})},1764:function(e,t,r){"use strict";var n=r(3506),o=r(8300),i=r(3567)("hasInstance"),a=Function.prototype;i in a||r(5395).f(a,i,{value:function(e){if("function"!=typeof this||!n(e))return!1;if(!n(this.prototype))return e instanceof this;for(;e=o(e);)if(this.prototype===e)return!0;return!1}})},5283:function(e,t,r){var n=r(5395).f,o=Function.prototype,i=/^\s*function ([^ (]*)/,a="name";a in o||r(508)&&n(o,a,{configurable:!0,get:function(){try{return(""+this).match(i)[1]}catch(e){return""}}})},7679:function(e,t,r){"use strict";var n=r(5019),o=r(1364),i="Map";e.exports=r(1107)(i,(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{get:function(e){var t=n.getEntry(o(this,i),e);return t&&t.v},set:function(e,t){return n.def(o(this,i),0===e?0:e,t)}},n,!0)},7494:function(e,t,r){var n=r(3276),o=r(5242),i=Math.sqrt,a=Math.acosh;n(n.S+n.F*!(a&&710==Math.floor(a(Number.MAX_VALUE))&&a(1/0)==1/0),"Math",{acosh:function(e){return(e=+e)<1?NaN:e>94906265.62425156?Math.log(e)+Math.LN2:o(e-1+i(e-1)*i(e+1))}})},8781:function(e,t,r){var n=r(3276),o=Math.asinh;n(n.S+n.F*!(o&&1/o(0)>0),"Math",{asinh:function e(t){return isFinite(t=+t)&&0!=t?t<0?-e(-t):Math.log(t+Math.sqrt(t*t+1)):t}})},7533:function(e,t,r){var n=r(3276),o=Math.atanh;n(n.S+n.F*!(o&&1/o(-0)<0),"Math",{atanh:function(e){return 0==(e=+e)?e:Math.log((1+e)/(1-e))/2}})},3301:function(e,t,r){var n=r(3276),o=r(5596);n(n.S,"Math",{cbrt:function(e){return o(e=+e)*Math.pow(Math.abs(e),1/3)}})},422:function(e,t,r){var n=r(3276);n(n.S,"Math",{clz32:function(e){return(e>>>=0)?31-Math.floor(Math.log(e+.5)*Math.LOG2E):32}})},9705:function(e,t,r){var n=r(3276),o=Math.exp;n(n.S,"Math",{cosh:function(e){return(o(e=+e)+o(-e))/2}})},711:function(e,t,r){var n=r(3276),o=r(2998);n(n.S+n.F*(o!=Math.expm1),"Math",{expm1:o})},6160:function(e,t,r){var n=r(3276);n(n.S,"Math",{fround:r(4619)})},5387:function(e,t,r){var n=r(3276),o=Math.abs;n(n.S,"Math",{hypot:function(e,t){for(var r,n,i=0,a=0,s=arguments.length,c=0;a<s;)c<(r=o(arguments[a++]))?(i=i*(n=c/r)*n+1,c=r):i+=r>0?(n=r/c)*n:r;return c===1/0?1/0:c*Math.sqrt(i)}})},4386:function(e,t,r){var n=r(3276),o=Math.imul;n(n.S+n.F*r(4277)((function(){return-5!=o(4294967295,5)||2!=o.length})),"Math",{imul:function(e,t){var r=65535,n=+e,o=+t,i=r&n,a=r&o;return 0|i*a+((r&n>>>16)*a+i*(r&o>>>16)<<16>>>0)}})},3568:function(e,t,r){var n=r(3276);n(n.S,"Math",{log10:function(e){return Math.log(e)*Math.LOG10E}})},6739:function(e,t,r){var n=r(3276);n(n.S,"Math",{log1p:r(5242)})},156:function(e,t,r){var n=r(3276);n(n.S,"Math",{log2:function(e){return Math.log(e)/Math.LN2}})},7022:function(e,t,r){var n=r(3276);n(n.S,"Math",{sign:r(5596)})},4132:function(e,t,r){var n=r(3276),o=r(2998),i=Math.exp;n(n.S+n.F*r(4277)((function(){return-2e-17!=!Math.sinh(-2e-17)})),"Math",{sinh:function(e){return Math.abs(e=+e)<1?(o(e)-o(-e))/2:(i(e-1)-i(-e-1))*(Math.E/2)}})},8463:function(e,t,r){var n=r(3276),o=r(2998),i=Math.exp;n(n.S,"Math",{tanh:function(e){var t=o(e=+e),r=o(-e);return t==1/0?1:r==1/0?-1:(t-r)/(i(e)+i(-e))}})},3695:function(e,t,r){var n=r(3276);n(n.S,"Math",{trunc:function(e){return(e>0?Math.floor:Math.ceil)(e)}})},6597:function(e,t,r){"use strict";var n=r(3349),o=r(4307),i=r(1647),a=r(672),s=r(2950),c=r(4277),u=r(1676).f,l=r(833).f,p=r(5395).f,d=r(2829).trim,h="Number",f=n.Number,m=f,y=f.prototype,g=i(r(1693)(y))==h,A="trim"in String.prototype,v=function(e){var t=s(e,!1);if("string"==typeof t&&t.length>2){var r,n,o,i=(t=A?t.trim():d(t,3)).charCodeAt(0);if(43===i||45===i){if(88===(r=t.charCodeAt(2))||120===r)return NaN}else if(48===i){switch(t.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+t}for(var a,c=t.slice(2),u=0,l=c.length;u<l;u++)if((a=c.charCodeAt(u))<48||a>o)return NaN;return parseInt(c,n)}}return+t};if(!f(" 0o1")||!f("0b1")||f("+0x1")){f=function(e){var t=arguments.length<1?0:e,r=this;return r instanceof f&&(g?c((function(){y.valueOf.call(r)})):i(r)!=h)?a(new m(v(t)),r,f):v(t)};for(var x,b=r(508)?u(m):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;b.length>w;w++)o(m,x=b[w])&&!o(f,x)&&p(f,x,l(m,x));f.prototype=y,y.constructor=f,r(5911)(n,h,f)}},7879:function(e,t,r){var n=r(3276);n(n.S,"Number",{EPSILON:Math.pow(2,-52)})},5878:function(e,t,r){var n=r(3276),o=r(3349).isFinite;n(n.S,"Number",{isFinite:function(e){return"number"==typeof e&&o(e)}})},2825:function(e,t,r){var n=r(3276);n(n.S,"Number",{isInteger:r(3830)})},580:function(e,t,r){var n=r(3276);n(n.S,"Number",{isNaN:function(e){return e!=e}})},475:function(e,t,r){var n=r(3276),o=r(3830),i=Math.abs;n(n.S,"Number",{isSafeInteger:function(e){return o(e)&&i(e)<=9007199254740991}})},2346:function(e,t,r){var n=r(3276);n(n.S,"Number",{MAX_SAFE_INTEGER:9007199254740991})},1877:function(e,t,r){var n=r(3276);n(n.S,"Number",{MIN_SAFE_INTEGER:-9007199254740991})},7156:function(e,t,r){var n=r(3276),o=r(3135);n(n.S+n.F*(Number.parseFloat!=o),"Number",{parseFloat:o})},6630:function(e,t,r){var n=r(3276),o=r(2134);n(n.S+n.F*(Number.parseInt!=o),"Number",{parseInt:o})},6865:function(e,t,r){"use strict";var n=r(3276),o=r(8591),i=r(5801),a=r(5067),s=1..toFixed,c=Math.floor,u=[0,0,0,0,0,0],l="Number.toFixed: incorrect invocation!",p="0",d=function(e,t){for(var r=-1,n=t;++r<6;)n+=e*u[r],u[r]=n%1e7,n=c(n/1e7)},h=function(e){for(var t=6,r=0;--t>=0;)r+=u[t],u[t]=c(r/e),r=r%e*1e7},f=function(){for(var e=6,t="";--e>=0;)if(""!==t||0===e||0!==u[e]){var r=String(u[e]);t=""===t?r:t+a.call(p,7-r.length)+r}return t},m=function e(t,r,n){return 0===r?n:r%2==1?e(t,r-1,n*t):e(t*t,r/2,n)};n(n.P+n.F*(!!s&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!r(4277)((function(){s.call({})}))),"Number",{toFixed:function(e){var t,r,n,s,c=i(this,l),u=o(e),y="",g=p;if(u<0||u>20)throw RangeError(l);if(c!=c)return"NaN";if(c<=-1e21||c>=1e21)return String(c);if(c<0&&(y="-",c=-c),c>1e-21)if(t=function(e){for(var t=0,r=e;r>=4096;)t+=12,r/=4096;for(;r>=2;)t+=1,r/=2;return t}(c*m(2,69,1))-69,r=t<0?c*m(2,-t,1):c/m(2,t,1),r*=4503599627370496,(t=52-t)>0){for(d(0,r),n=u;n>=7;)d(1e7,0),n-=7;for(d(m(10,n,1),0),n=t-1;n>=23;)h(1<<23),n-=23;h(1<<n),d(1,1),h(2),g=f()}else d(0,r),d(1<<-t,0),g=f()+a.call(p,u);return u>0?y+((s=g.length)<=u?"0."+a.call(p,u-s)+g:g.slice(0,s-u)+"."+g.slice(s-u)):y+g}})},2750:function(e,t,r){"use strict";var n=r(3276),o=r(4277),i=r(5801),a=1..toPrecision;n(n.P+n.F*(o((function(){return"1"!==a.call(1,void 0)}))||!o((function(){a.call({})}))),"Number",{toPrecision:function(e){var t=i(this,"Number#toPrecision: incorrect invocation!");return void 0===e?a.call(t):a.call(t,e)}})},5471:function(e,t,r){var n=r(3276);n(n.S+n.F,"Object",{assign:r(4236)})},3719:function(e,t,r){var n=r(3276);n(n.S,"Object",{create:r(1693)})},6462:function(e,t,r){var n=r(3276);n(n.S+n.F*!r(508),"Object",{defineProperties:r(4553)})},7279:function(e,t,r){var n=r(3276);n(n.S+n.F*!r(508),"Object",{defineProperty:r(5395).f})},4151:function(e,t,r){var n=r(3506),o=r(3797).onFreeze;r(139)("freeze",(function(e){return function(t){return e&&n(t)?e(o(t)):t}}))},5048:function(e,t,r){var n=r(8670),o=r(833).f;r(139)("getOwnPropertyDescriptor",(function(){return function(e,t){return o(n(e),t)}}))},1862:function(e,t,r){r(139)("getOwnPropertyNames",(function(){return r(645).f}))},3085:function(e,t,r){var n=r(4674),o=r(8300);r(139)("getPrototypeOf",(function(){return function(e){return o(n(e))}}))},513:function(e,t,r){var n=r(3506);r(139)("isExtensible",(function(e){return function(t){return!!n(t)&&(!e||e(t))}}))},7247:function(e,t,r){var n=r(3506);r(139)("isFrozen",(function(e){return function(t){return!n(t)||!!e&&e(t)}}))},2490:function(e,t,r){var n=r(3506);r(139)("isSealed",(function(e){return function(t){return!n(t)||!!e&&e(t)}}))},8848:function(e,t,r){var n=r(3276);n(n.S,"Object",{is:r(5681)})},8149:function(e,t,r){var n=r(4674),o=r(4190);r(139)("keys",(function(){return function(e){return o(n(e))}}))},4289:function(e,t,r){var n=r(3506),o=r(3797).onFreeze;r(139)("preventExtensions",(function(e){return function(t){return e&&n(t)?e(o(t)):t}}))},1422:function(e,t,r){var n=r(3506),o=r(3797).onFreeze;r(139)("seal",(function(e){return function(t){return e&&n(t)?e(o(t)):t}}))},7004:function(e,t,r){var n=r(3276);n(n.S,"Object",{setPrototypeOf:r(4106).set})},7678:function(e,t,r){"use strict";var n=r(9736),o={};o[r(3567)("toStringTag")]="z",o+""!="[object z]"&&r(5911)(Object.prototype,"toString",(function(){return"[object "+n(this)+"]"}),!0)},5318:function(e,t,r){var n=r(3276),o=r(3135);n(n.G+n.F*(parseFloat!=o),{parseFloat:o})},6831:function(e,t,r){var n=r(3276),o=r(2134);n(n.G+n.F*(parseInt!=o),{parseInt:o})},5164:function(e,t,r){"use strict";var n,o,i,a,s=r(5437),c=r(3349),u=r(7863),l=r(9736),p=r(3276),d=r(3506),h=r(3679),f=r(2450),m=r(8302),y=r(5168),g=r(2007).set,A=r(2739)(),v=r(8238),x=r(5243),b=r(3115),w=r(5750),P="Promise",k=c.TypeError,S=c.process,C=S&&S.versions,T=C&&C.v8||"",E=c.Promise,M="process"==l(S),O=function(){},B=o=v.f,R=!!function(){try{var e=E.resolve(1),t=(e.constructor={})[r(3567)("species")]=function(e){e(O,O)};return(M||"function"==typeof PromiseRejectionEvent)&&e.then(O)instanceof t&&0!==T.indexOf("6.6")&&-1===b.indexOf("Chrome/66")}catch(e){}}(),I=function(e){var t;return!(!d(e)||"function"!=typeof(t=e.then))&&t},D=function(e,t){if(!e._n){e._n=!0;var r=e._c;A((function(){for(var n=e._v,o=1==e._s,i=0,a=function(t){var r,i,a,s=o?t.ok:t.fail,c=t.resolve,u=t.reject,l=t.domain;try{s?(o||(2==e._h&&_(e),e._h=1),!0===s?r=n:(l&&l.enter(),r=s(n),l&&(l.exit(),a=!0)),r===t.promise?u(k("Promise-chain cycle")):(i=I(r))?i.call(r,c,u):c(r)):u(n)}catch(e){l&&!a&&l.exit(),u(e)}};r.length>i;)a(r[i++]);e._c=[],e._n=!1,t&&!e._h&&j(e)}))}},j=function(e){g.call(c,(function(){var t,r,n,o=e._v,i=L(e);if(i&&(t=x((function(){M?S.emit("unhandledRejection",o,e):(r=c.onunhandledrejection)?r({promise:e,reason:o}):(n=c.console)&&n.error&&n.error("Unhandled promise rejection",o)})),e._h=M||L(e)?2:1),e._a=void 0,i&&t.e)throw t.v}))},L=function(e){return 1!==e._h&&0===(e._a||e._c).length},_=function(e){g.call(c,(function(){var t;M?S.emit("rejectionHandled",e):(t=c.onrejectionhandled)&&t({promise:e,reason:e._v})}))},q=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),D(t,!0))},N=function e(t){var r,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw k("Promise can't be resolved itself");(r=I(t))?A((function(){var o={_w:n,_d:!1};try{r.call(t,u(e,o,1),u(q,o,1))}catch(e){q.call(o,e)}})):(n._v=t,n._s=1,D(n,!1))}catch(e){q.call({_w:n,_d:!1},e)}}};R||(E=function(e){f(this,E,P,"_h"),h(e),n.call(this);try{e(u(N,this,1),u(q,this,1))}catch(e){q.call(this,e)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=r(4112)(E.prototype,{then:function(e,t){var r=B(y(this,E));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=M?S.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&D(this,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new n;this.promise=e,this.resolve=u(N,e,1),this.reject=u(q,e,1)},v.f=B=function(e){return e===E||e===a?new i(e):o(e)}),p(p.G+p.W+p.F*!R,{Promise:E}),r(481)(E,P),r(9629)(P),a=r(7619).Promise,p(p.S+p.F*!R,P,{reject:function(e){var t=B(this);return(0,t.reject)(e),t.promise}}),p(p.S+p.F*(s||!R),P,{resolve:function(e){return w(s&&this===a?E:this,e)}}),p(p.S+p.F*!(R&&r(2569)((function(e){E.all(e).catch(O)}))),P,{all:function(e){var t=this,r=B(t),n=r.resolve,o=r.reject,i=x((function(){var r=[],i=0,a=1;m(e,!1,(function(e){var s=i++,c=!1;r.push(void 0),a++,t.resolve(e).then((function(e){c||(c=!0,r[s]=e,--a||n(r))}),o)})),--a||n(r)}));return i.e&&o(i.v),r.promise},race:function(e){var t=this,r=B(t),n=r.reject,o=x((function(){m(e,!1,(function(e){t.resolve(e).then(r.resolve,n)}))}));return o.e&&n(o.v),r.promise}})},7053:function(e,t,r){var n=r(3276),o=r(3679),i=r(8069),a=(r(3349).Reflect||{}).apply,s=Function.apply;n(n.S+n.F*!r(4277)((function(){a((function(){}))})),"Reflect",{apply:function(e,t,r){var n=o(e),c=i(r);return a?a(n,t,c):s.call(n,t,c)}})},3122:function(e,t,r){var n=r(3276),o=r(1693),i=r(3679),a=r(8069),s=r(3506),c=r(4277),u=r(6059),l=(r(3349).Reflect||{}).construct,p=c((function(){function e(){}return!(l((function(){}),[],e)instanceof e)})),d=!c((function(){l((function(){}))}));n(n.S+n.F*(p||d),"Reflect",{construct:function(e,t){i(e),a(t);var r=arguments.length<3?e:i(arguments[2]);if(d&&!p)return l(e,t,r);if(e==r){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var n=[null];return n.push.apply(n,t),new(u.apply(e,n))}var c=r.prototype,h=o(s(c)?c:Object.prototype),f=Function.apply.call(e,h,t);return s(f)?f:h}})},56:function(e,t,r){var n=r(5395),o=r(3276),i=r(8069),a=r(2950);o(o.S+o.F*r(4277)((function(){Reflect.defineProperty(n.f({},1,{value:1}),1,{value:2})})),"Reflect",{defineProperty:function(e,t,r){i(e),t=a(t,!0),i(r);try{return n.f(e,t,r),!0}catch(e){return!1}}})},2162:function(e,t,r){var n=r(3276),o=r(833).f,i=r(8069);n(n.S,"Reflect",{deleteProperty:function(e,t){var r=o(i(e),t);return!(r&&!r.configurable)&&delete e[t]}})},1078:function(e,t,r){"use strict";var n=r(3276),o=r(8069),i=function(e){this._t=o(e),this._i=0;var t,r=this._k=[];for(t in e)r.push(t)};r(4076)(i,"Object",(function(){var e,t=this,r=t._k;do{if(t._i>=r.length)return{value:void 0,done:!0}}while(!((e=r[t._i++])in t._t));return{value:e,done:!1}})),n(n.S,"Reflect",{enumerate:function(e){return new i(e)}})},9160:function(e,t,r){var n=r(833),o=r(3276),i=r(8069);o(o.S,"Reflect",{getOwnPropertyDescriptor:function(e,t){return n.f(i(e),t)}})},4082:function(e,t,r){var n=r(3276),o=r(8300),i=r(8069);n(n.S,"Reflect",{getPrototypeOf:function(e){return o(i(e))}})},8981:function(e,t,r){var n=r(833),o=r(8300),i=r(4307),a=r(3276),s=r(3506),c=r(8069);a(a.S,"Reflect",{get:function e(t,r){var a,u,l=arguments.length<3?t:arguments[2];return c(t)===l?t[r]:(a=n.f(t,r))?i(a,"value")?a.value:void 0!==a.get?a.get.call(l):void 0:s(u=o(t))?e(u,r,l):void 0}})},1723:function(e,t,r){var n=r(3276);n(n.S,"Reflect",{has:function(e,t){return t in e}})},8324:function(e,t,r){var n=r(3276),o=r(8069),i=Object.isExtensible;n(n.S,"Reflect",{isExtensible:function(e){return o(e),!i||i(e)}})},7372:function(e,t,r){var n=r(3276);n(n.S,"Reflect",{ownKeys:r(456)})},8490:function(e,t,r){var n=r(3276),o=r(8069),i=Object.preventExtensions;n(n.S,"Reflect",{preventExtensions:function(e){o(e);try{return i&&i(e),!0}catch(e){return!1}}})},4728:function(e,t,r){var n=r(3276),o=r(4106);o&&n(n.S,"Reflect",{setPrototypeOf:function(e,t){o.check(e,t);try{return o.set(e,t),!0}catch(e){return!1}}})},7454:function(e,t,r){var n=r(5395),o=r(833),i=r(8300),a=r(4307),s=r(3276),c=r(432),u=r(8069),l=r(3506);s(s.S,"Reflect",{set:function e(t,r,s){var p,d,h=arguments.length<4?t:arguments[3],f=o.f(u(t),r);if(!f){if(l(d=i(t)))return e(d,r,s,h);f=c(0)}if(a(f,"value")){if(!1===f.writable||!l(h))return!1;if(p=o.f(h,r)){if(p.get||p.set||!1===p.writable)return!1;p.value=s,n.f(h,r,p)}else n.f(h,r,c(0,s));return!0}return void 0!==f.set&&(f.set.call(h,s),!0)}})},9721:function(e,t,r){var n=r(3349),o=r(672),i=r(5395).f,a=r(1676).f,s=r(2741),c=r(5056),u=n.RegExp,l=u,p=u.prototype,d=/a/g,h=/a/g,f=new u(d)!==d;if(r(508)&&(!f||r(4277)((function(){return h[r(3567)("match")]=!1,u(d)!=d||u(h)==h||"/a/i"!=u(d,"i")})))){u=function(e,t){var r=this instanceof u,n=s(e),i=void 0===t;return!r&&n&&e.constructor===u&&i?e:o(f?new l(n&&!i?e.source:e,t):l((n=e instanceof u)?e.source:e,n&&i?c.call(e):t),r?this:p,u)};for(var m=function(e){e in u||i(u,e,{configurable:!0,get:function(){return l[e]},set:function(t){l[e]=t}})},y=a(l),g=0;y.length>g;)m(y[g++]);p.constructor=u,u.prototype=p,r(5911)(n,"RegExp",u)}r(9629)("RegExp")},5488:function(e,t,r){"use strict";var n=r(1796);r(3276)({target:"RegExp",proto:!0,forced:n!==/./.exec},{exec:n})},5743:function(e,t,r){r(508)&&"g"!=/./g.flags&&r(5395).f(RegExp.prototype,"flags",{configurable:!0,get:r(5056)})},7600:function(e,t,r){"use strict";var n=r(8069),o=r(1284),i=r(1501),a=r(3059);r(5844)("match",1,(function(e,t,r,s){return[function(r){var n=e(this),o=null==r?void 0:r[t];return void 0!==o?o.call(r,n):new RegExp(r)[t](String(n))},function(e){var t=s(r,e,this);if(t.done)return t.value;var c=n(e),u=String(this);if(!c.global)return a(c,u);var l=c.unicode;c.lastIndex=0;for(var p,d=[],h=0;null!==(p=a(c,u));){var f=String(p[0]);d[h]=f,""===f&&(c.lastIndex=i(u,o(c.lastIndex),l)),h++}return 0===h?null:d}]}))},2953:function(e,t,r){"use strict";var n=r(8069),o=r(4674),i=r(1284),a=r(8591),s=r(1501),c=r(3059),u=Math.max,l=Math.min,p=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,h=/\$([$&`']|\d\d?)/g;r(5844)("replace",2,(function(e,t,r,f){return[function(n,o){var i=e(this),a=null==n?void 0:n[t];return void 0!==a?a.call(n,i,o):r.call(String(i),n,o)},function(e,t){var o=f(r,e,this,t);if(o.done)return o.value;var p=n(e),d=String(this),h="function"==typeof t;h||(t=String(t));var y=p.global;if(y){var g=p.unicode;p.lastIndex=0}for(var A=[];;){var v=c(p,d);if(null===v)break;if(A.push(v),!y)break;""===String(v[0])&&(p.lastIndex=s(d,i(p.lastIndex),g))}for(var x,b="",w=0,P=0;P<A.length;P++){v=A[P];for(var k=String(v[0]),S=u(l(a(v.index),d.length),0),C=[],T=1;T<v.length;T++)C.push(void 0===(x=v[T])?x:String(x));var E=v.groups;if(h){var M=[k].concat(C,S,d);void 0!==E&&M.push(E);var O=String(t.apply(void 0,M))}else O=m(k,d,S,C,E,t);S>=w&&(b+=d.slice(w,S)+O,w=S+k.length)}return b+d.slice(w)}];function m(e,t,n,i,a,s){var c=n+e.length,u=i.length,l=h;return void 0!==a&&(a=o(a),l=d),r.call(s,l,(function(r,o){var s;switch(o.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,n);case"'":return t.slice(c);case"<":s=a[o.slice(1,-1)];break;default:var l=+o;if(0===l)return r;if(l>u){var d=p(l/10);return 0===d?r:d<=u?void 0===i[d-1]?o.charAt(1):i[d-1]+o.charAt(1):r}s=i[l-1]}return void 0===s?"":s}))}}))},3024:function(e,t,r){"use strict";var n=r(8069),o=r(5681),i=r(3059);r(5844)("search",1,(function(e,t,r,a){return[function(r){var n=e(this),o=null==r?void 0:r[t];return void 0!==o?o.call(r,n):new RegExp(r)[t](String(n))},function(e){var t=a(r,e,this);if(t.done)return t.value;var s=n(e),c=String(this),u=s.lastIndex;o(u,0)||(s.lastIndex=0);var l=i(s,c);return o(s.lastIndex,u)||(s.lastIndex=u),null===l?-1:l.index}]}))},7977:function(e,t,r){"use strict";var n=r(2741),o=r(8069),i=r(5168),a=r(1501),s=r(1284),c=r(3059),u=r(1796),l=r(4277),p=Math.min,d=[].push,h=4294967295,f=!l((function(){RegExp(h,"y")}));r(5844)("split",2,(function(e,t,r,l){var m;return m="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,t){var o=String(this);if(void 0===e&&0===t)return[];if(!n(e))return r.call(o,e,t);for(var i,a,s,c=[],l=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),p=0,f=void 0===t?h:t>>>0,m=new RegExp(e.source,l+"g");(i=u.call(m,o))&&!((a=m.lastIndex)>p&&(c.push(o.slice(p,i.index)),i.length>1&&i.index<o.length&&d.apply(c,i.slice(1)),s=i[0].length,p=a,c.length>=f));)m.lastIndex===i.index&&m.lastIndex++;return p===o.length?!s&&m.test("")||c.push(""):c.push(o.slice(p)),c.length>f?c.slice(0,f):c}:"0".split(void 0,0).length?function(e,t){return void 0===e&&0===t?[]:r.call(this,e,t)}:r,[function(r,n){var o=e(this),i=null==r?void 0:r[t];return void 0!==i?i.call(r,o,n):m.call(String(o),r,n)},function(e,t){var n=l(m,e,this,t,m!==r);if(n.done)return n.value;var u=o(e),d=String(this),y=i(u,RegExp),g=u.unicode,A=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(f?"y":"g"),v=new y(f?u:"^(?:"+u.source+")",A),x=void 0===t?h:t>>>0;if(0===x)return[];if(0===d.length)return null===c(v,d)?[d]:[];for(var b=0,w=0,P=[];w<d.length;){v.lastIndex=f?w:0;var k,S=c(v,f?d:d.slice(w));if(null===S||(k=p(s(v.lastIndex+(f?0:w)),d.length))===b)w=a(d,w,g);else{if(P.push(d.slice(b,w)),P.length===x)return P;for(var C=1;C<=S.length-1;C++)if(P.push(S[C]),P.length===x)return P;w=b=k}}return P.push(d.slice(b)),P}]}))},9021:function(e,t,r){"use strict";r(5743);var n=r(8069),o=r(5056),i=r(508),a="toString",s=/./.toString,c=function(e){r(5911)(RegExp.prototype,a,e,!0)};r(4277)((function(){return"/a/b"!=s.call({source:"a",flags:"b"})}))?c((function(){var e=n(this);return"/".concat(e.source,"/","flags"in e?e.flags:!i&&e instanceof RegExp?o.call(e):void 0)})):s.name!=a&&c((function(){return s.call(this)}))},900:function(e,t,r){"use strict";var n=r(5019),o=r(1364);e.exports=r(1107)("Set",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return n.def(o(this,"Set"),e=0===e?0:e,e)}},n)},1937:function(e,t,r){"use strict";r(591)("anchor",(function(e){return function(t){return e(this,"a","name",t)}}))},7613:function(e,t,r){"use strict";r(591)("big",(function(e){return function(){return e(this,"big","","")}}))},6270:function(e,t,r){"use strict";r(591)("blink",(function(e){return function(){return e(this,"blink","","")}}))},7056:function(e,t,r){"use strict";r(591)("bold",(function(e){return function(){return e(this,"b","","")}}))},6617:function(e,t,r){"use strict";var n=r(3276),o=r(1692)(!1);n(n.P,"String",{codePointAt:function(e){return o(this,e)}})},2793:function(e,t,r){"use strict";var n=r(3276),o=r(1284),i=r(2445),a="endsWith",s="".endsWith;n(n.P+n.F*r(8834)(a),"String",{endsWith:function(e){var t=i(this,e,a),r=arguments.length>1?arguments[1]:void 0,n=o(t.length),c=void 0===r?n:Math.min(o(r),n),u=String(e);return s?s.call(t,u,c):t.slice(c-u.length,c)===u}})},2307:function(e,t,r){"use strict";r(591)("fixed",(function(e){return function(){return e(this,"tt","","")}}))},3391:function(e,t,r){"use strict";r(591)("fontcolor",(function(e){return function(t){return e(this,"font","color",t)}}))},7770:function(e,t,r){"use strict";r(591)("fontsize",(function(e){return function(t){return e(this,"font","size",t)}}))},4795:function(e,t,r){var n=r(3276),o=r(8413),i=String.fromCharCode,a=String.fromCodePoint;n(n.S+n.F*(!!a&&1!=a.length),"String",{fromCodePoint:function(e){for(var t,r=[],n=arguments.length,a=0;n>a;){if(t=+arguments[a++],o(t,1114111)!==t)throw RangeError(t+" is not a valid code point");r.push(t<65536?i(t):i(55296+((t-=65536)>>10),t%1024+56320))}return r.join("")}})},688:function(e,t,r){"use strict";var n=r(3276),o=r(2445),i="includes";n(n.P+n.F*r(8834)(i),"String",{includes:function(e){return!!~o(this,e,i).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},8812:function(e,t,r){"use strict";r(591)("italics",(function(e){return function(){return e(this,"i","","")}}))},6331:function(e,t,r){"use strict";var n=r(1692)(!0);r(2117)(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=n(t,r),this._i+=e.length,{value:e,done:!1})}))},4518:function(e,t,r){"use strict";r(591)("link",(function(e){return function(t){return e(this,"a","href",t)}}))},7367:function(e,t,r){var n=r(3276),o=r(8670),i=r(1284);n(n.S,"String",{raw:function(e){for(var t=o(e.raw),r=i(t.length),n=arguments.length,a=[],s=0;r>s;)a.push(String(t[s++])),s<n&&a.push(String(arguments[s]));return a.join("")}})},7793:function(e,t,r){var n=r(3276);n(n.P,"String",{repeat:r(5067)})},5554:function(e,t,r){"use strict";r(591)("small",(function(e){return function(){return e(this,"small","","")}}))},5297:function(e,t,r){"use strict";var n=r(3276),o=r(1284),i=r(2445),a="startsWith",s="".startsWith;n(n.P+n.F*r(8834)(a),"String",{startsWith:function(e){var t=i(this,e,a),r=o(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return s?s.call(t,n,r):t.slice(r,r+n.length)===n}})},1986:function(e,t,r){"use strict";r(591)("strike",(function(e){return function(){return e(this,"strike","","")}}))},7572:function(e,t,r){"use strict";r(591)("sub",(function(e){return function(){return e(this,"sub","","")}}))},3844:function(e,t,r){"use strict";r(591)("sup",(function(e){return function(){return e(this,"sup","","")}}))},3184:function(e,t,r){"use strict";r(2829)("trim",(function(e){return function(){return e(this,3)}}))},2353:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=r(3349),i=r(4307),a=r(508),s=r(3276),c=r(5911),u=r(3797).KEY,l=r(4277),p=r(1375),d=r(481),h=r(7398),f=r(3567),m=r(185),y=r(5490),g=r(2337),A=r(8151),v=r(8069),x=r(3506),b=r(4674),w=r(8670),P=r(2950),k=r(432),S=r(1693),C=r(645),T=r(833),E=r(4128),M=r(5395),O=r(4190),B=T.f,R=M.f,I=C.f,D=o.Symbol,j=o.JSON,L=j&&j.stringify,_=f("_hidden"),q=f("toPrimitive"),N={}.propertyIsEnumerable,F=p("symbol-registry"),U=p("symbols"),K=p("op-symbols"),G=Object.prototype,H="function"==typeof D&&!!E.f,z=o.QObject,W=!z||!z.prototype||!z.prototype.findChild,V=a&&l((function(){return 7!=S(R({},"a",{get:function(){return R(this,"a",{value:7}).a}})).a}))?function(e,t,r){var n=B(G,t);n&&delete G[t],R(e,t,r),n&&e!==G&&R(G,t,n)}:R,Q=function(e){var t=U[e]=S(D.prototype);return t._k=e,t},Y=H&&"symbol"==n(D.iterator)?function(e){return"symbol"==n(e)}:function(e){return e instanceof D},X=function(e,t,r){return e===G&&X(K,t,r),v(e),t=P(t,!0),v(r),i(U,t)?(r.enumerable?(i(e,_)&&e[_][t]&&(e[_][t]=!1),r=S(r,{enumerable:k(0,!1)})):(i(e,_)||R(e,_,k(1,{})),e[_][t]=!0),V(e,t,r)):R(e,t,r)},$=function(e,t){v(e);for(var r,n=g(t=w(t)),o=0,i=n.length;i>o;)X(e,r=n[o++],t[r]);return e},J=function(e){var t=N.call(this,e=P(e,!0));return!(this===G&&i(U,e)&&!i(K,e))&&(!(t||!i(this,e)||!i(U,e)||i(this,_)&&this[_][e])||t)},Z=function(e,t){if(e=w(e),t=P(t,!0),e!==G||!i(U,t)||i(K,t)){var r=B(e,t);return!r||!i(U,t)||i(e,_)&&e[_][t]||(r.enumerable=!0),r}},ee=function(e){for(var t,r=I(w(e)),n=[],o=0;r.length>o;)i(U,t=r[o++])||t==_||t==u||n.push(t);return n},te=function(e){for(var t,r=e===G,n=I(r?K:w(e)),o=[],a=0;n.length>a;)!i(U,t=n[a++])||r&&!i(G,t)||o.push(U[t]);return o};H||(D=function(){if(this instanceof D)throw TypeError("Symbol is not a constructor!");var e=h(arguments.length>0?arguments[0]:void 0),t=function t(r){this===G&&t.call(K,r),i(this,_)&&i(this[_],e)&&(this[_][e]=!1),V(this,e,k(1,r))};return a&&W&&V(G,e,{configurable:!0,set:t}),Q(e)},c(D.prototype,"toString",(function(){return this._k})),T.f=Z,M.f=X,r(1676).f=C.f=ee,r(2224).f=J,E.f=te,a&&!r(5437)&&c(G,"propertyIsEnumerable",J,!0),m.f=function(e){return Q(f(e))}),s(s.G+s.W+s.F*!H,{Symbol:D});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;re.length>ne;)f(re[ne++]);for(var oe=O(f.store),ie=0;oe.length>ie;)y(oe[ie++]);s(s.S+s.F*!H,"Symbol",{for:function(e){return i(F,e+="")?F[e]:F[e]=D(e)},keyFor:function(e){if(!Y(e))throw TypeError(e+" is not a symbol!");for(var t in F)if(F[t]===e)return t},useSetter:function(){W=!0},useSimple:function(){W=!1}}),s(s.S+s.F*!H,"Object",{create:function(e,t){return void 0===t?S(e):$(S(e),t)},defineProperty:X,defineProperties:$,getOwnPropertyDescriptor:Z,getOwnPropertyNames:ee,getOwnPropertySymbols:te});var ae=l((function(){E.f(1)}));s(s.S+s.F*ae,"Object",{getOwnPropertySymbols:function(e){return E.f(b(e))}}),j&&s(s.S+s.F*(!H||l((function(){var e=D();return"[null]"!=L([e])||"{}"!=L({a:e})||"{}"!=L(Object(e))}))),"JSON",{stringify:function(e){for(var t,r,n=[e],o=1;arguments.length>o;)n.push(arguments[o++]);if(r=t=n[1],(x(t)||void 0!==e)&&!Y(e))return A(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!Y(t))return t}),n[1]=t,L.apply(j,n)}}),D.prototype[q]||r(6879)(D.prototype,q,D.prototype.valueOf),d(D,"Symbol"),d(Math,"Math",!0),d(o.JSON,"JSON",!0)},9511:function(e,t,r){"use strict";var n=r(3276),o=r(6303),i=r(2428),a=r(8069),s=r(8413),c=r(1284),u=r(3506),l=r(3349).ArrayBuffer,p=r(5168),d=i.ArrayBuffer,h=i.DataView,f=o.ABV&&l.isView,m=d.prototype.slice,y=o.VIEW,g="ArrayBuffer";n(n.G+n.W+n.F*(l!==d),{ArrayBuffer:d}),n(n.S+n.F*!o.CONSTR,g,{isView:function(e){return f&&f(e)||u(e)&&y in e}}),n(n.P+n.U+n.F*r(4277)((function(){return!new d(2).slice(1,void 0).byteLength})),g,{slice:function(e,t){if(void 0!==m&&void 0===t)return m.call(a(this),e);for(var r=a(this).byteLength,n=s(e,r),o=s(void 0===t?r:t,r),i=new(p(this,d))(c(o-n)),u=new h(this),l=new h(i),f=0;n<o;)l.setUint8(f++,u.getUint8(n++));return i}}),r(9629)(g)},6708:function(e,t,r){var n=r(3276);n(n.G+n.W+n.F*!r(6303).ABV,{DataView:r(2428).DataView})},925:function(e,t,r){r(6500)("Float32",4,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},7865:function(e,t,r){r(6500)("Float64",8,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},8095:function(e,t,r){r(6500)("Int16",2,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},5187:function(e,t,r){r(6500)("Int32",4,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},3443:function(e,t,r){r(6500)("Int8",1,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},5977:function(e,t,r){r(6500)("Uint16",2,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},7444:function(e,t,r){r(6500)("Uint32",4,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},5558:function(e,t,r){r(6500)("Uint8",1,(function(e){return function(t,r,n){return e(this,t,r,n)}}))},9159:function(e,t,r){r(6500)("Uint8",1,(function(e){return function(t,r,n){return e(this,t,r,n)}}),!0)},3313:function(e,t,r){"use strict";var n,o=r(3349),i=r(3828)(0),a=r(5911),s=r(3797),c=r(4236),u=r(8888),l=r(3506),p=r(1364),d=r(1364),h=!o.ActiveXObject&&"ActiveXObject"in o,f="WeakMap",m=s.getWeak,y=Object.isExtensible,g=u.ufstore,A=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},v={get:function(e){if(l(e)){var t=m(e);return!0===t?g(p(this,f)).get(e):t?t[this._i]:void 0}},set:function(e,t){return u.def(p(this,f),e,t)}},x=e.exports=r(1107)(f,A,v,u,!0,!0);d&&h&&(c((n=u.getConstructor(A,f)).prototype,v),s.NEED=!0,i(["delete","has","get","set"],(function(e){var t=x.prototype,r=t[e];a(t,e,(function(t,o){if(l(t)&&!y(t)){this._f||(this._f=new n);var i=this._f[e](t,o);return"set"==e?this:i}return r.call(this,t,o)}))})))},959:function(e,t,r){"use strict";var n=r(8888),o=r(1364),i="WeakSet";r(1107)(i,(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return n.def(o(this,i),e,!0)}},n,!1,!0)},8465:function(e,t,r){"use strict";var n=r(3276),o=r(1835),i=r(4674),a=r(1284),s=r(3679),c=r(5026);n(n.P,"Array",{flatMap:function(e){var t,r,n=i(this);return s(e),t=a(n.length),r=c(n,0),o(r,n,n,t,0,1,e,arguments[1]),r}}),r(3680)("flatMap")},1248:function(e,t,r){"use strict";var n=r(3276),o=r(6329)(!0);n(n.P,"Array",{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),r(3680)("includes")},847:function(e,t,r){var n=r(3276),o=r(4399)(!0);n(n.S,"Object",{entries:function(e){return o(e)}})},3947:function(e,t,r){var n=r(3276),o=r(456),i=r(8670),a=r(833),s=r(5094);n(n.S,"Object",{getOwnPropertyDescriptors:function(e){for(var t,r,n=i(e),c=a.f,u=o(n),l={},p=0;u.length>p;)void 0!==(r=c(n,t=u[p++]))&&s(l,t,r);return l}})},5378:function(e,t,r){var n=r(3276),o=r(4399)(!1);n(n.S,"Object",{values:function(e){return o(e)}})},6002:function(e,t,r){"use strict";var n=r(3276),o=r(7619),i=r(3349),a=r(5168),s=r(5750);n(n.P+n.R,"Promise",{finally:function(e){var t=a(this,o.Promise||i.Promise),r="function"==typeof e;return this.then(r?function(r){return s(t,e()).then((function(){return r}))}:e,r?function(r){return s(t,e()).then((function(){throw r}))}:e)}})},4234:function(e,t,r){"use strict";var n=r(3276),o=r(2713),i=r(3115),a=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(i);n(n.P+n.F*a,"String",{padEnd:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0,!1)}})},7087:function(e,t,r){"use strict";var n=r(3276),o=r(2713),i=r(3115),a=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(i);n(n.P+n.F*a,"String",{padStart:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0,!0)}})},4931:function(e,t,r){"use strict";r(2829)("trimLeft",(function(e){return function(){return e(this,1)}}),"trimStart")},4834:function(e,t,r){"use strict";r(2829)("trimRight",(function(e){return function(){return e(this,2)}}),"trimEnd")},6177:function(e,t,r){r(5490)("asyncIterator")},7986:function(e,t,r){for(var n=r(3774),o=r(4190),i=r(5911),a=r(3349),s=r(6879),c=r(9746),u=r(3567),l=u("iterator"),p=u("toStringTag"),d=c.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},f=o(h),m=0;m<f.length;m++){var y,g=f[m],A=h[g],v=a[g],x=v&&v.prototype;if(x&&(x[l]||s(x,l,d),x[p]||s(x,p,g),c[g]=d,A))for(y in n)x[y]||i(x,y,n[y],!0)}},5908:function(e,t,r){var n=r(3276),o=r(2007);n(n.G+n.B,{setImmediate:o.set,clearImmediate:o.clear})},16:function(e,t,r){var n=r(3349),o=r(3276),i=r(3115),a=[].slice,s=/MSIE .\./.test(i),c=function(e){return function(t,r){var n=arguments.length>2,o=!!n&&a.call(arguments,2);return e(n?function(){("function"==typeof t?t:Function(t)).apply(this,o)}:t,r)}};o(o.G+o.B+o.F*s,{setTimeout:c(n.setTimeout),setInterval:c(n.setInterval)})},70:function(e,t,r){r(16),r(5908),r(7986),e.exports=r(7619)},6028:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var o=function(e){"use strict";var t,r=Object.prototype,o=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var o=t&&t.prototype instanceof g?t:g,i=Object.create(o.prototype),a=new M(n||[]);return i._invoke=function(e,t,r){var n=d;return function(o,i){if(n===f)throw new Error("Generator is already running");if(n===m){if("throw"===o)throw i;return B()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var s=C(a,r);if(s){if(s===y)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===d)throw n=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=f;var c=p(e,t,r);if("normal"===c.type){if(n=r.done?m:h,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n=m,r.method="throw",r.arg=c.arg)}}}(e,r,a),i}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var d="suspendedStart",h="suspendedYield",f="executing",m="completed",y={};function g(){}function A(){}function v(){}var x={};u(x,a,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(O([])));w&&w!==r&&o.call(w,a)&&(x=w);var P=v.prototype=g.prototype=Object.create(x);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function r(i,a,s,c){var u=p(e[i],e,a);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"===n(d)&&o.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(d).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,c)}))}c(u.arg)}var i;this._invoke=function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}}function C(e,r){var n=e.iterator[r.method];if(n===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method))return y;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var o=p(n,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function O(e){if(e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function r(){for(;++n<e.length;)if(o.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}return{next:B}}function B(){return{value:t,done:!0}}return A.prototype=v,u(P,"constructor",v),u(v,"constructor",A),A.displayName=u(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===A||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,u(e,c,"GeneratorFunction")),e.prototype=Object.create(P),e},e.awrap=function(e){return{__await:e}},k(S.prototype),u(S.prototype,s,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(P),u(P,c,"Generator"),u(P,a,(function(){return this})),u(P,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=O,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),u=o.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}("object"===n(e=r.nmd(e))?e.exports:{});try{regeneratorRuntime=o}catch(e){"object"===("undefined"==typeof globalThis?"undefined":n(globalThis))?globalThis.regeneratorRuntime=o:Function("r","regeneratorRuntime = r")(o)}},9239:function(e,t,r){var n,o;function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}e=r.nmd(e),function(a,s){"use strict";"object"===i(e)&&e.exports?e.exports=s():void 0===(o="function"==typeof(n=s)?n.call(t,r,t,e):n)||(e.exports=o)}(0,(function(e){"use strict";var t=e&&e.IPv6;return{best:function(e){var t,r,n=e.toLowerCase().split(":"),o=n.length,i=8;for(""===n[0]&&""===n[1]&&""===n[2]?(n.shift(),n.shift()):""===n[0]&&""===n[1]?n.shift():""===n[o-1]&&""===n[o-2]&&n.pop(),-1!==n[(o=n.length)-1].indexOf(".")&&(i=7),t=0;t<o&&""!==n[t];t++);if(t<i)for(n.splice(t,1,"0000");n.length<i;)n.splice(t,0,"0000");for(var a=0;a<i;a++){r=n[a].split("");for(var s=0;s<3&&"0"===r[0]&&r.length>1;s++)r.splice(0,1);n[a]=r.join("")}var c=-1,u=0,l=0,p=-1,d=!1;for(a=0;a<i;a++)d?"0"===n[a]?l+=1:(d=!1,l>u&&(c=p,u=l)):"0"===n[a]&&(d=!0,p=a,l=1);l>u&&(c=p,u=l),u>1&&n.splice(c,u,""),o=n.length;var h="";for(""===n[0]&&(h=":"),a=0;a<o&&(h+=n[a],a!==o-1);a++)h+=":";return""===n[o-1]&&(h+=":"),h},noConflict:function(){return e.IPv6===this&&(e.IPv6=t),this}}}))},4035:function(e,t,r){var n,o;function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}e=r.nmd(e),function(a,s){"use strict";"object"===i(e)&&e.exports?e.exports=s():void 0===(o="function"==typeof(n=s)?n.call(t,r,t,e):n)||(e.exports=o)}(0,(function(e){"use strict";var t=e&&e.SecondLevelDomains,r={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;var n=e.lastIndexOf(".",t-1);if(n<=0||n>=t-1)return!1;var o=r.list[e.slice(t+1)];return!!o&&o.indexOf(" "+e.slice(n+1,t)+" ")>=0},is:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;if(e.lastIndexOf(".",t-1)>=0)return!1;var n=r.list[e.slice(t+1)];return!!n&&n.indexOf(" "+e.slice(0,t)+" ")>=0},get:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return null;var n=e.lastIndexOf(".",t-1);if(n<=0||n>=t-1)return null;var o=r.list[e.slice(t+1)];return o?o.indexOf(" "+e.slice(n+1,t)+" ")<0?null:e.slice(n+1):null},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return r}))},5016:function(e,t,r){var n,o,i;function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}e=r.nmd(e),function(s,c){"use strict";"object"===a(e)&&e.exports?e.exports=c(r(2456),r(9239),r(4035)):(o=[r(2456),r(9239),r(4035)],void 0===(i="function"==typeof(n=c)?n.apply(t,o):n)||(e.exports=i))}(0,(function(e,t,r,n){"use strict";var o=n&&n.URI;function i(e,t){var r=arguments.length>=1,n=arguments.length>=2;if(!(this instanceof i))return r?n?new i(e,t):new i(e):new i;if(void 0===e){if(r)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}if(null===e&&r)throw new TypeError("null is not a valid argument for URI");return this.href(e),void 0!==t?this.absoluteTo(t):this}i.version="1.19.10";var s=i.prototype,c=Object.prototype.hasOwnProperty;function u(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function p(e){return"Array"===l(e)}function d(e,t){var r,n,o={};if("RegExp"===l(t))o=null;else if(p(t))for(r=0,n=t.length;r<n;r++)o[t[r]]=!0;else o[t]=!0;for(r=0,n=e.length;r<n;r++)(o&&void 0!==o[e[r]]||!o&&t.test(e[r]))&&(e.splice(r,1),n--,r--);return e}function h(e,t){var r,n;if(p(t)){for(r=0,n=t.length;r<n;r++)if(!h(e,t[r]))return!1;return!0}var o=l(t);for(r=0,n=e.length;r<n;r++)if("RegExp"===o){if("string"==typeof e[r]&&e[r].match(t))return!0}else if(e[r]===t)return!0;return!1}function f(e,t){if(!p(e)||!p(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}function m(e){return e.replace(/^\/+|\/+$/g,"")}function y(e){return escape(e)}function g(e){return encodeURIComponent(e).replace(/[!'()*]/g,y).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return i.domAttributes[t]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(e,t){var r=i.encode(e+"");return void 0===t&&(t=i.escapeQuerySpace),t?r.replace(/%20/g,"+"):r},i.decodeQuery=function(e,t){e+="",void 0===t&&(t=i.escapeQuerySpace);try{return i.decode(t?e.replace(/\+/g,"%20"):e)}catch(t){return e}};var A,v={encode:"encode",decode:"decode"},x=function(e,t){return function(r){try{return i[t](r+"").replace(i.characters[e][t].expression,(function(r){return i.characters[e][t].map[r]}))}catch(e){return r}}};for(A in v)i[A+"PathSegment"]=x("pathname",v[A]),i[A+"UrnPathSegment"]=x("urnpath",v[A]);var b=function(e,t,r){return function(n){var o;o=r?function(e){return i[t](i[r](e))}:i[t];for(var a=(n+"").split(e),s=0,c=a.length;s<c;s++)a[s]=o(a[s]);return a.join(e)}};function w(e){return function(t,r){return void 0===t?this._parts[e]||"":(this._parts[e]=t||null,this.build(!r),this)}}function P(e,t){return function(r,n){return void 0===r?this._parts[e]||"":(null!==r&&(r+="").charAt(0)===t&&(r=r.substring(1)),this._parts[e]=r,this.build(!n),this)}}i.decodePath=b("/","decodePathSegment"),i.decodeUrnPath=b(":","decodeUrnPathSegment"),i.recodePath=b("/","encodePathSegment","decode"),i.recodeUrnPath=b(":","encodeUrnPathSegment","decode"),i.encodeReserved=x("reserved","encode"),i.parse=function(e,t){var r;return t||(t={preventInvalidHostname:i.preventInvalidHostname}),(r=(e=e.replace(i.leading_whitespace_expression,"")).indexOf("#"))>-1&&(t.fragment=e.substring(r+1)||null,e=e.substring(0,r)),(r=e.indexOf("?"))>-1&&(t.query=e.substring(r+1)||null,e=e.substring(0,r)),"//"===(e=e.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).substring(0,2)?(t.protocol=null,e=e.substring(2),e=i.parseAuthority(e,t)):(r=e.indexOf(":"))>-1&&(t.protocol=e.substring(0,r)||null,t.protocol&&!t.protocol.match(i.protocol_expression)?t.protocol=void 0:"//"===e.substring(r+1,r+3).replace(/\\/g,"/")?(e=e.substring(r+3),e=i.parseAuthority(e,t)):(e=e.substring(r+1),t.urn=!0)),t.path=e,t},i.parseHost=function(e,t){e||(e="");var r,n,o=(e=e.replace(/\\/g,"/")).indexOf("/");if(-1===o&&(o=e.length),"["===e.charAt(0))r=e.indexOf("]"),t.hostname=e.substring(1,r)||null,t.port=e.substring(r+2,o)||null,"/"===t.port&&(t.port=null);else{var a=e.indexOf(":"),s=e.indexOf("/"),c=e.indexOf(":",a+1);-1!==c&&(-1===s||c<s)?(t.hostname=e.substring(0,o)||null,t.port=null):(n=e.substring(0,o).split(":"),t.hostname=n[0]||null,t.port=n[1]||null)}return t.hostname&&"/"!==e.substring(o).charAt(0)&&(o++,e="/"+e),t.preventInvalidHostname&&i.ensureValidHostname(t.hostname,t.protocol),t.port&&i.ensureValidPort(t.port),e.substring(o)||"/"},i.parseAuthority=function(e,t){return e=i.parseUserinfo(e,t),i.parseHost(e,t)},i.parseUserinfo=function(e,t){var r=e;-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/"));var n,o=e.indexOf("/"),a=e.lastIndexOf("@",o>-1?o:e.length-1);return a>-1&&(-1===o||a<o)?(n=e.substring(0,a).split(":"),t.username=n[0]?i.decode(n[0]):null,n.shift(),t.password=n[0]?i.decode(n.join(":")):null,e=r.substring(a+1)):(t.username=null,t.password=null),e},i.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,n,o,a={},s=e.split("&"),u=s.length,l=0;l<u;l++)r=s[l].split("="),n=i.decodeQuery(r.shift(),t),o=r.length?i.decodeQuery(r.join("="),t):null,"__proto__"!==n&&(c.call(a,n)?("string"!=typeof a[n]&&null!==a[n]||(a[n]=[a[n]]),a[n].push(o)):a[n]=o);return a},i.build=function(e){var t="",r=!1;return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//",r=!0),t+=i.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&r&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},i.buildHost=function(e){var t="";return e.hostname?(i.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},i.buildAuthority=function(e){return i.buildUserinfo(e)+i.buildHost(e)},i.buildUserinfo=function(e){var t="";return e.username&&(t+=i.encode(e.username)),e.password&&(t+=":"+i.encode(e.password)),t&&(t+="@"),t},i.buildQuery=function(e,t,r){var n,o,a,s,u="";for(o in e)if("__proto__"!==o&&c.call(e,o))if(p(e[o]))for(n={},a=0,s=e[o].length;a<s;a++)void 0!==e[o][a]&&void 0===n[e[o][a]+""]&&(u+="&"+i.buildQueryParameter(o,e[o][a],r),!0!==t&&(n[e[o][a]+""]=!0));else void 0!==e[o]&&(u+="&"+i.buildQueryParameter(o,e[o],r));return u.substring(1)},i.buildQueryParameter=function(e,t,r){return i.encodeQuery(e,r)+(null!==t?"="+i.encodeQuery(t,r):"")},i.addQuery=function(e,t,r){if("object"===a(t))for(var n in t)c.call(t,n)&&i.addQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=r);"string"==typeof e[t]&&(e[t]=[e[t]]),p(r)||(r=[r]),e[t]=(e[t]||[]).concat(r)}},i.setQuery=function(e,t,r){if("object"===a(t))for(var n in t)c.call(t,n)&&i.setQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");e[t]=void 0===r?null:r}},i.removeQuery=function(e,t,r){var n,o,s;if(p(t))for(n=0,o=t.length;n<o;n++)e[t[n]]=void 0;else if("RegExp"===l(t))for(s in e)t.test(s)&&(e[s]=void 0);else if("object"===a(t))for(s in t)c.call(t,s)&&i.removeQuery(e,s,t[s]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===l(r)?!p(e[t])&&r.test(e[t])?e[t]=void 0:e[t]=d(e[t],r):e[t]!==String(r)||p(r)&&1!==r.length?p(e[t])&&(e[t]=d(e[t],r)):e[t]=void 0:e[t]=void 0}},i.hasQuery=function(e,t,r,n){switch(l(t)){case"String":break;case"RegExp":for(var o in e)if(c.call(e,o)&&t.test(o)&&(void 0===r||i.hasQuery(e,o,r)))return!0;return!1;case"Object":for(var a in t)if(c.call(t,a)&&!i.hasQuery(e,a,t[a]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(r)){case"Undefined":return t in e;case"Boolean":return r===Boolean(p(e[t])?e[t].length:e[t]);case"Function":return!!r(e[t],t,e);case"Array":return!!p(e[t])&&(n?h:f)(e[t],r);case"RegExp":return p(e[t])?!!n&&h(e[t],r):Boolean(e[t]&&e[t].match(r));case"Number":r=String(r);case"String":return p(e[t])?!!n&&h(e[t],r):e[t]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var e=[],t=[],r=0,n=0;n<arguments.length;n++){var o=new i(arguments[n]);e.push(o);for(var a=o.segment(),s=0;s<a.length;s++)"string"==typeof a[s]&&t.push(a[s]),a[s]&&r++}if(!t.length||!r)return new i("");var c=new i("").segment(t);return""!==e[0].path()&&"/"!==e[0].path().slice(0,1)||c.path("/"+c.path()),c.normalize()},i.commonPath=function(e,t){var r,n=Math.min(e.length,t.length);for(r=0;r<n;r++)if(e.charAt(r)!==t.charAt(r)){r--;break}return r<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(r)&&"/"===t.charAt(r)||(r=e.substring(0,r).lastIndexOf("/")),e.substring(0,r+1))},i.withinString=function(e,t,r){r||(r={});var n=r.start||i.findUri.start,o=r.end||i.findUri.end,a=r.trim||i.findUri.trim,s=r.parens||i.findUri.parens,c=/[a-z0-9-]=["']?$/i;for(n.lastIndex=0;;){var u=n.exec(e);if(!u)break;var l=u.index;if(r.ignoreHtml){var p=e.slice(Math.max(l-3,0),l);if(p&&c.test(p))continue}for(var d=l+e.slice(l).search(o),h=e.slice(l,d),f=-1;;){var m=s.exec(h);if(!m)break;var y=m.index+m[0].length;f=Math.max(f,y)}if(!((h=f>-1?h.slice(0,f)+h.slice(f).replace(a,""):h.replace(a,"")).length<=u[0].length||r.ignore&&r.ignore.test(h))){var g=t(h,l,d=l+h.length,e);void 0!==g?(g=String(g),e=e.slice(0,l)+g+e.slice(d),n.lastIndex=l+g.length):n.lastIndex=d}}return n.lastIndex=0,e},i.ensureValidHostname=function(t,r){var n=!!t,o=!1;if(!!r&&(o=h(i.hostProtocols,r)),o&&!n)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(t&&t.match(i.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(e){if(e){var t=Number(e);if(!(/^[0-9]+$/.test(t)&&t>0&&t<65536))throw new TypeError('Port "'+e+'" is not a valid port')}},i.noConflict=function(e){if(e){var t={URI:this.noConflict()};return n.URITemplate&&"function"==typeof n.URITemplate.noConflict&&(t.URITemplate=n.URITemplate.noConflict()),n.IPv6&&"function"==typeof n.IPv6.noConflict&&(t.IPv6=n.IPv6.noConflict()),n.SecondLevelDomains&&"function"==typeof n.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=n.SecondLevelDomains.noConflict()),t}return n.URI===this&&(n.URI=o),this},s.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=w("protocol"),s.username=w("username"),s.password=w("password"),s.hostname=w("hostname"),s.port=w("port"),s.query=P("query","?"),s.fragment=P("fragment","#"),s.search=function(e,t){var r=this.query(e,t);return"string"==typeof r&&r.length?"?"+r:r},s.hash=function(e,t){var r=this.fragment(e,t);return"string"==typeof r&&r.length?"#"+r:r},s.pathname=function(e,t){if(void 0===e||!0===e){var r=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?i.decodeUrnPath:i.decodePath)(r):r}return this._parts.urn?this._parts.path=e?i.recodeUrnPath(e):"":this._parts.path=e?i.recodePath(e):"/",this.build(!t),this},s.path=s.pathname,s.href=function(e,t){var r;if(void 0===e)return this.toString();this._string="",this._parts=i._parts();var n=e instanceof i,o="object"===a(e)&&(e.hostname||e.path||e.pathname);if(e.nodeName&&(e=e[i.getDomAttribute(e)]||"",o=!1),!n&&o&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=i.parse(String(e),this._parts);else{if(!n&&!o)throw new TypeError("invalid input");var s=n?e._parts:e;for(r in s)"query"!==r&&c.call(this._parts,r)&&(this._parts[r]=s[r]);s.query&&this.query(s.query,!1)}return this.build(!t),this},s.is=function(e){var t=!1,n=!1,o=!1,a=!1,s=!1,c=!1,u=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,n=i.ip4_expression.test(this._parts.hostname),o=i.ip6_expression.test(this._parts.hostname),s=(a=!(t=n||o))&&r&&r.has(this._parts.hostname),c=a&&i.idn_expression.test(this._parts.hostname),u=a&&i.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return a;case"sld":return s;case"ip":return t;case"ip4":case"ipv4":case"inet4":return n;case"ip6":case"ipv6":case"inet6":return o;case"idn":return c;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return u}return null};var k=s.protocol,S=s.port,C=s.hostname;s.protocol=function(e,t){if(e&&!(e=e.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return k.call(this,e,t)},s.scheme=s.protocol,s.port=function(e,t){return this._parts.urn?void 0===e?"":this:(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),i.ensureValidPort(e))),S.call(this,e,t))},s.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(e,r))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');e=r.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(e,this._parts.protocol)}return C.call(this,e,t)},s.origin=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var n=i(e);return this.protocol(n.protocol()).authority(n.authority()).build(!t),this},s.host=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.authority=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=i.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==e[e.length-1]&&(e+="@"),i.parseUserinfo(e,this._parts),this.build(!t),this},s.resource=function(e,t){var r;return void 0===e?this.path()+this.search()+this.hash():(r=i.parse(e),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!t),this)},s.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var n=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,n),a=new RegExp("^"+u(o));if(e&&"."!==e.charAt(e.length-1)&&(e+="."),-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");return e&&i.ensureValidHostname(e,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(a,e),this.build(!t),this},s.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var n=this._parts.hostname.length-this.tld(t).length-1;return n=this._parts.hostname.lastIndexOf(".",n-1)+1,this._parts.hostname.substring(n)||""}if(!e)throw new TypeError("cannot set domain empty");if(-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(e,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var o=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},s.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(n+1);return!0!==t&&r&&r.list[o.toLowerCase()]&&r.get(this._parts.hostname)||o}var i;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}return this.build(!t),this},s.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,n=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return e?i.decodePath(n):n}var o=this._parts.path.length-this.filename().length,a=this._parts.path.substring(0,o),s=new RegExp("^"+u(a));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=i.recodePath(e),this._parts.path=this._parts.path.replace(s,e),this.build(!t),this},s.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("string"!=typeof e){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),n=this._parts.path.substring(r+1);return e?i.decodePathSegment(n):n}var o=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(o=!0);var a=new RegExp(u(this.filename())+"$");return e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e),o?this.normalizePath(t):this.build(!t),this},s.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var r,n,o=this.filename(),a=o.lastIndexOf(".");return-1===a?"":(r=o.substring(a+1),n=/^[a-z0-9%]+$/i.test(r)?r:"",e?i.decodePathSegment(n):n)}"."===e.charAt(0)&&(e=e.substring(1));var s,c=this.suffix();if(c)s=e?new RegExp(u(c)+"$"):new RegExp(u("."+c)+"$");else{if(!e)return this;this._parts.path+="."+i.recodePath(e)}return s&&(e=i.recodePath(e),this._parts.path=this._parts.path.replace(s,e)),this.build(!t),this},s.segment=function(e,t,r){var n=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),a=o.split(n);if(void 0!==e&&"number"!=typeof e&&(r=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(i&&a.shift(),e<0&&(e=Math.max(a.length+e,0)),void 0===t)return void 0===e?a:a[e];if(null===e||void 0===a[e])if(p(t)){a=[];for(var s=0,c=t.length;s<c;s++)(t[s].length||a.length&&a[a.length-1].length)&&(a.length&&!a[a.length-1].length&&a.pop(),a.push(m(t[s])))}else(t||"string"==typeof t)&&(t=m(t),""===a[a.length-1]?a[a.length-1]=t:a.push(t));else t?a[e]=m(t):a.splice(e,1);return i&&a.unshift(""),this.path(a.join(n),r)},s.segmentCoded=function(e,t,r){var n,o,a;if("number"!=typeof e&&(r=t,t=e,e=void 0),void 0===t){if(p(n=this.segment(e,t,r)))for(o=0,a=n.length;o<a;o++)n[o]=i.decode(n[o]);else n=void 0!==n?i.decode(n):void 0;return n}if(p(t))for(o=0,a=t.length;o<a;o++)t[o]=i.encode(t[o]);else t="string"==typeof t||t instanceof String?i.encode(t):t;return this.segment(e,t,r)};var T=s.query;return s.query=function(e,t){if(!0===e)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof e){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),n=e.call(this,r);return this._parts.query=i.buildQuery(n||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this}return void 0!==e&&"string"!=typeof e?(this._parts.query=i.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):T.call(this,e,t)},s.setQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)n[e]=void 0!==t?t:null;else{if("object"!==a(e))throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in e)c.call(e,o)&&(n[o]=e[o])}return this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.addQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(n,e,void 0===t?null:t),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.removeQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(n,e,t),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.hasQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(n,e,t,r)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},s.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},s.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},s.normalizePath=function(e){var t,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var n,o,a="";for("/"!==(r=i.recodePath(r)).charAt(0)&&(t=!0,r="/"+r),"/.."!==r.slice(-3)&&"/."!==r.slice(-2)||(r+="/"),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),t&&(a=r.substring(1).match(/^(\.\.\/)+/)||"")&&(a=a[0]);-1!==(n=r.search(/\/\.\.(\/|$)/));)0!==n?(-1===(o=r.substring(0,n).lastIndexOf("/"))&&(o=n),r=r.substring(0,o)+r.substring(n+3)):r=r.substring(3);return t&&this.is("relative")&&(r=a+r.substring(1)),this._parts.path=r,this.build(!e),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},s.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var e=i.encode,t=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.unicode=function(){var e=i.encode,t=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.readable=function(){var t=this.clone();t.username("").password("").normalize();var r="";if(t._parts.protocol&&(r+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(r+=e.toUnicode(t._parts.hostname),t._parts.port&&(r+=":"+t._parts.port)):r+=t.host()),t._parts.hostname&&t._parts.path&&"/"!==t._parts.path.charAt(0)&&(r+="/"),r+=t.path(!0),t._parts.query){for(var n="",o=0,a=t._parts.query.split("&"),s=a.length;o<s;o++){var c=(a[o]||"").split("=");n+="&"+i.decodeQuery(c[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==c[1]&&(n+="="+i.decodeQuery(c[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+n.substring(1)}return r+i.decodeQuery(t.hash(),!0)},s.absoluteTo=function(e){var t,r,n,o=this.clone(),a=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof i||(e=new i(e)),o._parts.protocol)return o;if(o._parts.protocol=e._parts.protocol,this._parts.hostname)return o;for(r=0;n=a[r];r++)o._parts[n]=e._parts[n];return o._parts.path?(".."===o._parts.path.substring(-2)&&(o._parts.path+="/"),"/"!==o.path().charAt(0)&&(t=(t=e.directory())||(0===e.path().indexOf("/")?"/":""),o._parts.path=(t?t+"/":"")+o._parts.path,o.normalizePath())):(o._parts.path=e._parts.path,o._parts.query||(o._parts.query=e._parts.query)),o.build(),o},s.relativeTo=function(e){var t,r,n,o,a,s=this.clone().normalize();if(s._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new i(e).normalize(),t=s._parts,r=e._parts,o=s.path(),a=e.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==a.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===r.protocol&&(t.protocol=null),t.username!==r.username||t.password!==r.password)return s.build();if(null!==t.protocol||null!==t.username||null!==t.password)return s.build();if(t.hostname!==r.hostname||t.port!==r.port)return s.build();if(t.hostname=null,t.port=null,o===a)return t.path="",s.build();if(!(n=i.commonPath(o,a)))return s.build();var c=r.path.substring(n.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=c+t.path.substring(n.length)||"./",s.build()},s.equals=function(e){var t,r,n,o,a,s=this.clone(),u=new i(e),l={};if(s.normalize(),u.normalize(),s.toString()===u.toString())return!0;if(n=s.query(),o=u.query(),s.query(""),u.query(""),s.toString()!==u.toString())return!1;if(n.length!==o.length)return!1;for(a in t=i.parseQuery(n,this._parts.escapeQuerySpace),r=i.parseQuery(o,this._parts.escapeQuerySpace),t)if(c.call(t,a)){if(p(t[a])){if(!f(t[a],r[a]))return!1}else if(t[a]!==r[a])return!1;l[a]=!0}for(a in r)if(c.call(r,a)&&!l[a])return!1;return!0},s.preventInvalidHostname=function(e){return this._parts.preventInvalidHostname=!!e,this},s.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},s.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},i}))},2456:function(e,t,r){var n;function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}e=r.nmd(e),function(i){var a="object"==o(t)&&t&&!t.nodeType&&t,s="object"==o(e)&&e&&!e.nodeType&&e,c="object"==(void 0===r.g?"undefined":o(r.g))&&r.g;c.global!==c&&c.window!==c&&c.self!==c||(i=c);var u,l,p=2147483647,d=36,h=/^xn--/,f=/[^\x20-\x7E]/,m=/[\x2E\u3002\uFF0E\uFF61]/g,y={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},g=Math.floor,A=String.fromCharCode;function v(e){throw new RangeError(y[e])}function x(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function b(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+x((e=e.replace(m,".")).split("."),t).join(".")}function w(e){for(var t,r,n=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(r=e.charCodeAt(o++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),o--):n.push(t);return n}function P(e){return x(e,(function(e){var t="";return e>65535&&(t+=A((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+A(e)})).join("")}function k(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function S(e,t,r){var n=0;for(e=r?g(e/700):e>>1,e+=g(e/t);e>455;n+=d)e=g(e/35);return g(n+36*e/(e+38))}function C(e){var t,r,n,o,i,a,s,c,u,l,h,f=[],m=e.length,y=0,A=128,x=72;for((r=e.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&v("not-basic"),f.push(e.charCodeAt(n));for(o=r>0?r+1:0;o<m;){for(i=y,a=1,s=d;o>=m&&v("invalid-input"),((c=(h=e.charCodeAt(o++))-48<10?h-22:h-65<26?h-65:h-97<26?h-97:d)>=d||c>g((p-y)/a))&&v("overflow"),y+=c*a,!(c<(u=s<=x?1:s>=x+26?26:s-x));s+=d)a>g(p/(l=d-u))&&v("overflow"),a*=l;x=S(y-i,t=f.length+1,0==i),g(y/t)>p-A&&v("overflow"),A+=g(y/t),y%=t,f.splice(y++,0,A)}return P(f)}function T(e){var t,r,n,o,i,a,s,c,u,l,h,f,m,y,x,b=[];for(f=(e=w(e)).length,t=128,r=0,i=72,a=0;a<f;++a)(h=e[a])<128&&b.push(A(h));for(n=o=b.length,o&&b.push("-");n<f;){for(s=p,a=0;a<f;++a)(h=e[a])>=t&&h<s&&(s=h);for(s-t>g((p-r)/(m=n+1))&&v("overflow"),r+=(s-t)*m,t=s,a=0;a<f;++a)if((h=e[a])<t&&++r>p&&v("overflow"),h==t){for(c=r,u=d;!(c<(l=u<=i?1:u>=i+26?26:u-i));u+=d)x=c-l,y=d-l,b.push(A(k(l+x%y,0))),c=g(x/y);b.push(A(k(c,0))),i=S(r,m,n==o),r=0,++n}++r,++t}return b.join("")}if(u={version:"1.3.2",ucs2:{decode:w,encode:P},decode:C,encode:T,toASCII:function(e){return b(e,(function(e){return f.test(e)?"xn--"+T(e):e}))},toUnicode:function(e){return b(e,(function(e){return h.test(e)?C(e.slice(4).toLowerCase()):e}))}},"object"==o(r.amdO)&&r.amdO)void 0===(n=function(){return u}.call(t,r,t,e))||(e.exports=n);else if(a&&s)if(e.exports==a)s.exports=u;else for(l in u)u.hasOwnProperty(l)&&(a[l]=u[l]);else i.punycode=u}(this)},1062:function(e,t){t.AclPrivate="private",t.AclPublicRead="public-read",t.AclPublicReadWrite="public-read-write",t.AclPublicReadDelivered="public-read-delivered",t.AclPublicReadWriteDelivered="public-read-write-delivered",t.AclAuthenticatedRead="authenticated-read",t.AclBucketOwnerRead="bucket-owner-read",t.AclBucketOwnerFullControl="bucket-owner-full-control",t.AclLogDeliveryWrite="log-delivery-write",t.StorageClassStandard="STANDARD",t.StorageClassWarm="WARM",t.StorageClassCold="COLD",t.PermissionRead="READ",t.PermissionWrite="WRITE",t.PermissionReadAcp="READ_ACP",t.PermissionWriteAcp="WRITE_ACP",t.PermissionFullControl="FULL_CONTROL",t.GroupAllUsers="AllUsers",t.GroupAuthenticatedUsers="AuthenticatedUsers",t.GroupLogDelivery="LogDelivery",t.RestoreTierExpedited="Expedited",t.RestoreTierStandard="Standard",t.RestoreTierBulk="Bulk",t.GranteeGroup="Group",t.GranteeUser="CanonicalUser",t.CopyMetadata="COPY",t.ReplaceMetadata="REPLACE",t.EventObjectCreatedAll="ObjectCreated:*",t.EventObjectCreatedPut="ObjectCreated:Put",t.EventObjectCreatedPost="ObjectCreated:Post",t.EventObjectCreatedCopy="ObjectCreated:Copy",t.EventObjectCreatedCompleteMultipartUpload="ObjectCreated:CompleteMultipartUpload",t.EventObjectRemovedAll="ObjectRemoved:*",t.EventObjectRemovedDelete="ObjectRemoved:Delete",t.EventObjectRemovedDeleteMarkerCreated="ObjectRemoved:DeleteMarkerCreated"}},n={};function o(e){var t=n[e];if(void 0!==t)return t.exports;var i=n[e]={id:e,loaded:!1,exports:{}};return r[e].call(i.exports,i,i.exports,o),i.loaded=!0,i.exports}o.amdO={},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},o.t=function(r,n){if(1&n&&(r=this(r)),8&n)return r;if("object"==typeof r&&r){if(4&n&&r.__esModule)return r;if(16&n&&"function"==typeof r.then)return r}var i=Object.create(null);o.r(i);var a={};e=e||[null,t({}),t([]),t(t)];for(var s=2&n&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((function(e){a[e]=function(){return r[e]}}));return a.default=function(){return r},o.d(i,a),i},o.d=function(e,t){for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e};var i={};return function(){"use strict";o(8392);var e,t=(e=o(9269))&&e.__esModule?e:{default:e};t.default._babelPolyfill&&"undefined"!=typeof console&&console.warn&&console.warn("@babel/polyfill is loaded more than once on this page. This is probably not desirable/intended and may have consequences if different versions of the polyfills are applied sequentially. If you do need to load the polyfill more than once, use @babel/polyfill/noConflict instead to bypass the warning."),t.default._babelPolyfill=!0}(),function(){"use strict";o.d(i,{default:function(){return $r}});var e=o(5016),t=o.n(e),r=o(2990),n=o.n(r);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}function c(e,t){return c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},c(e,t)}function u(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=p(e);if(t){var o=p(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return l(this,r)}}function l(e,t){if(t&&("object"===a(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&h(e.prototype,t),r&&h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}var m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function y(e,t,r,n){var o,i,a,s=t||[0],c=(r=r||0)>>>3,u=-1===n?3:0;for(o=0;o<e.length;o+=1)i=(a=o+c)>>>2,s.length<=i&&s.push(0),s[i]|=e[o]<<8*(u+n*(a%4));return{value:s,binLen:8*e.length+r}}function g(e,t,r){switch(t){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw new Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(e){case"HEX":return function(e,t,n){return function(e,t,r,n){var o,i,a,s;if(0!=e.length%2)throw new Error("String of HEX type must be in byte increments");var c=t||[0],u=(r=r||0)>>>3,l=-1===n?3:0;for(o=0;o<e.length;o+=2){if(i=parseInt(e.substr(o,2),16),isNaN(i))throw new Error("String of HEX type contains invalid characters");for(a=(s=(o>>>1)+u)>>>2;c.length<=a;)c.push(0);c[a]|=i<<8*(l+n*(s%4))}return{value:c,binLen:4*e.length+r}}(e,t,n,r)};case"TEXT":return function(e,n,o){return function(e,t,r,n,o){var i,a,s,c,u,l,p,d,h=0,f=r||[0],m=(n=n||0)>>>3;if("UTF8"===t)for(p=-1===o?3:0,s=0;s<e.length;s+=1)for(a=[],128>(i=e.charCodeAt(s))?a.push(i):2048>i?(a.push(192|i>>>6),a.push(128|63&i)):55296>i||57344<=i?a.push(224|i>>>12,128|i>>>6&63,128|63&i):(s+=1,i=65536+((1023&i)<<10|1023&e.charCodeAt(s)),a.push(240|i>>>18,128|i>>>12&63,128|i>>>6&63,128|63&i)),c=0;c<a.length;c+=1){for(u=(l=h+m)>>>2;f.length<=u;)f.push(0);f[u]|=a[c]<<8*(p+o*(l%4)),h+=1}else for(p=-1===o?2:0,d="UTF16LE"===t&&1!==o||"UTF16LE"!==t&&1===o,s=0;s<e.length;s+=1){for(i=e.charCodeAt(s),!0===d&&(i=(c=255&i)<<8|i>>>8),u=(l=h+m)>>>2;f.length<=u;)f.push(0);f[u]|=i<<8*(p+o*(l%4)),h+=2}return{value:f,binLen:8*h+n}}(e,t,n,o,r)};case"B64":return function(e,t,n){return function(e,t,r,n){var o,i,a,s,c,u,l=0,p=t||[0],d=(r=r||0)>>>3,h=-1===n?3:0,f=e.indexOf("=");if(-1===e.search(/^[a-zA-Z0-9=+/]+$/))throw new Error("Invalid character in base-64 string");if(e=e.replace(/=/g,""),-1!==f&&f<e.length)throw new Error("Invalid '=' found in base-64 string");for(o=0;o<e.length;o+=4){for(s=e.substr(o,4),a=0,i=0;i<s.length;i+=1)a|=m.indexOf(s.charAt(i))<<18-6*i;for(i=0;i<s.length-1;i+=1){for(c=(u=l+d)>>>2;p.length<=c;)p.push(0);p[c]|=(a>>>16-8*i&255)<<8*(h+n*(u%4)),l+=1}}return{value:p,binLen:8*l+r}}(e,t,n,r)};case"BYTES":return function(e,t,n){return function(e,t,r,n){var o,i,a,s,c=t||[0],u=(r=r||0)>>>3,l=-1===n?3:0;for(i=0;i<e.length;i+=1)o=e.charCodeAt(i),a=(s=i+u)>>>2,c.length<=a&&c.push(0),c[a]|=o<<8*(l+n*(s%4));return{value:c,binLen:8*e.length+r}}(e,t,n,r)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(e){throw new Error("ARRAYBUFFER not supported by this environment")}return function(e,t,n){return function(e,t,r,n){return y(new Uint8Array(e),t,r,n)}(e,t,n,r)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(e){throw new Error("UINT8ARRAY not supported by this environment")}return function(e,t,n){return y(e,t,n,r)};default:throw new Error("format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function A(e,t,r,n){switch(e){case"HEX":return function(e){return function(e,t,r,n){var o,i,a="",s=t/8,c=-1===r?3:0;for(o=0;o<s;o+=1)i=e[o>>>2]>>>8*(c+r*(o%4)),a+="0123456789abcdef".charAt(i>>>4&15)+"0123456789abcdef".charAt(15&i);return n.outputUpper?a.toUpperCase():a}(e,t,r,n)};case"B64":return function(e){return function(e,t,r,n){var o,i,a,s,c,u="",l=t/8,p=-1===r?3:0;for(o=0;o<l;o+=3)for(s=o+1<l?e[o+1>>>2]:0,c=o+2<l?e[o+2>>>2]:0,a=(e[o>>>2]>>>8*(p+r*(o%4))&255)<<16|(s>>>8*(p+r*((o+1)%4))&255)<<8|c>>>8*(p+r*((o+2)%4))&255,i=0;i<4;i+=1)u+=8*o+6*i<=t?m.charAt(a>>>6*(3-i)&63):n.b64Pad;return u}(e,t,r,n)};case"BYTES":return function(e){return function(e,t,r){var n,o,i="",a=t/8,s=-1===r?3:0;for(n=0;n<a;n+=1)o=e[n>>>2]>>>8*(s+r*(n%4))&255,i+=String.fromCharCode(o);return i}(e,t,r)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(e){throw new Error("ARRAYBUFFER not supported by this environment")}return function(e){return function(e,t,r){var n,o=t/8,i=new ArrayBuffer(o),a=new Uint8Array(i),s=-1===r?3:0;for(n=0;n<o;n+=1)a[n]=e[n>>>2]>>>8*(s+r*(n%4))&255;return i}(e,t,r)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(e){throw new Error("UINT8ARRAY not supported by this environment")}return function(e){return function(e,t,r){var n,o=t/8,i=-1===r?3:0,a=new Uint8Array(o);for(n=0;n<o;n+=1)a[n]=e[n>>>2]>>>8*(i+r*(n%4))&255;return a}(e,t,r)};default:throw new Error("format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}var v=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],x=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],b=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],w="Chosen SHA variant is not supported";function P(e,t){var r,n,o=e.binLen>>>3,i=t.binLen>>>3,a=o<<3,s=4-o<<3;if(o%4!=0){for(r=0;r<i;r+=4)n=o+r>>>2,e.value[n]|=t.value[r>>>2]<<a,e.value.push(0),e.value[n+1]|=t.value[r>>>2]>>>s;return(e.value.length<<2)-4>=i+o&&e.value.pop(),{value:e.value,binLen:e.binLen+t.binLen}}return{value:e.value.concat(t.value),binLen:e.binLen+t.binLen}}function k(e){var t={outputUpper:!1,b64Pad:"=",outputLen:-1},r=e||{},n="Output length must be a multiple of 8";if(t.outputUpper=r.outputUpper||!1,r.b64Pad&&(t.b64Pad=r.b64Pad),r.outputLen){if(r.outputLen%8!=0)throw new Error(n);t.outputLen=r.outputLen}else if(r.shakeLen){if(r.shakeLen%8!=0)throw new Error(n);t.outputLen=r.shakeLen}if("boolean"!=typeof t.outputUpper)throw new Error("Invalid outputUpper formatting option");if("string"!=typeof t.b64Pad)throw new Error("Invalid b64Pad formatting option");return t}function S(e,t,r,n){var o=e+" must include a value and format";if(!t){if(!n)throw new Error(o);return n}if(void 0===t.value||!t.format)throw new Error(o);return g(t.format,t.encoding||"UTF8",r)(t.value)}var C=function(){function e(t,r,n){d(this,e);var o=n||{};if(this.t=r,this.i=o.encoding||"UTF8",this.numRounds=o.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error("numRounds must a integer >= 1");this.s=t,this.o=[],this.h=0,this.u=!1,this.l=0,this.A=!1,this.H=[],this.S=[]}return f(e,[{key:"update",value:function(e){var t,r=0,n=this.p>>>5,o=this.m(e,this.o,this.h),i=o.binLen,a=o.value,s=i>>>5;for(t=0;t<s;t+=n)r+this.p<=i&&(this.C=this.R(a.slice(t,t+n),this.C),r+=this.p);this.l+=r,this.o=a.slice(r>>>5),this.h=i%this.p,this.u=!0}},{key:"getHash",value:function(e,t){var r,n,o=this.U,i=k(t);if(this.v){if(-1===i.outputLen)throw new Error("Output length must be specified in options");o=i.outputLen}var a=A(e,o,this.K,i);if(this.A&&this.T)return a(this.T(i));for(n=this.F(this.o.slice(),this.h,this.l,this.g(this.C),o),r=1;r<this.numRounds;r+=1)this.v&&o%32!=0&&(n[n.length-1]&=16777215>>>24-o%32),n=this.F(n,o,0,this.B(this.s),o);return a(n)}},{key:"setHMACKey",value:function(e,t,r){if(!this.L)throw new Error("Variant does not support HMAC");if(this.u)throw new Error("Cannot set MAC key after calling update");var n=g(t,(r||{}).encoding||"UTF8",this.K);this.M(n(e))}},{key:"M",value:function(e){var t,r=this.p>>>3,n=r/4-1;if(1!==this.numRounds)throw new Error("Cannot set numRounds with MAC");if(this.A)throw new Error("MAC key already set");for(r<e.binLen/8&&(e.value=this.F(e.value,e.binLen,0,this.B(this.s),this.U));e.value.length<=n;)e.value.push(0);for(t=0;t<=n;t+=1)this.H[t]=909522486^e.value[t],this.S[t]=1549556828^e.value[t];this.C=this.R(this.H,this.C),this.l=this.p,this.A=!0}},{key:"getHMAC",value:function(e,t){var r=k(t);return A(e,this.U,this.K,r)(this.k())}},{key:"k",value:function(){var e;if(!this.A)throw new Error("Cannot call getHMAC without first setting MAC key");var t=this.F(this.o.slice(),this.h,this.l,this.g(this.C),this.U);return e=this.R(this.S,this.B(this.s)),this.F(t,this.U,this.p,e,this.U)}}]),e}();function T(e,t){return e<<t|e>>>32-t}function E(e,t){return e>>>t|e<<32-t}function M(e,t){return e>>>t}function O(e,t,r){return e^t^r}function B(e,t,r){return e&t^~e&r}function R(e,t,r){return e&t^e&r^t&r}function I(e){return E(e,2)^E(e,13)^E(e,22)}function D(e,t){var r=(65535&e)+(65535&t);return(65535&(e>>>16)+(t>>>16)+(r>>>16))<<16|65535&r}function j(e,t,r,n){var o=(65535&e)+(65535&t)+(65535&r)+(65535&n);return(65535&(e>>>16)+(t>>>16)+(r>>>16)+(n>>>16)+(o>>>16))<<16|65535&o}function L(e,t,r,n,o){var i=(65535&e)+(65535&t)+(65535&r)+(65535&n)+(65535&o);return(65535&(e>>>16)+(t>>>16)+(r>>>16)+(n>>>16)+(o>>>16)+(i>>>16))<<16|65535&i}function _(e){return E(e,7)^E(e,18)^M(e,3)}function q(e){return E(e,6)^E(e,11)^E(e,25)}function N(e){return[1732584193,4023233417,2562383102,271733878,3285377520]}function F(e,t){var r,n,o,i,a,s,c,u=[];for(r=t[0],n=t[1],o=t[2],i=t[3],a=t[4],c=0;c<80;c+=1)u[c]=c<16?e[c]:T(u[c-3]^u[c-8]^u[c-14]^u[c-16],1),s=c<20?L(T(r,5),B(n,o,i),a,1518500249,u[c]):c<40?L(T(r,5),O(n,o,i),a,1859775393,u[c]):c<60?L(T(r,5),R(n,o,i),a,2400959708,u[c]):L(T(r,5),O(n,o,i),a,3395469782,u[c]),a=i,i=o,o=T(n,30),n=r,r=s;return t[0]=D(r,t[0]),t[1]=D(n,t[1]),t[2]=D(o,t[2]),t[3]=D(i,t[3]),t[4]=D(a,t[4]),t}function U(e,t,r,n){for(var o,i=15+(t+65>>>9<<4),a=t+r;e.length<=i;)e.push(0);for(e[t>>>5]|=128<<24-t%32,e[i]=4294967295&a,e[i-1]=a/4294967296|0,o=0;o<e.length;o+=16)n=F(e.slice(o,o+16),n);return n}var K=function(e){s(r,e);var t=u(r);function r(e,n,o){var i;if(d(this,r),"SHA-1"!==e)throw new Error(w);var a=o||{};return(i=t.call(this,e,n,o)).L=!0,i.T=i.k,i.K=-1,i.m=g(i.t,i.i,i.K),i.R=F,i.g=function(e){return e.slice()},i.B=N,i.F=U,i.C=[1732584193,4023233417,2562383102,271733878,3285377520],i.p=512,i.U=160,i.v=!1,a.hmacKey&&i.M(S("hmacKey",a.hmacKey,i.K)),i}return f(r)}(C);function G(e){return"SHA-224"==e?x.slice():b.slice()}function H(e,t){var r,n,o,i,a,s,c,u,l,p,d,h,f=[];for(r=t[0],n=t[1],o=t[2],i=t[3],a=t[4],s=t[5],c=t[6],u=t[7],d=0;d<64;d+=1)f[d]=d<16?e[d]:j(E(h=f[d-2],17)^E(h,19)^M(h,10),f[d-7],_(f[d-15]),f[d-16]),l=L(u,q(a),B(a,s,c),v[d],f[d]),p=D(I(r),R(r,n,o)),u=c,c=s,s=a,a=D(i,l),i=o,o=n,n=r,r=D(l,p);return t[0]=D(r,t[0]),t[1]=D(n,t[1]),t[2]=D(o,t[2]),t[3]=D(i,t[3]),t[4]=D(a,t[4]),t[5]=D(s,t[5]),t[6]=D(c,t[6]),t[7]=D(u,t[7]),t}var z=function(e){s(r,e);var t=u(r);function r(e,n,o){var i;if(d(this,r),"SHA-224"!==e&&"SHA-256"!==e)throw new Error(w);var a=o||{};return(i=t.call(this,e,n,o)).T=i.k,i.L=!0,i.K=-1,i.m=g(i.t,i.i,i.K),i.R=H,i.g=function(e){return e.slice()},i.B=G,i.F=function(t,r,n,o){return function(e,t,r,n,o){for(var i,a=15+(t+65>>>9<<4),s=t+r;e.length<=a;)e.push(0);for(e[t>>>5]|=128<<24-t%32,e[a]=4294967295&s,e[a-1]=s/4294967296|0,i=0;i<e.length;i+=16)n=H(e.slice(i,i+16),n);return"SHA-224"===o?[n[0],n[1],n[2],n[3],n[4],n[5],n[6]]:n}(t,r,n,o,e)},i.C=G(e),i.p=512,i.U="SHA-224"===e?224:256,i.v=!1,a.hmacKey&&i.M(S("hmacKey",a.hmacKey,i.K)),i}return f(r)}(C),W=f((function e(t,r){d(this,e),this.Y=t,this.N=r}));function V(e,t){var r;return t>32?(r=64-t,new W(e.N<<t|e.Y>>>r,e.Y<<t|e.N>>>r)):0!==t?(r=32-t,new W(e.Y<<t|e.N>>>r,e.N<<t|e.Y>>>r)):e}function Q(e,t){var r;return t<32?(r=32-t,new W(e.Y>>>t|e.N<<r,e.N>>>t|e.Y<<r)):(r=64-t,new W(e.N>>>t|e.Y<<r,e.Y>>>t|e.N<<r))}function Y(e,t){return new W(e.Y>>>t,e.N>>>t|e.Y<<32-t)}function X(e,t,r){return new W(e.Y&t.Y^e.Y&r.Y^t.Y&r.Y,e.N&t.N^e.N&r.N^t.N&r.N)}function $(e){var t=Q(e,28),r=Q(e,34),n=Q(e,39);return new W(t.Y^r.Y^n.Y,t.N^r.N^n.N)}function J(e,t){var r,n;r=(65535&e.N)+(65535&t.N);var o=(65535&(n=(e.N>>>16)+(t.N>>>16)+(r>>>16)))<<16|65535&r;return r=(65535&e.Y)+(65535&t.Y)+(n>>>16),n=(e.Y>>>16)+(t.Y>>>16)+(r>>>16),new W((65535&n)<<16|65535&r,o)}function Z(e,t,r,n){var o,i;o=(65535&e.N)+(65535&t.N)+(65535&r.N)+(65535&n.N);var a=(65535&(i=(e.N>>>16)+(t.N>>>16)+(r.N>>>16)+(n.N>>>16)+(o>>>16)))<<16|65535&o;return o=(65535&e.Y)+(65535&t.Y)+(65535&r.Y)+(65535&n.Y)+(i>>>16),i=(e.Y>>>16)+(t.Y>>>16)+(r.Y>>>16)+(n.Y>>>16)+(o>>>16),new W((65535&i)<<16|65535&o,a)}function ee(e,t,r,n,o){var i,a;i=(65535&e.N)+(65535&t.N)+(65535&r.N)+(65535&n.N)+(65535&o.N);var s=(65535&(a=(e.N>>>16)+(t.N>>>16)+(r.N>>>16)+(n.N>>>16)+(o.N>>>16)+(i>>>16)))<<16|65535&i;return i=(65535&e.Y)+(65535&t.Y)+(65535&r.Y)+(65535&n.Y)+(65535&o.Y)+(a>>>16),a=(e.Y>>>16)+(t.Y>>>16)+(r.Y>>>16)+(n.Y>>>16)+(o.Y>>>16)+(i>>>16),new W((65535&a)<<16|65535&i,s)}function te(e,t){return new W(e.Y^t.Y,e.N^t.N)}function re(e){var t=Q(e,19),r=Q(e,61),n=Y(e,6);return new W(t.Y^r.Y^n.Y,t.N^r.N^n.N)}function ne(e){var t=Q(e,1),r=Q(e,8),n=Y(e,7);return new W(t.Y^r.Y^n.Y,t.N^r.N^n.N)}function oe(e){var t=Q(e,14),r=Q(e,18),n=Q(e,41);return new W(t.Y^r.Y^n.Y,t.N^r.N^n.N)}var ie=[new W(v[0],3609767458),new W(v[1],602891725),new W(v[2],3964484399),new W(v[3],2173295548),new W(v[4],4081628472),new W(v[5],3053834265),new W(v[6],2937671579),new W(v[7],3664609560),new W(v[8],2734883394),new W(v[9],1164996542),new W(v[10],1323610764),new W(v[11],3590304994),new W(v[12],4068182383),new W(v[13],991336113),new W(v[14],633803317),new W(v[15],3479774868),new W(v[16],2666613458),new W(v[17],944711139),new W(v[18],2341262773),new W(v[19],2007800933),new W(v[20],1495990901),new W(v[21],1856431235),new W(v[22],3175218132),new W(v[23],2198950837),new W(v[24],3999719339),new W(v[25],766784016),new W(v[26],2566594879),new W(v[27],3203337956),new W(v[28],1034457026),new W(v[29],2466948901),new W(v[30],3758326383),new W(v[31],168717936),new W(v[32],1188179964),new W(v[33],1546045734),new W(v[34],1522805485),new W(v[35],2643833823),new W(v[36],2343527390),new W(v[37],1014477480),new W(v[38],1206759142),new W(v[39],344077627),new W(v[40],1290863460),new W(v[41],3158454273),new W(v[42],3505952657),new W(v[43],106217008),new W(v[44],3606008344),new W(v[45],1432725776),new W(v[46],1467031594),new W(v[47],851169720),new W(v[48],3100823752),new W(v[49],1363258195),new W(v[50],3750685593),new W(v[51],3785050280),new W(v[52],3318307427),new W(v[53],3812723403),new W(v[54],2003034995),new W(v[55],3602036899),new W(v[56],1575990012),new W(v[57],1125592928),new W(v[58],2716904306),new W(v[59],442776044),new W(v[60],593698344),new W(v[61],3733110249),new W(v[62],2999351573),new W(v[63],3815920427),new W(3391569614,3928383900),new W(3515267271,566280711),new W(3940187606,3454069534),new W(4118630271,4000239992),new W(116418474,1914138554),new W(174292421,2731055270),new W(289380356,3203993006),new W(460393269,320620315),new W(685471733,587496836),new W(852142971,1086792851),new W(1017036298,365543100),new W(1126000580,2618297676),new W(1288033470,3409855158),new W(1501505948,4234509866),new W(1607167915,987167468),new W(1816402316,1246189591)];function ae(e){return"SHA-384"===e?[new W(3418070365,x[0]),new W(1654270250,x[1]),new W(2438529370,x[2]),new W(355462360,x[3]),new W(1731405415,x[4]),new W(41048885895,x[5]),new W(3675008525,x[6]),new W(1203062813,x[7])]:[new W(b[0],4089235720),new W(b[1],2227873595),new W(b[2],4271175723),new W(b[3],1595750129),new W(b[4],2917565137),new W(b[5],725511199),new W(b[6],4215389547),new W(b[7],327033209)]}function se(e,t){var r,n,o,i,a,s,c,u,l,p,d,h,f,m,y,g=[];for(r=t[0],n=t[1],o=t[2],i=t[3],a=t[4],s=t[5],c=t[6],u=t[7],d=0;d<80;d+=1)d<16?(h=2*d,g[d]=new W(e[h],e[h+1])):g[d]=Z(re(g[d-2]),g[d-7],ne(g[d-15]),g[d-16]),l=ee(u,oe(a),(m=s,y=c,new W((f=a).Y&m.Y^~f.Y&y.Y,f.N&m.N^~f.N&y.N)),ie[d],g[d]),p=J($(r),X(r,n,o)),u=c,c=s,s=a,a=J(i,l),i=o,o=n,n=r,r=J(l,p);return t[0]=J(r,t[0]),t[1]=J(n,t[1]),t[2]=J(o,t[2]),t[3]=J(i,t[3]),t[4]=J(a,t[4]),t[5]=J(s,t[5]),t[6]=J(c,t[6]),t[7]=J(u,t[7]),t}var ce=function(e){s(r,e);var t=u(r);function r(e,n,o){var i;if(d(this,r),"SHA-384"!==e&&"SHA-512"!==e)throw new Error(w);var a=o||{};return(i=t.call(this,e,n,o)).T=i.k,i.L=!0,i.K=-1,i.m=g(i.t,i.i,i.K),i.R=se,i.g=function(e){return e.slice()},i.B=ae,i.F=function(t,r,n,o){return function(e,t,r,n,o){for(var i,a=31+(t+129>>>10<<5),s=t+r;e.length<=a;)e.push(0);for(e[t>>>5]|=128<<24-t%32,e[a]=4294967295&s,e[a-1]=s/4294967296|0,i=0;i<e.length;i+=32)n=se(e.slice(i,i+32),n);return"SHA-384"===o?[(n=n)[0].Y,n[0].N,n[1].Y,n[1].N,n[2].Y,n[2].N,n[3].Y,n[3].N,n[4].Y,n[4].N,n[5].Y,n[5].N]:[n[0].Y,n[0].N,n[1].Y,n[1].N,n[2].Y,n[2].N,n[3].Y,n[3].N,n[4].Y,n[4].N,n[5].Y,n[5].N,n[6].Y,n[6].N,n[7].Y,n[7].N]}(t,r,n,o,e)},i.C=ae(e),i.p=1024,i.U="SHA-384"===e?384:512,i.v=!1,a.hmacKey&&i.M(S("hmacKey",a.hmacKey,i.K)),i}return f(r)}(C),ue=[new W(0,1),new W(0,32898),new W(2147483648,32906),new W(2147483648,2147516416),new W(0,32907),new W(0,2147483649),new W(2147483648,2147516545),new W(2147483648,32777),new W(0,138),new W(0,136),new W(0,2147516425),new W(0,2147483658),new W(0,2147516555),new W(2147483648,139),new W(2147483648,32905),new W(2147483648,32771),new W(2147483648,32770),new W(2147483648,128),new W(0,32778),new W(2147483648,2147483658),new W(2147483648,2147516545),new W(2147483648,32896),new W(0,2147483649),new W(2147483648,2147516424)],le=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]];function pe(e){var t,r=[];for(t=0;t<5;t+=1)r[t]=[new W(0,0),new W(0,0),new W(0,0),new W(0,0),new W(0,0)];return r}function de(e){var t,r=[];for(t=0;t<5;t+=1)r[t]=e[t].slice();return r}function he(e,t){var r,n,o,i,a,s,c,u,l,p=[],d=[];if(null!==e)for(n=0;n<e.length;n+=2)t[(n>>>1)%5][(n>>>1)/5|0]=te(t[(n>>>1)%5][(n>>>1)/5|0],new W(e[n+1],e[n]));for(r=0;r<24;r+=1){for(i=pe(),n=0;n<5;n+=1)p[n]=(a=t[n][0],s=t[n][1],c=t[n][2],u=t[n][3],l=t[n][4],new W(a.Y^s.Y^c.Y^u.Y^l.Y,a.N^s.N^c.N^u.N^l.N));for(n=0;n<5;n+=1)d[n]=te(p[(n+4)%5],V(p[(n+1)%5],1));for(n=0;n<5;n+=1)for(o=0;o<5;o+=1)t[n][o]=te(t[n][o],d[n]);for(n=0;n<5;n+=1)for(o=0;o<5;o+=1)i[o][(2*n+3*o)%5]=V(t[n][o],le[n][o]);for(n=0;n<5;n+=1)for(o=0;o<5;o+=1)t[n][o]=te(i[n][o],new W(~i[(n+1)%5][o].Y&i[(n+2)%5][o].Y,~i[(n+1)%5][o].N&i[(n+2)%5][o].N));t[0][0]=te(t[0][0],ue[r])}return t}function fe(e){var t,r,n=0,o=[0,0],i=[4294967295&e,e/4294967296&2097151];for(t=6;t>=0;t--)0==(r=i[t>>2]>>>8*t&255)&&0===n||(o[n+1>>2]|=r<<8*(n+1),n+=1);return n=0!==n?n:1,o[0]|=n,{value:n+1>4?o:[o[0]],binLen:8+8*n}}function me(e){return P(fe(e.binLen),e)}function ye(e,t){var r,n=fe(t),o=t>>>2,i=(o-(n=P(n,e)).value.length%o)%o;for(r=0;r<i;r++)n.value.push(0);return n.value}var ge=function(e){s(r,e);var t=u(r);function r(e,n,o){var i;d(this,r);var a=6,s=0,c=o||{};if(1!==(i=t.call(this,e,n,o)).numRounds){if(c.kmacKey||c.hmacKey)throw new Error("Cannot set numRounds with MAC");if("CSHAKE128"===i.s||"CSHAKE256"===i.s)throw new Error("Cannot set numRounds for CSHAKE variants")}switch(i.K=1,i.m=g(i.t,i.i,i.K),i.R=he,i.g=de,i.B=pe,i.C=pe(),i.v=!1,e){case"SHA3-224":i.p=s=1152,i.U=224,i.L=!0,i.T=i.k;break;case"SHA3-256":i.p=s=1088,i.U=256,i.L=!0,i.T=i.k;break;case"SHA3-384":i.p=s=832,i.U=384,i.L=!0,i.T=i.k;break;case"SHA3-512":i.p=s=576,i.U=512,i.L=!0,i.T=i.k;break;case"SHAKE128":a=31,i.p=s=1344,i.U=-1,i.v=!0,i.L=!1,i.T=null;break;case"SHAKE256":a=31,i.p=s=1088,i.U=-1,i.v=!0,i.L=!1,i.T=null;break;case"KMAC128":a=4,i.p=s=1344,i.I(o),i.U=-1,i.v=!0,i.L=!1,i.T=i.X;break;case"KMAC256":a=4,i.p=s=1088,i.I(o),i.U=-1,i.v=!0,i.L=!1,i.T=i.X;break;case"CSHAKE128":i.p=s=1344,a=i._(o),i.U=-1,i.v=!0,i.L=!1,i.T=null;break;case"CSHAKE256":i.p=s=1088,a=i._(o),i.U=-1,i.v=!0,i.L=!1,i.T=null;break;default:throw new Error(w)}return i.F=function(e,t,r,n,o){return function(e,t,r,n,o,i,a){var s,c,u=0,l=[],p=o>>>5,d=t>>>5;for(s=0;s<d&&t>=o;s+=p)n=he(e.slice(s,s+p),n),t-=o;for(e=e.slice(s),t%=o;e.length<p;)e.push(0);for(e[(s=t>>>3)>>2]^=i<<s%4*8,e[p-1]^=2147483648,n=he(e,n);32*l.length<a&&(c=n[u%5][u/5|0],l.push(c.N),!(32*l.length>=a));)l.push(c.Y),0==64*(u+=1)%o&&(he(null,n),u=0);return l}(e,t,0,n,s,a,o)},c.hmacKey&&i.M(S("hmacKey",c.hmacKey,i.K)),i}return f(r,[{key:"_",value:function(e,t){var r=function(e){var t=e||{};return{funcName:S("funcName",t.funcName,1,{value:[],binLen:0}),customization:S("Customization",t.customization,1,{value:[],binLen:0})}}(e||{});t&&(r.funcName=t);var n=P(me(r.funcName),me(r.customization));if(0!==r.customization.binLen||0!==r.funcName.binLen){for(var o=ye(n,this.p>>>3),i=0;i<o.length;i+=this.p>>>5)this.C=this.R(o.slice(i,i+(this.p>>>5)),this.C),this.l+=this.p;return 4}return 31}},{key:"I",value:function(e){var t=function(e){var t=e||{};return{kmacKey:S("kmacKey",t.kmacKey,1),funcName:{value:[1128353099],binLen:32},customization:S("Customization",t.customization,1,{value:[],binLen:0})}}(e||{});this._(e,t.funcName);for(var r=ye(me(t.kmacKey),this.p>>>3),n=0;n<r.length;n+=this.p>>>5)this.C=this.R(r.slice(n,n+(this.p>>>5)),this.C),this.l+=this.p;this.A=!0}},{key:"X",value:function(e){var t=P({value:this.o.slice(),binLen:this.h},function(e){var t,r,n=0,o=[0,0],i=[4294967295&e,e/4294967296&2097151];for(t=6;t>=0;t--)0==(r=i[t>>2]>>>8*t&255)&&0===n||(o[n>>2]|=r<<8*n,n+=1);return o[(n=0!==n?n:1)>>2]|=n<<8*n,{value:n+1>4?o:[o[0]],binLen:8+8*n}}(e.outputLen));return this.F(t.value,t.binLen,this.l,this.g(this.C),e.outputLen)}}]),r}(C),Ae=function(){function e(t,r,n){if(d(this,e),"SHA-1"==t)this.O=new K(t,r,n);else if("SHA-224"==t||"SHA-256"==t)this.O=new z(t,r,n);else if("SHA-384"==t||"SHA-512"==t)this.O=new ce(t,r,n);else{if("SHA3-224"!=t&&"SHA3-256"!=t&&"SHA3-384"!=t&&"SHA3-512"!=t&&"SHAKE128"!=t&&"SHAKE256"!=t&&"CSHAKE128"!=t&&"CSHAKE256"!=t&&"KMAC128"!=t&&"KMAC256"!=t)throw new Error(w);this.O=new ge(t,r,n)}}return f(e,[{key:"update",value:function(e){this.O.update(e)}},{key:"getHash",value:function(e,t){return this.O.getHash(e,t)}},{key:"setHMACKey",value:function(e,t,r){this.O.setHMACKey(e,t,r)}},{key:"getHMAC",value:function(e,t){return this.O.getHMAC(e,t)}}]),e}(),ve="function"==typeof atob,xe="function"==typeof btoa,be="function"==typeof Buffer,we="function"==typeof TextDecoder?new TextDecoder:void 0,Pe="function"==typeof TextEncoder?new TextEncoder:void 0,ke=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),Se=function(e){var t={};return e.forEach((function(e,r){return t[e]=r})),t}(ke),Ce=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Te=String.fromCharCode.bind(String),Ee="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return e};return new Uint8Array(Array.prototype.slice.call(e,0).map(t))},Me=function(e){return e.replace(/=/g,"").replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"}))},Oe=function(e){return e.replace(/[^A-Za-z0-9\+\/]/g,"")},Be=function(e){for(var t,r,n,o,i="",a=e.length%3,s=0;s<e.length;){if((r=e.charCodeAt(s++))>255||(n=e.charCodeAt(s++))>255||(o=e.charCodeAt(s++))>255)throw new TypeError("invalid character found");i+=ke[(t=r<<16|n<<8|o)>>18&63]+ke[t>>12&63]+ke[t>>6&63]+ke[63&t]}return a?i.slice(0,a-3)+"===".substring(a):i},Re=xe?function(e){return btoa(e)}:be?function(e){return Buffer.from(e,"binary").toString("base64")}:Be,Ie=be?function(e){return Buffer.from(e).toString("base64")}:function(e){for(var t=[],r=0,n=e.length;r<n;r+=4096)t.push(Te.apply(null,e.subarray(r,r+4096)));return Re(t.join(""))},De=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t?Me(Ie(e)):Ie(e)},je=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?Te(192|t>>>6)+Te(128|63&t):Te(224|t>>>12&15)+Te(128|t>>>6&63)+Te(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return Te(240|t>>>18&7)+Te(128|t>>>12&63)+Te(128|t>>>6&63)+Te(128|63&t)},Le=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,_e=function(e){return e.replace(Le,je)},qe=be?function(e){return Buffer.from(e,"utf8").toString("base64")}:Pe?function(e){return Ie(Pe.encode(e))}:function(e){return Re(_e(e))},Ne=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t?Me(qe(e)):qe(e)},Fe=function(e){return Ne(e,!0)},Ue=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Ke=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return Te(55296+(t>>>10))+Te(56320+(1023&t));case 3:return Te((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return Te((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},Ge=function(e){return e.replace(Ue,Ke)},He=function(e){if(e=e.replace(/\s+/g,""),!Ce.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));for(var t,r,n,o="",i=0;i<e.length;)t=Se[e.charAt(i++)]<<18|Se[e.charAt(i++)]<<12|(r=Se[e.charAt(i++)])<<6|(n=Se[e.charAt(i++)]),o+=64===r?Te(t>>16&255):64===n?Te(t>>16&255,t>>8&255):Te(t>>16&255,t>>8&255,255&t);return o},ze=ve?function(e){return atob(Oe(e))}:be?function(e){return Buffer.from(e,"base64").toString("binary")}:He,We=be?function(e){return Ee(Buffer.from(e,"base64"))}:function(e){return Ee(ze(e),(function(e){return e.charCodeAt(0)}))},Ve=function(e){return We(Ye(e))},Qe=be?function(e){return Buffer.from(e,"base64").toString("utf8")}:we?function(e){return we.decode(We(e))}:function(e){return Ge(ze(e))},Ye=function(e){return Oe(e.replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})))},Xe=function(e){return Qe(Ye(e))},$e=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}},Je=function(){var e=function(e,t){return Object.defineProperty(String.prototype,e,$e(t))};e("fromBase64",(function(){return Xe(this)})),e("toBase64",(function(e){return Ne(this,e)})),e("toBase64URI",(function(){return Ne(this,!0)})),e("toBase64URL",(function(){return Ne(this,!0)})),e("toUint8Array",(function(){return Ve(this)}))},Ze=function(){var e=function(e,t){return Object.defineProperty(Uint8Array.prototype,e,$e(t))};e("toBase64",(function(e){return De(this,e)})),e("toBase64URI",(function(){return De(this,!0)})),e("toBase64URL",(function(){return De(this,!0)}))},et={version:"3.7.2",VERSION:"3.7.2",atob:ze,atobPolyfill:He,btoa:Re,btoaPolyfill:Be,fromBase64:Xe,toBase64:Ne,encode:Ne,encodeURI:Fe,encodeURL:Fe,utob:_e,btou:Ge,decode:Xe,isValid:function(e){if("string"!=typeof e)return!1;var t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:De,toUint8Array:Ve,extendString:Je,extendUint8Array:Ze,extendBuiltins:function(){Je(),Ze()}},tt=o(5917),rt=o.n(tt),nt=new function(){var e=function e(t){var r={};if(1===t.nodeType){if(t.attributes.length>0){r["@attributes"]={};for(var n=0;n<t.attributes.length;n++){var o=t.attributes.item(n);r["@attributes"][o.nodeName]=o.value}}}else 3===t.nodeType&&(r=t.nodeValue);if(t.hasChildNodes())for(var i=0;i<t.childNodes.length;i++){var a=t.childNodes.item(i),s=a.nodeName;if(void 0===r[s])r[s]=e(a);else{if(void 0===r[s].push){var c=r[s];r[s]=[],r[s].push(c)}r[s].push(e(a))}}return r};this.parseString=function(t,r){var n;window.DOMParser?n=(new window.DOMParser).parseFromString(t,"text/xml"):(n=new window.ActiveXObject("Microsoft.XMLDOM")).async="false";var o,i,a=(o=e(n),-1===(i=JSON.stringify(o,void 0,2).replace(/(\\t|\\r|\\n)/g,"").replace(/"",[\n\t\r\s]+""[,]*/g,"").replace(/(\n[\t\s\r]*\n)/g,"").replace(/[\s\t]{2,}""[,]{0,1}/g,"").replace(/"[\s\t]{1,}"[,]{0,1}/g,"")).indexOf('"parsererror": {')?i:"Invalid XML format");return void 0===r?JSON.parse(a):a}},ot={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"}}},it={type:"object",location:"xml",sentAs:"Initiator",parameters:{ID:{sentAs:"ID"}}},at={sentAs:"BackToSourceRule",required:!0,location:"xml",type:"array",items:{type:"object",parameters:{ID:{sentAs:"ID"},Condition:{sentAs:"Condition",type:"object",parameters:{ObjectKeyPrefixEquals:{sentAs:"ObjectKeyPrefixEquals"},HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"}}},Redirect:{sentAs:"Redirect",type:"object",parameters:{HttpRedirectCode:{sentAs:"HttpRedirectCode"},SourceEndpoint:{sentAs:"SourceEndpoint"},SourceBucketName:{sentAs:"SourceBucketName"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"},StaticUri:{sentAs:"StaticUri"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},MigrationConfiguration:{sentAs:"MigrationConfiguration",type:"object",parameters:{Agency:{sentAs:"Agency"},LogBucketName:{sentAs:"LogBucketName"},PrivateBucketConfiguration:{sentAs:"PrivateBucketConfiguration",type:"object",parameters:{SourceStorageProvider:{sentAs:"SourceStorageProvider"},SourceBucketAK:{sentAs:"SourceBucketAK"},SourceBucketSK:{sentAs:"SourceBucketSK"},SourceBucketZone:{sentAs:"SourceBucketZone"}}}}}}}}}},st={type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{decode:!0,sentAs:"Prefix"},MTime:{sentAs:"MTime"},InodeNo:{sentAs:"InodeNo"}}}},ct={type:"array",location:"xml",wrapper:"AccessControlList",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID",notAllowEmpty:!0},URI:{sentAs:"Canned",type:"adapter",notAllowEmpty:!0}}},Permission:{sentAs:"Permission"},Delivered:{sentAs:"Delivered"}}}},ut={type:"object",location:"xml",sentAs:"LoggingEnabled",parameters:{TargetBucket:{sentAs:"TargetBucket"},TargetPrefix:{sentAs:"TargetPrefix"},TargetGrants:{type:"array",wrapper:"TargetGrants",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},URI:{sentAs:"Canned",type:"adapter"}}},Permission:{sentAs:"Permission"}}}}}},lt={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Transitions:{type:"array",sentAs:"Transition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}}},Expiration:{type:"object",sentAs:"Expiration",parameters:{Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}},AbortIncompleteMultipartUpload:{type:"object",sentAs:"AbortIncompleteMultipartUpload",parameters:{DaysAfterInitiation:{type:"number",sentAs:"DaysAfterInitiation"}}},NoncurrentVersionTransitions:{type:"array",sentAs:"NoncurrentVersionTransition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}},NoncurrentVersionExpiration:{type:"object",sentAs:"NoncurrentVersionExpiration",parameters:{NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}}}},pt={type:"object",location:"xml",sentAs:"RedirectAllRequestsTo",parameters:{HostName:{sentAs:"HostName"},Protocol:{sentAs:"Protocol"}}},dt={type:"array",wrapper:"RoutingRules",location:"xml",sentAs:"RoutingRule",items:{type:"object",parameters:{Condition:{type:"object",sentAs:"Condition",parameters:{HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"},KeyPrefixEquals:{sentAs:"KeyPrefixEquals"}}},Redirect:{type:"object",sentAs:"Redirect",parameters:{HostName:{sentAs:"HostName"},HttpRedirectCode:{sentAs:"HttpRedirectCode"},Protocol:{sentAs:"Protocol"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"}}}}}},ht={type:"object",location:"xml",sentAs:"IndexDocument",parameters:{Suffix:{sentAs:"Suffix"}}},ft={type:"object",location:"xml",sentAs:"ErrorDocument",parameters:{Key:{sentAs:"Key"}}},mt={required:!0,type:"array",location:"xml",sentAs:"CORSRule",items:{type:"object",parameters:{ID:{sentAs:"ID"},AllowedMethod:{type:"array",sentAs:"AllowedMethod",items:{type:"string"}},AllowedOrigin:{type:"array",sentAs:"AllowedOrigin",items:{type:"string"}},AllowedHeader:{type:"array",sentAs:"AllowedHeader",items:{type:"string"}},MaxAgeSeconds:{type:"number",sentAs:"MaxAgeSeconds"},ExposeHeader:{type:"array",sentAs:"ExposeHeader",items:{type:"string"}}}}},yt={type:"array",location:"xml",sentAs:"FunctionGraphConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionGraph:{},Event:{type:"array",items:{type:"adapter"}}}}},gt={type:"array",location:"xml",sentAs:"TopicConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},Topic:{},Event:{type:"array",items:{type:"adapter"}}}}},At={type:"array",location:"xml",sentAs:"FunctionStageConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionStage:{},Event:{type:"array",items:{type:"adapter"}}}}},vt={required:!0,type:"array",location:"xml",wrapper:"TagSet",sentAs:"Tag",items:{type:"object",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}},xt={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Destination:{type:"object",sentAs:"Destination",parameters:{Bucket:{sentAs:"Bucket",type:"adapter"},StorageClass:{sentAs:"StorageClass",type:"adapter"},DeleteData:{sentAs:"DeleteData",type:"string"}}},HistoricalObjectReplication:{sentAs:"HistoricalObjectReplication"}}}},bt={type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"},ProjectID:{sentAs:"ProjectID"}}}}},wt={type:"object",location:"xml",sentAs:"InventoryConfiguration",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"},Encryption:{type:"object",sentAs:"Encryption",parameters:{"SSE-KMS":{type:"object",sentAs:"SSE-KMS",parameters:{KeyId:{sentAs:"KeyId",type:"adapter"}}}}}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}}}},Pt={HeadBucket:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"}}},HeadApiVersion:{httpMethod:"HEAD",urlPath:"apiversion",parameters:{Bucket:{location:"uri"}}},HeadApiVersionOutput:{parameters:{ApiVersion:{location:"header",sentAs:"x-obs-api"}}},CreateBucket:{httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"storage-class",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"Location"}}},GetBucketMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"}}},GetBucketMetadataOutput:{parameters:{StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},ObsVersion:{location:"header",sentAs:"version",withPrefix:!0},Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}}},DeleteBucket:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"}}},ListBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},ListObjects:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},Marker:{location:"urlPath",sentAs:"marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListObjectsOutput:{data:{type:"xml",xmlRoot:"ListBucketResult"},parameters:{Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Marker:{decode:!0,location:"xml",sentAs:"Marker"},NextMarker:{decode:!0,location:"xml",sentAs:"NextMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},EncodingType:{location:"xml",sentAs:"EncodingType"},Contents:{type:"array",location:"xml",sentAs:"Contents",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},StorageClass:{sentAs:"StorageClass"},Owner:ot}}},CommonPrefixes:st}},ListVersions:{httpMethod:"GET",urlPath:"versions",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},VersionIdMarker:{location:"urlPath",sentAs:"version-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListVersionsOutput:{data:{type:"xml",xmlRoot:"ListVersionsResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},VersionIdMarker:{location:"xml",sentAs:"VersionIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},NextVersionIdMarker:{location:"xml",sentAs:"NextVersionIdMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Versions:{type:"array",location:"xml",sentAs:"Version",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},Owner:ot,StorageClass:{sentAs:"StorageClass"}}}},DeleteMarkers:{type:"array",location:"xml",sentAs:"DeleteMarker",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},Owner:ot}}},CommonPrefixes:st}},PutBackToSource:{httpMethod:"PUT",data:{xmlRoot:"BackToSourceConfiguration"},urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"},BackToSourceRules:at,ContentMD5:{location:"header",sentAs:"Content-MD5"}}},DeleteBackToSource:{httpMethod:"DELETE",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSource:{httpMethod:"GET",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSourceOutput:{data:{type:"xml",xmlRoot:"BackToSourceConfiguration"},parameters:{BackToSourceRules:at}},GetBucketLocation:{httpMethod:"GET",urlPath:"location",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLocationOutput:{data:{type:"xml"},parameters:{Location:{location:"xml",sentAs:"Location"}}},GetBucketStorageInfo:{httpMethod:"GET",urlPath:"storageinfo",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStorageInfoOutput:{data:{type:"xml",xmlRoot:"GetBucketStorageInfoResult"},parameters:{Size:{location:"xml",sentAs:"Size"},ObjectNumber:{location:"xml",sentAs:"ObjectNumber"}}},SetBucketQuota:{httpMethod:"PUT",urlPath:"quota",data:{xmlRoot:"Quota"},parameters:{Bucket:{required:!0,location:"uri"},StorageQuota:{required:!0,location:"xml",sentAs:"StorageQuota"}}},GetBucketQuota:{httpMethod:"GET",urlPath:"quota",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketQuotaOutput:{data:{type:"xml",xmlRoot:"Quota"},parameters:{StorageQuota:{location:"xml",sentAs:"StorageQuota"}}},SetBucketAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:ot,Grants:ct}},GetBucketInventory:{httpMethod:"GET",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"ListInventoryConfiguration"},parameters:{Rules:{type:"array",location:"xml",sentAs:"InventoryConfiguration",items:{type:"object",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}},LastExportTime:{sentAs:"LastExportTime"}}}}}},SetBucketInventory:{httpMethod:"PUT",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"},InventoryConfiguration:wt}},SetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:wt}},DeleteBucketInventory:{httpMethod:"DELETE",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"}}},DeleteBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:wt}},GetBucketAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{Owner:ot,Grants:ct}},SetBucketLoggingConfiguration:{httpMethod:"PUT",urlPath:"logging",data:{xmlRoot:"BucketLoggingStatus",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},LoggingEnabled:ut}},GetBucketLoggingConfiguration:{httpMethod:"GET",urlPath:"logging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLoggingConfigurationOutput:{data:{type:"xml",xmlRoot:"BucketLoggingStatus"},parameters:{Agency:{location:"xml",sentAs:"Agency"},LoggingEnabled:ut}},SetSFSAcl:{httpMethod:"PUT",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetSFSAcl:{httpMethod:"GET",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},GetSFSAclOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteSFSAcl:{httpMethod:"DELETE",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketPolicy:{httpMethod:"PUT",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetBucketPolicy:{httpMethod:"GET",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicyOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteBucketPolicy:{httpMethod:"DELETE",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketDisPolicy:{httpMethod:"PUT",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},GetBucketDisPolicy:{httpMethod:"GET",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},ContentType:{location:"header",sentAs:"Content-Type"}}},GetBucketDisPolicyOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteBucketDisPolicy:{httpMethod:"DELETE",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},SetBucketLifecycleConfiguration:{httpMethod:"PUT",urlPath:"lifecycle",data:{xmlRoot:"LifecycleConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Rules:lt}},GetBucketLifecycleConfiguration:{httpMethod:"GET",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLifecycleConfigurationOutput:{data:{type:"xml",xmlRoot:"LifecycleConfiguration"},parameters:{Rules:lt}},DeleteBucketLifecycleConfiguration:{httpMethod:"DELETE",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketWebsiteConfiguration:{httpMethod:"PUT",urlPath:"website",data:{xmlRoot:"WebsiteConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},RedirectAllRequestsTo:pt,IndexDocument:ht,ErrorDocument:ft,RoutingRules:dt}},GetBucketWebsiteConfiguration:{httpMethod:"GET",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketWebsiteConfigurationOutput:{data:{type:"xml",xmlRoot:"WebsiteConfiguration"},parameters:{RedirectAllRequestsTo:pt,IndexDocument:ht,ErrorDocument:ft,RoutingRules:dt}},DeleteBucketWebsiteConfiguration:{httpMethod:"DELETE",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketVersioningConfiguration:{httpMethod:"PUT",urlPath:"versioning",data:{xmlRoot:"VersioningConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},VersionStatus:{required:!0,location:"xml",sentAs:"Status"}}},GetBucketVersioningConfiguration:{httpMethod:"GET",urlPath:"versioning",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketVersioningConfigurationOutput:{data:{type:"xml",xmlRoot:"VersioningConfiguration"},parameters:{VersionStatus:{location:"xml",sentAs:"Status"}}},SetBucketCors:{httpMethod:"PUT",urlPath:"cors",data:{xmlRoot:"CORSConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},CorsRules:mt}},GetBucketCors:{httpMethod:"GET",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCorsOutput:{data:{type:"xml",xmlRoot:"CORSConfiguration"},parameters:{CorsRules:mt}},DeleteBucketCors:{httpMethod:"DELETE",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketNotification:{httpMethod:"PUT",urlPath:"notification",data:{xmlRoot:"NotificationConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},TopicConfigurations:gt,FunctionGraphConfigurations:yt,FunctionStageConfigurations:At}},GetBucketNotification:{httpMethod:"GET",urlPath:"notification",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketNotificationOutput:{data:{type:"xml",xmlRoot:"NotificationConfiguration"},parameters:{RequestId:{location:"header",sentAs:"x-obs-request-id"},TopicConfigurations:gt,FunctionGraphConfigurations:yt,FunctionStageConfigurations:At}},SetBucketTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Tags:vt}},DeleteBucketTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:vt}},SetBucketStoragePolicy:{httpMethod:"PUT",urlPath:"storageClass",parameters:{Bucket:{required:!0,location:"uri"},StorageClass:{required:!0,location:"xml",type:"adapter",sentAs:"StorageClass"}}},GetBucketStoragePolicy:{httpMethod:"GET",urlPath:"storageClass",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStoragePolicyOutput:{data:{type:"xml"},parameters:{StorageClass:{location:"xml",type:"string",sentAs:"StorageClass"}}},SetBucketReplication:{httpMethod:"PUT",urlPath:"replication",data:{xmlRoot:"ReplicationConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},Rules:xt}},GetBucketReplication:{httpMethod:"GET",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketReplicationOutput:{data:{type:"xml",xmlRoot:"ReplicationConfiguration"},parameters:{Agency:{location:"xml",sentAs:"Agency"},Rules:xt}},DeleteBucketReplication:{httpMethod:"DELETE",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},PutObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},SuccessActionRedirect:{location:"header",sentAs:"success-action-redirect"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},PutObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AppendObject:{httpMethod:"POST",urlPath:"append",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Position:{location:"urlPath",sentAs:"position",type:"number"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},AppendObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},NextPosition:{location:"header",sentAs:"next-append-position",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},GetObject:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ResponseCacheControl:{location:"urlPath",sentAs:"response-cache-control"},ResponseContentDisposition:{location:"urlPath",sentAs:"response-content-disposition"},ResponseContentEncoding:{location:"urlPath",sentAs:"response-content-encoding"},ResponseContentLanguage:{location:"urlPath",sentAs:"response-content-language"},ResponseContentType:{location:"urlPath",sentAs:"response-content-type"},ResponseExpires:{location:"urlPath",sentAs:"response-expires"},VersionId:{location:"urlPath",sentAs:"versionId"},ImageProcess:{location:"urlPath",sentAs:"x-image-process"},IfMatch:{location:"header",sentAs:"If-Match"},IfModifiedSince:{location:"header",sentAs:"If-Modified-Since"},IfNoneMatch:{location:"header",sentAs:"If-None-Match"},IfUnmodifiedSince:{location:"header",sentAs:"If-Unmodified-Since"},Range:{location:"header",sentAs:"Range"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SaveByType:{type:"dstFile"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},GetObjectOutput:{data:{type:"body"},parameters:{Content:{location:"body"},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},ContentLength:{location:"header",sentAs:"Content-Length"},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},CopyObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},CopySource:{required:!0,location:"header",sentAs:"copy-source",withPrefix:!0,skipEncoding:!0},CopySourceIfMatch:{location:"header",sentAs:"copy-source-if-match",withPrefix:!0},CopySourceIfModifiedSince:{location:"header",sentAs:"copy-source-if-modified-since",withPrefix:!0},CopySourceIfNoneMatch:{location:"header",sentAs:"copy-source-if-none-match",withPrefix:!0},CopySourceIfUnmodifiedSince:{location:"header",sentAs:"copy-source-if-unmodified-since",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},ContentEncoding:{location:"header",sentAs:"content-encoding"},ContentLanguage:{location:"header",sentAs:"content-language"},ContentDisposition:{location:"header",sentAs:"content-disposition"},CacheControl:{location:"header",sentAs:"cache-control"},Expires:{location:"header",sentAs:"expires"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},SuccessActionRedirect:{location:"header",sentAs:"success-action-redirect"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyObjectOutput:{data:{type:"xml",xmlRoot:"CopyObjectResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},CopySourceVersionId:{location:"header",sentAs:"copy-source-version-id",withPrefix:!0},ETag:{location:"xml",sentAs:"ETag"},LastModified:{location:"xml",sentAs:"LastModified"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},RestoreObject:{httpMethod:"POST",urlPath:"restore",data:{xmlRoot:"RestoreRequest",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Days:{location:"xml",sentAs:"Days"},Tier:{wrapper:"RestoreJob",location:"xml",sentAs:"Tier"}}},GetObjectMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},GetObjectMetadataOutput:{parameters:{Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},ObjectType:{location:"header",sentAs:"x-obs-object-type"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"x-obs-server-side-encryption-kms-key-id"},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},Expires:{location:"header",sentAs:"Expires"},ReplicationStatus:{location:"header",sentAs:"replication-status",withPrefix:!0}}},SetObjectMetadata:{httpMethod:"PUT",urlPath:"metadata",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},Metadata:{shape:"Sy",location:"header",type:"object",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}}},SetObjectMetadataOutput:{parameters:{Expires:{location:"header",sentAs:"Expires"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},SetObjectAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Delivered:{location:"xml",sentAs:"Delivered"},Owner:ot,Grants:ct}},SetObjectAclOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0}}},GetObjectAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Delivered:{location:"xml",sentAs:"Delivered"},Owner:ot,Grants:ct}},DeleteObject:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObjectOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0}}},DeleteObjects:{httpMethod:"POST",urlPath:"delete",data:{xmlRoot:"Delete",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Quiet:{location:"xml",sentAs:"Quiet"},EncodingType:{location:"xml",sentAs:"EncodingType"},Objects:{required:!0,type:"array",location:"xml",sentAs:"Object",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"}}}}}},DeleteObjectsOutput:{data:{type:"xml",xmlRoot:"DeleteResult"},EncodingType:{location:"xml",sentAs:"EncodingType"},parameters:{Deleteds:{type:"array",location:"xml",sentAs:"Deleted",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},DeleteMarker:{sentAs:"DeleteMarker"},DeleteMarkerVersionId:{sentAs:"DeleteMarkerVersionId"}}}},Errors:{type:"array",location:"xml",sentAs:"Error",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},Code:{sentAs:"Code"},Message:{sentAs:"Message"}}}}}},InitiateMultipartUpload:{httpMethod:"POST",urlPath:"uploads",parameters:{EncodingType:{location:"urlPath",sentAs:"encoding-type"},Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},InitiateMultipartUploadOutput:{data:{type:"xml",xmlRoot:"InitiateMultipartUploadResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListMultipartUploads:{httpMethod:"GET",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Delimiter:{location:"urlPath",sentAs:"delimiter"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxUploads:{type:"number",location:"urlPath",sentAs:"max-uploads"},Prefix:{location:"urlPath",sentAs:"prefix"},UploadIdMarker:{location:"urlPath",sentAs:"upload-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListMultipartUploadsOutput:{data:{type:"xml",xmlRoot:"ListMultipartUploadsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},UploadIdMarker:{location:"xml",sentAs:"UploadIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},NextUploadIdMarker:{location:"xml",sentAs:"NextUploadIdMarker"},MaxUploads:{location:"xml",sentAs:"MaxUploads"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},EncodingType:{location:"xml",sentAs:"EncodingType"},Uploads:{type:"array",location:"xml",sentAs:"Upload",items:{type:"object",parameters:{UploadId:{sentAs:"UploadId"},Key:{decode:!0,sentAs:"Key"},Initiated:{sentAs:"Initiated"},StorageClass:{sentAs:"StorageClass"},Owner:ot,Initiator:it}}},CommonPrefixes:st}},UploadPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,type:"number",location:"urlPath",sentAs:"partNumber"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Body:{location:"body"},SourceFile:{type:"srcFile"},Offset:{type:"plain"},PartSize:{type:"plain"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},UploadPartOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListParts:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},MaxParts:{type:"number",location:"urlPath",sentAs:"max-parts"},PartNumberMarker:{type:"number",location:"urlPath",sentAs:"part-number-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListPartsOutput:{data:{type:"xml",xmlRoot:"ListPartsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},PartNumberMarker:{location:"xml",sentAs:"PartNumberMarker"},NextPartNumberMarker:{location:"xml",sentAs:"NextPartNumberMarker"},MaxParts:{location:"xml",sentAs:"MaxParts"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},StorageClass:{location:"xml",sentAs:"StorageClass"},EncodingType:{location:"urlPath",sentAs:"EncodingType"},Initiator:it,Owner:ot,Parts:{type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"}}}}}},CopyPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,location:"urlPath",sentAs:"partNumber",type:"number"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},CopySource:{required:!0,location:"header",sentAs:"copy-source",skipEncoding:!0,withPrefix:!0},CopySourceRange:{location:"header",sentAs:"copy-source-range",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyPartOutput:{data:{type:"xml",xmlRoot:"CopyPartResult"},parameters:{LastModified:{location:"xml",sentAs:"LastModified"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AbortMultipartUpload:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"}}},CompleteMultipartUpload:{httpMethod:"POST",data:{xmlRoot:"CompleteMultipartUpload"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},EncodingType:{location:"urlPath",sentAs:"encoding-type"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},Parts:{required:!0,type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},ETag:{sentAs:"ETag"}}}},Callback:{location:"header",sentAs:"callback",withPrefix:!0,type:"callback",parameters:{CallbackUrl:{required:!0},CallbackBody:{required:!0},CallbackHost:{},CallbackBodyType:{}}}}},CompleteMultipartUploadOutput:{data:{type:"xml",xmlRoot:"CompleteMultipartUploadResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Location:{location:"xml",sentAs:"Location"},EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}},CallbackResponse:{location:"body",sentAs:"CallbackResponseBody"}},OptionsBucket:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsBucketOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},OptionsObject:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsObjectOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},GetBucketEncryption:{httpMethod:"GET",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:{type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"},ProjectID:{sentAs:"ProjectID"}}}}}}},SetBucketEncryption:{httpMethod:"PUT",urlPath:"encryption",data:{xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:bt}},SetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:bt}},DeleteBucketEncryption:{httpMethod:"DELETE",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:bt}},GetBucketRequesterPay:{httpMethod:"GET",urlPath:"requestPayment",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPay:{httpMethod:"PUT",urlPath:"requestPayment",data:{xmlRoot:"RequestPaymentConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},SetMirrorBackToSource:{httpMethod:"PUT",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},SetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},GetMirrorBackToSource:{httpMethod:"GET",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteMirrorBackToSource:{httpMethod:"DELETE",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetBucketDirectColdAccess:{httpMethod:"GET",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccess:{httpMethod:"PUT",urlPath:"directcoldaccess",data:{xmlRoot:"DirectColdAccessConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Status:{required:!0,location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},DeleteBucketDirectColdAccess:{httpMethod:"DELETE",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},GetBucketCustomDomain:{httpMethod:"GET",urlPath:"customdomain",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCustomDomainOutput:{data:{type:"xml",xmlRoot:"ListBucketCustomDomainsResult"},parameters:{Domains:{location:"xml",type:"array",sentAs:"Domains",items:{type:"object",parameters:{DomainName:{sentAs:"DomainName"},Value:{sentAs:"CreateTime"}}}}}},SetBucketCustomDomain:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}}},DeleteBucketCustomDomain:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}}},GetCDNNotifyConfiguration:{httpMethod:"GET",urlPath:"CDNNotifyConfiguration",parameters:{Bucket:{required:!0,location:"uri"}}},GetCDNNotifyConfigurationOutput:{data:{type:"xml",xmlRoot:"CDNNotifyConfiguration"},parameters:{Domain:{location:"xml",type:"array",sentAs:"Domain",items:{type:"object",parameters:{Name:{sentAs:"Name"},Events:{type:"array",items:{type:"adapter"},sentAs:"Event"}}}}}},SetCdnNotifyConfiguration:{httpMethod:"PUT",urlPath:"CDNNotifyConfiguration",data:{xmlRoot:"CDNNotifyConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},Domain:{location:"xml",sentAs:"Domain",type:"array",items:{type:"object",parameters:{Name:{type:"string",sentAs:"Name"},Event:{type:"array",items:{type:"adapter"},sentAs:"Event"}}}}}},GetQuota:{httpMethod:"GET",urlPath:"quota"},GetQuotaOutput:{data:{type:"xml",xmlRoot:"MaxBucketNumber"},parameters:{Size:{location:"xml",sentAs:"Number"}}},GetWorkflowTrigger:{httpMethod:"GET",urlPath:"obsworkflowtriggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetWorkflowTriggerOutput:{data:{type:"body"},parameters:{rules:{location:"body"}}},DeleteWorkflowTrigger:{httpMethod:"DELETE",urlPath:"obsworkflowtriggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},CreateWorkflowTrigger:{httpMethod:"PUT",urlPath:"obsworkflowtriggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rule:{required:!0,location:"body"}}},RestoreFailedWorkflowExecution:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"execution_name"},GraphName:{required:!0,location:"urlPath",sentAs:"x-workflow-graph-name"}}},CreateTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Template:{required:!0,location:"body"}}},CreateWorkflow:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"graph_name"},Workflow_create:{location:"urlPath",sentAs:"x-workflow-create"},Workflow:{required:!0,location:"body"}}},DeleteWorkflow:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"}}},UpdateWorkflow:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"},Graph_name:{required:!0,location:"body"}}},GetWorkflowList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name_prefix"},XObsLimit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsStart:{type:"number",location:"urlPath",sentAs:"x-workflow-start"}}},GetWorkflowListOutput:{data:{type:"body"},parameters:{workflows:{location:"body"}}},GetWorkflowTemplateList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowTemplateListOutput:{data:{type:"body"},parameters:{templates:{location:"body"}}},GetWorkflowInstanceList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"execution_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},Graph_name:{location:"urlPath",sentAs:"x-workflow-graph-name"},State:{location:"urlPath",sentAs:"x-workflow-execution-state"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowInstanceListOutput:{data:{type:"body"},parameters:{instances:{location:"body"}}},DeleteTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name"}}},GetActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsCategory:{type:"String",location:"urlPath",sentAs:"x-workflow-category"}}},GetActionTemplatesOutput:{data:{type:"body"},parameters:{actions:{location:"body"}}},GetWorkflowAuthorization:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},GetWorkflowAuthorizationOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAuthorization:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"}}},CreateOnlineDecom:{httpMethod:"PUT",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"},Decom:{required:!0,location:"body"}}},GetOnlineDecom:{httpMethod:"GET",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetOnlineDecomOutput:{data:{type:"body"},parameters:{Decom:{location:"body"}}},DeleteOnlineDecom:{httpMethod:"DELETE",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetPublicationTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetPublicationTemplatesOutput:{data:{type:"body"},parameters:{PublishedTemplates:{location:"body"}}},GetPublicationTemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetPublicationTemplateDetailOutput:{data:{type:"body"},parameters:{PublishTemplate:{location:"body"}}},GetWorkflowAgreements:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},GetWorkflowAgreementsOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAgreements:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},CreateMyActionTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},CreateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},GetMyActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetMyActionTemplatesOutput:{data:{type:"body"},parameters:{ActionTemplates:{location:"body"}}},GetMyactiontemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetMyactiontemplateDetailOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},UpdateMyActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},UpdateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},DeleteMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},ForbidMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},XObsForbid:{location:"urlPath",sentAs:"x-workflow-forbid"}}},UpdatePublicActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},PublicAction:{required:!0,location:"body"}}},GetOmPublicActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetOmPublicActionTemplatesOutput:{data:{type:"body"},parameters:{Templates:{location:"body"}}},SetBucketAlias:{httpMethod:"PUT",urlPath:"obsbucketalias",data:{xmlRoot:"CreateBucketAlias"},parameters:{Bucket:{required:!0,location:"uri"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}},GetBucketAlias:{httpMethod:"GET",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAliasOutput:{data:{type:"xml",xmlRoot:"AliasList"},parameters:{BucketAlias:{location:"xml",type:"object",sentAs:"BucketAlias",parameters:{Alias:{sentAs:"Alias"},BucketList:{sentAs:"Bucket",location:"xml",type:"array",wrapper:"BucketList",items:{type:"string"}}}}}},DeleteBucketAlias:{httpMethod:"DELETE",urlPath:"obsbucketalias",parameters:{Bucket:{required:!0,location:"uri"}}},BindBucketAlias:{httpMethod:"PUT",urlPath:"obsalias",data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},BindBucketAliasOutput:{data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},UnbindBucketAlias:{httpMethod:"DELETE",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},ListBucketsAlias:{httpMethod:"GET",urlPath:"obsbucketalias"},ListBucketsAliasOutput:{data:{type:"xml",xmlRoot:"ListBucketAliasResult"},parameters:{BucketAliasList:{location:"xml",sentAs:"BucketAliasList",type:"object",parameters:{BucketAlias:{location:"xml",type:"array",sentAs:"BucketAlias",items:{type:"object",parameters:{Alias:{sentAs:"Alias"},CreationDate:{sentAs:"CreationDate"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}}}}},Owner:{location:"xml",sentAs:"Owner",type:"object",parameters:{ID:{sentAs:"ID"}}}}}},kt={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},St={type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"},ProjectID:{sentAs:"ProjectID"}}}}},Ct={type:"object",location:"xml",sentAs:"InventoryConfiguration",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"},Encryption:{type:"object",sentAs:"Encryption",parameters:{"SSE-KMS":{type:"object",sentAs:"SSE-KMS",parameters:{KeyId:{sentAs:"KeyId",type:"adapter"}}}}}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}}}},Tt={type:"object",location:"xml",sentAs:"Initiator",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},Et={sentAs:"BackToSourceRule",required:!0,location:"xml",type:"array",items:{type:"object",parameters:{ID:{sentAs:"ID"},Condition:{sentAs:"Condition",type:"object",parameters:{ObjectKeyPrefixEquals:{sentAs:"ObjectKeyPrefixEquals"},HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"}}},Redirect:{sentAs:"Redirect",type:"object",parameters:{HttpRedirectCode:{sentAs:"HttpRedirectCode"},SourceEndpoint:{sentAs:"SourceEndpoint"},SourceBucketName:{sentAs:"SourceBucketName"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"},StaticUri:{sentAs:"StaticUri"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},MigrationConfiguration:{sentAs:"MigrationConfiguration",type:"object",parameters:{Agency:{sentAs:"Agency"},LogBucketName:{sentAs:"LogBucketName"},PrivateBucketConfiguration:{sentAs:"PrivateBucketConfiguration",type:"object",parameters:{SourceStorageProvider:{sentAs:"SourceStorageProvider"},SourceBucketAK:{sentAs:"SourceBucketAK"},SourceBucketSK:{sentAs:"SourceBucketSK"},SourceBucketZone:{sentAs:"SourceBucketZone"}}}}}}}}}},Mt={type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{decode:!0,sentAs:"Prefix"},MTime:{sentAs:"MTime"},InodeNo:{sentAs:"InodeNo"}}}},Ot={type:"array",location:"xml",wrapper:"AccessControlList",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{data:{xsiNamespace:"http://www.w3.org/2001/XMLSchema-instance",xsiType:"Type"},type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID",notAllowEmpty:!0},Name:{sentAs:"DisplayName"},URI:{sentAs:"URI",type:"adapter",notAllowEmpty:!0}}},Permission:{sentAs:"Permission"}}}},Bt={type:"object",location:"xml",sentAs:"LoggingEnabled",parameters:{TargetBucket:{sentAs:"TargetBucket"},TargetPrefix:{sentAs:"TargetPrefix"},TargetGrants:{type:"array",wrapper:"TargetGrants",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{data:{xsiNamespace:"http://www.w3.org/2001/XMLSchema-instance",xsiType:"Type"},type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"},URI:{sentAs:"URI",type:"adapter"}}},Permission:{sentAs:"Permission"}}}}}},Rt={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Transitions:{type:"array",sentAs:"Transition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}}},Expiration:{type:"object",sentAs:"Expiration",parameters:{Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}},AbortIncompleteMultipartUpload:{type:"object",sentAs:"AbortIncompleteMultipartUpload",parameters:{DaysAfterInitiation:{type:"number",sentAs:"DaysAfterInitiation"}}},NoncurrentVersionTransitions:{type:"array",sentAs:"NoncurrentVersionTransition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}},NoncurrentVersionExpiration:{type:"object",sentAs:"NoncurrentVersionExpiration",parameters:{NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}}}},It={type:"object",location:"xml",sentAs:"RedirectAllRequestsTo",parameters:{HostName:{sentAs:"HostName"},Protocol:{sentAs:"Protocol"}}},Dt={type:"array",wrapper:"RoutingRules",location:"xml",sentAs:"RoutingRule",items:{type:"object",parameters:{Condition:{type:"object",sentAs:"Condition",parameters:{HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"},KeyPrefixEquals:{sentAs:"KeyPrefixEquals"}}},Redirect:{type:"object",sentAs:"Redirect",parameters:{HostName:{sentAs:"HostName"},HttpRedirectCode:{sentAs:"HttpRedirectCode"},Protocol:{sentAs:"Protocol"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"}}}}}},jt={type:"object",location:"xml",sentAs:"IndexDocument",parameters:{Suffix:{sentAs:"Suffix"}}},Lt={type:"object",location:"xml",sentAs:"ErrorDocument",parameters:{Key:{sentAs:"Key"}}},_t={required:!0,type:"array",location:"xml",sentAs:"CORSRule",items:{type:"object",parameters:{ID:{sentAs:"ID"},AllowedMethod:{type:"array",sentAs:"AllowedMethod",items:{type:"string"}},AllowedOrigin:{type:"array",sentAs:"AllowedOrigin",items:{type:"string"}},AllowedHeader:{type:"array",sentAs:"AllowedHeader",items:{type:"string"}},MaxAgeSeconds:{type:"number",sentAs:"MaxAgeSeconds"},ExposeHeader:{type:"array",sentAs:"ExposeHeader",items:{type:"string"}}}}},qt={type:"array",location:"xml",sentAs:"FunctionGraphConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"S3Key",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionGraph:{},Event:{type:"array",items:{type:"adapter"}}}}},Nt={type:"array",location:"xml",sentAs:"FunctionStageConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"S3Key",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionStage:{},Event:{type:"array",items:{type:"adapter"}}}}},Ft={type:"array",location:"xml",sentAs:"TopicConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"S3Key",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},Topic:{},Event:{type:"array",items:{type:"adapter"}}}}},Ut={required:!0,type:"array",location:"xml",wrapper:"TagSet",sentAs:"Tag",items:{type:"object",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}},Kt={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Destination:{type:"object",sentAs:"Destination",parameters:{Bucket:{sentAs:"Bucket",type:"adapter"},StorageClass:{sentAs:"StorageClass",type:"adapter"},DeleteData:{sentAs:"DeleteData",type:"string"}}},HistoricalObjectReplication:{sentAs:"HistoricalObjectReplication"}}}},Gt={HeadBucket:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"}}},HeadApiVersion:{httpMethod:"HEAD",urlPath:"apiversion",parameters:{Bucket:{location:"uri"}}},HeadApiVersionOutput:{parameters:{ApiVersion:{location:"header",sentAs:"x-obs-api"}}},CreateBucket:{httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"x-default-storage-class"},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"LocationConstraint"}}},GetBucketMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"}}},GetBucketMetadataOutput:{parameters:{StorageClass:{location:"header",sentAs:"x-default-storage-class"},ObsVersion:{location:"header",sentAs:"x-obs-version"},Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}}},DeleteBucket:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"}}},ListBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},ListObjects:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},Marker:{location:"urlPath",sentAs:"marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListObjectsOutput:{data:{type:"xml",xmlRoot:"ListBucketResult"},parameters:{Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Marker:{decode:!0,location:"xml",sentAs:"Marker"},NextMarker:{decode:!0,location:"xml",sentAs:"NextMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},EncodingType:{location:"xml",sentAs:"EncodingType"},Contents:{type:"array",location:"xml",sentAs:"Contents",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},StorageClass:{sentAs:"StorageClass"},Owner:kt}}},CommonPrefixes:Mt}},ListVersions:{httpMethod:"GET",urlPath:"versions",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},VersionIdMarker:{location:"urlPath",sentAs:"version-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListVersionsOutput:{data:{type:"xml",xmlRoot:"ListVersionsResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},VersionIdMarker:{location:"xml",sentAs:"VersionIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},NextVersionIdMarker:{location:"xml",sentAs:"NextVersionIdMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Versions:{type:"array",location:"xml",sentAs:"Version",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},Owner:kt,StorageClass:{sentAs:"StorageClass"}}}},DeleteMarkers:{type:"array",location:"xml",sentAs:"DeleteMarker",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},Owner:kt}}},CommonPrefixes:Mt}},PutBackToSource:{httpMethod:"PUT",data:{xmlRoot:"BackToSourceConfiguration"},urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"},BackToSourceRules:Et,ContentMD5:{location:"header",sentAs:"Content-MD5"}}},DeleteBackToSource:{httpMethod:"DELETE",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSource:{httpMethod:"GET",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSourceOutput:{data:{type:"xml",xmlRoot:"BackToSourceConfiguration"},parameters:{BackToSourceRules:Et}},GetBucketLocation:{httpMethod:"GET",urlPath:"location",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLocationOutput:{data:{type:"xml",xmlRoot:"CreateBucketConfiguration"},parameters:{Location:{location:"xml",sentAs:"LocationConstraint"}}},GetBucketStorageInfo:{httpMethod:"GET",urlPath:"storageinfo",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStorageInfoOutput:{data:{type:"xml",xmlRoot:"GetBucketStorageInfoResult"},parameters:{Size:{location:"xml",sentAs:"Size"},ObjectNumber:{location:"xml",sentAs:"ObjectNumber"}}},SetBucketQuota:{httpMethod:"PUT",urlPath:"quota",data:{xmlRoot:"Quota"},parameters:{Bucket:{required:!0,location:"uri"},StorageQuota:{required:!0,location:"xml",sentAs:"StorageQuota"}}},GetBucketQuota:{httpMethod:"GET",urlPath:"quota",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketQuotaOutput:{data:{type:"xml",xmlRoot:"Quota"},parameters:{StorageQuota:{location:"xml",sentAs:"StorageQuota"}}},SetBucketAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:kt,Grants:Ot}},GetBucketAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{Owner:kt,Grants:Ot}},SetBucketLoggingConfiguration:{httpMethod:"PUT",urlPath:"logging",data:{xmlRoot:"BucketLoggingStatus",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},LoggingEnabled:Bt}},GetBucketLoggingConfiguration:{httpMethod:"GET",urlPath:"logging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLoggingConfigurationOutput:{data:{type:"xml",xmlRoot:"BucketLoggingStatus"},parameters:{LoggingEnabled:Bt}},SetBucketPolicy:{httpMethod:"PUT",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},SetSFSAcl:{httpMethod:"PUT",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetSFSAcl:{httpMethod:"GET",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},GetSFSAclOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteSFSAcl:{httpMethod:"DELETE",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicy:{httpMethod:"GET",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicyOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteBucketPolicy:{httpMethod:"DELETE",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketDisPolicy:{httpMethod:"PUT",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},GetBucketDisPolicy:{httpMethod:"GET",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},ContentType:{location:"header",sentAs:"Content-Type"}}},GetBucketDisPolicyOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteBucketDisPolicy:{httpMethod:"DELETE",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},SetBucketLifecycleConfiguration:{httpMethod:"PUT",urlPath:"lifecycle",data:{xmlRoot:"LifecycleConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Rules:Rt}},GetBucketLifecycleConfiguration:{httpMethod:"GET",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLifecycleConfigurationOutput:{data:{type:"xml",xmlRoot:"LifecycleConfiguration"},parameters:{Rules:Rt}},DeleteBucketLifecycleConfiguration:{httpMethod:"DELETE",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketWebsiteConfiguration:{httpMethod:"PUT",urlPath:"website",data:{xmlRoot:"WebsiteConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},RedirectAllRequestsTo:It,IndexDocument:jt,ErrorDocument:Lt,RoutingRules:Dt}},GetBucketWebsiteConfiguration:{httpMethod:"GET",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketWebsiteConfigurationOutput:{data:{type:"xml",xmlRoot:"WebsiteConfiguration"},parameters:{RedirectAllRequestsTo:It,IndexDocument:jt,ErrorDocument:Lt,RoutingRules:Dt}},DeleteBucketWebsiteConfiguration:{httpMethod:"DELETE",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketVersioningConfiguration:{httpMethod:"PUT",urlPath:"versioning",data:{xmlRoot:"VersioningConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},VersionStatus:{required:!0,location:"xml",sentAs:"Status"}}},GetBucketVersioningConfiguration:{httpMethod:"GET",urlPath:"versioning",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketVersioningConfigurationOutput:{data:{type:"xml",xmlRoot:"VersioningConfiguration"},parameters:{VersionStatus:{location:"xml",sentAs:"Status"}}},SetBucketCors:{httpMethod:"PUT",urlPath:"cors",data:{xmlRoot:"CORSConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},CorsRules:_t}},GetBucketCors:{httpMethod:"GET",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCorsOutput:{data:{type:"xml",xmlRoot:"CORSConfiguration"},parameters:{CorsRules:_t}},DeleteBucketCors:{httpMethod:"DELETE",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketNotification:{httpMethod:"PUT",urlPath:"notification",data:{xmlRoot:"NotificationConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},TopicConfigurations:Ft,FunctionGraphConfigurations:qt,FunctionStageConfigurations:Nt}},GetBucketNotification:{httpMethod:"GET",urlPath:"notification",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketNotificationOutput:{data:{type:"xml",xmlRoot:"NotificationConfiguration"},parameters:{TopicConfigurations:Ft,FunctionGraphConfigurations:qt,FunctionStageConfigurations:Nt}},SetBucketTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Tags:Ut}},DeleteBucketTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:Ut}},SetBucketStoragePolicy:{httpMethod:"PUT",urlPath:"storagePolicy",data:{xmlRoot:"StoragePolicy"},parameters:{Bucket:{required:!0,location:"uri"},StorageClass:{required:!0,location:"xml",type:"adapter",sentAs:"DefaultStorageClass"}}},GetBucketStoragePolicy:{httpMethod:"GET",urlPath:"storagePolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStoragePolicyOutput:{data:{type:"xml",xmlRoot:"StoragePolicy"},parameters:{StorageClass:{location:"xml",type:"string",sentAs:"DefaultStorageClass"}}},SetBucketReplication:{httpMethod:"PUT",urlPath:"replication",data:{xmlRoot:"ReplicationConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},Rules:Kt}},GetBucketReplication:{httpMethod:"GET",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketReplicationOutput:{data:{type:"xml",xmlRoot:"ReplicationConfiguration"},parameters:{Agency:{location:"xml",sentAs:"Agency"},Rules:Kt}},DeleteBucketReplication:{httpMethod:"DELETE",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},PutObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},PutObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AppendObject:{httpMethod:"POST",urlPath:"append",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Position:{location:"urlPath",sentAs:"position",type:"number"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Offset:{type:"plain"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},AppendObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},GetObject:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ResponseCacheControl:{location:"urlPath",sentAs:"response-cache-control"},ResponseContentDisposition:{location:"urlPath",sentAs:"response-content-disposition"},ResponseContentEncoding:{location:"urlPath",sentAs:"response-content-encoding"},ResponseContentLanguage:{location:"urlPath",sentAs:"response-content-language"},ResponseContentType:{location:"urlPath",sentAs:"response-content-type"},ResponseExpires:{location:"urlPath",sentAs:"response-expires"},VersionId:{location:"urlPath",sentAs:"versionId"},ImageProcess:{location:"urlPath",sentAs:"x-image-process"},IfMatch:{location:"header",sentAs:"If-Match"},IfModifiedSince:{location:"header",sentAs:"If-Modified-Since"},IfNoneMatch:{location:"header",sentAs:"If-None-Match"},IfUnmodifiedSince:{location:"header",sentAs:"If-Unmodified-Since"},Range:{location:"header",sentAs:"Range"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SaveByType:{type:"dstFile"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},GetObjectOutput:{data:{type:"body"},parameters:{Content:{location:"body"},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},ContentLength:{location:"header",sentAs:"Content-Length"},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},CopyObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},CopySource:{required:!0,location:"header",sentAs:"copy-source",withPrefix:!0,skipEncoding:!0},CopySourceIfMatch:{location:"header",sentAs:"copy-source-if-match",withPrefix:!0},CopySourceIfModifiedSince:{location:"header",sentAs:"copy-source-if-modified-since",withPrefix:!0},CopySourceIfNoneMatch:{location:"header",sentAs:"copy-source-if-none-match",withPrefix:!0},CopySourceIfUnmodifiedSince:{location:"header",sentAs:"copy-source-if-unmodified-since",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},ContentEncoding:{location:"header",sentAs:"content-encoding"},ContentLanguage:{location:"header",sentAs:"content-language"},ContentDisposition:{location:"header",sentAs:"content-disposition"},CacheControl:{location:"header",sentAs:"cache-control"},Expires:{location:"header",sentAs:"expires"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyObjectOutput:{data:{type:"xml",xmlRoot:"CopyObjectResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},CopySourceVersionId:{location:"header",sentAs:"copy-source-version-id",withPrefix:!0},ETag:{location:"xml",sentAs:"ETag"},LastModified:{location:"xml",sentAs:"LastModified"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},RestoreObject:{httpMethod:"POST",urlPath:"restore",data:{xmlRoot:"RestoreRequest",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Days:{location:"xml",sentAs:"Days"},Tier:{wrapper:"GlacierJobParameters",location:"xml",sentAs:"Tier"}}},GetObjectMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},GetObjectMetadataOutput:{parameters:{Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},ObjectType:{location:"header",sentAs:"x-obs-object-type"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},Expires:{location:"header",sentAs:"Expires"},ReplicationStatus:{location:"header",sentAs:"replication-status",withPrefix:!0}}},SetObjectMetadata:{httpMethod:"PUT",urlPath:"metadata",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},Metadata:{shape:"Sy",location:"header",type:"object",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}}},SetObjectMetadataOutput:{parameters:{Expires:{location:"header",sentAs:"Expires"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},SetObjectAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:kt,Grants:Ot}},SetObjectAclOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0}}},GetObjectAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Owner:kt,Grants:Ot}},DeleteObject:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObjectOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0}}},DeleteObjects:{httpMethod:"POST",urlPath:"delete",data:{xmlRoot:"Delete",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Quiet:{location:"xml",sentAs:"Quiet"},EncodingType:{location:"xml",sentAs:"EncodingType"},Objects:{required:!0,type:"array",location:"xml",sentAs:"Object",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"}}}}}},DeleteObjectsOutput:{data:{type:"xml",xmlRoot:"DeleteResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Deleteds:{type:"array",location:"xml",sentAs:"Deleted",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},DeleteMarker:{sentAs:"DeleteMarker"},DeleteMarkerVersionId:{sentAs:"DeleteMarkerVersionId"}}}},Errors:{type:"array",location:"xml",sentAs:"Error",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},Code:{sentAs:"Code"},Message:{sentAs:"Message"}}}}}},InitiateMultipartUpload:{httpMethod:"POST",urlPath:"uploads",parameters:{EncodingType:{location:"urlPath",sentAs:"encoding-type"},Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},ContentType:{location:"header",sentAs:"Content-Type"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},InitiateMultipartUploadOutput:{data:{type:"xml",xmlRoot:"InitiateMultipartUploadResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListMultipartUploads:{httpMethod:"GET",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Delimiter:{location:"urlPath",sentAs:"delimiter"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxUploads:{type:"number",location:"urlPath",sentAs:"max-uploads"},Prefix:{location:"urlPath",sentAs:"prefix"},UploadIdMarker:{location:"urlPath",sentAs:"upload-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListMultipartUploadsOutput:{data:{type:"xml",xmlRoot:"ListMultipartUploadsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},UploadIdMarker:{location:"xml",sentAs:"UploadIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},NextUploadIdMarker:{location:"xml",sentAs:"NextUploadIdMarker"},MaxUploads:{location:"xml",sentAs:"MaxUploads"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},EncodingType:{location:"xml",sentAs:"EncodingType"},Uploads:{type:"array",location:"xml",sentAs:"Upload",items:{type:"object",parameters:{UploadId:{sentAs:"UploadId"},Key:{decode:!0,sentAs:"Key"},Initiated:{sentAs:"Initiated"},StorageClass:{sentAs:"StorageClass"},Owner:kt,Initiator:Tt}}},CommonPrefixes:Mt}},UploadPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,type:"number",location:"urlPath",sentAs:"partNumber"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Body:{location:"body"},SourceFile:{type:"srcFile"},Offset:{type:"plain"},PartSize:{type:"plain"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},UploadPartOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListParts:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},MaxParts:{type:"number",location:"urlPath",sentAs:"max-parts"},PartNumberMarker:{type:"number",location:"urlPath",sentAs:"part-number-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListPartsOutput:{data:{type:"xml",xmlRoot:"ListPartsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},PartNumberMarker:{location:"xml",sentAs:"PartNumberMarker"},NextPartNumberMarker:{location:"xml",sentAs:"NextPartNumberMarker"},MaxParts:{location:"xml",sentAs:"MaxParts"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},StorageClass:{location:"xml",sentAs:"StorageClass"},EncodingType:{location:"urlPath",sentAs:"EncodingType"},Initiator:Tt,Owner:kt,Parts:{type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"}}}}}},CopyPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,location:"urlPath",sentAs:"partNumber",type:"number"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},CopySource:{required:!0,location:"header",sentAs:"copy-source",skipEncoding:!0,withPrefix:!0},CopySourceRange:{location:"header",sentAs:"copy-source-range",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyPartOutput:{data:{type:"xml",xmlRoot:"CopyPartResult"},parameters:{LastModified:{location:"xml",sentAs:"LastModified"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AbortMultipartUpload:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"}}},CompleteMultipartUpload:{httpMethod:"POST",data:{xmlRoot:"CompleteMultipartUpload"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},EncodingType:{location:"urlPath",sentAs:"encoding-type"},Parts:{required:!0,type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},ETag:{sentAs:"ETag"}}}},Callback:{location:"header",sentAs:"callback",withPrefix:!0,type:"callback",parameters:{CallbackUrl:{required:!0},CallbackBody:{required:!0},CallbackHost:{},CallbackBodyType:{}}}}},CompleteMultipartUploadOutput:{data:{type:"xml",xmlRoot:"CompleteMultipartUploadResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Location:{location:"xml",sentAs:"Location"},EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}},CallbackResponse:{location:"body",sentAs:"CallbackResponseBody"}},GetBucketInventory:{httpMethod:"GET",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"ListInventoryConfiguration"},parameters:{Rules:{type:"array",location:"xml",sentAs:"InventoryConfiguration",items:{type:"object",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}},LastExportTime:{sentAs:"LastExportTime"}}}}}},SetBucketInventory:{httpMethod:"PUT",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"},InventoryConfiguration:Ct}},SetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:Ct}},DeleteInventory:{httpMethod:"DELETE",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"}}},DeleteInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:Ct}},GetBucketEncryption:{httpMethod:"GET",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:{type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"},ProjectID:{sentAs:"ProjectID"}}}}}}},SetBucketEncryption:{httpMethod:"PUT",urlPath:"encryption",data:{xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:St}},SetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:St}},DeleteBucketEncryption:{httpMethod:"DELETE",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:St}},GetBucketRequesterPay:{httpMethod:"GET",urlPath:"requestPayment",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPay:{httpMethod:"PUT",urlPath:"requestPayment",data:{xmlRoot:"RequestPaymentConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},OptionsBucket:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsBucketOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},OptionsObject:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsObjectOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},SetMirrorBackToSource:{httpMethod:"PUT",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},SetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},GetMirrorBackToSource:{httpMethod:"GET",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},deleteMirrorBackToSource:{httpMethod:"DELETE",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetBucketDirectColdAccess:{httpMethod:"GET",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccess:{httpMethod:"PUT",urlPath:"directcoldaccess",data:{xmlRoot:"DirectColdAccessConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Status:{required:!0,location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},DeleteBucketDirectColdAccess:{httpMethod:"DELETE",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},GetBucketCustomDomain:{httpMethod:"GET",urlPath:"customdomain",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCustomDomainOutput:{data:{type:"xml",xmlRoot:"ListBucketCustomDomainsResult"},parameters:{Domains:{location:"xml",type:"array",sentAs:"Domains",items:{type:"object",parameters:{DomainName:{sentAs:"DomainName"},Value:{sentAs:"CreateTime"}}}}}},SetBucketCustomDomain:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}}},DeleteBucketCustomDomain:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}}},GetCDNNotifyConfiguration:{httpMethod:"GET",urlPath:"CDNNotifyConfiguration",parameters:{Bucket:{required:!0,location:"uri"}}},GetCDNNotifyConfigurationOutput:{data:{type:"xml",xmlRoot:"CDNNotifyConfiguration"},parameters:{Domain:{location:"xml",type:"array",sentAs:"Domain",items:{type:"object",parameters:{Name:{sentAs:"Name"},Events:{type:"array",items:{type:"adapter"},sentAs:"Event"}}}}}},SetCdnNotifyConfiguration:{httpMethod:"PUT",urlPath:"CDNNotifyConfiguration",data:{xmlRoot:"CDNNotifyConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},Domain:{location:"xml",sentAs:"Domain",type:"array",items:{type:"object",parameters:{Name:{type:"string",sentAs:"Name"},Event:{type:"array",items:{type:"adapter"},sentAs:"Event"}}}}}},GetQuota:{httpMethod:"GET",urlPath:"quota"},GetQuotaOutput:{data:{type:"xml",xmlRoot:"MaxBucketNumber"},parameters:{Size:{location:"xml",sentAs:"Number"}}},GetWorkflowTrigger:{httpMethod:"GET",urlPath:"triggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetWorkflowTriggerOutput:{data:{type:"body"},parameters:{rules:{location:"body"}}},DeleteWorkflowTrigger:{httpMethod:"DELETE",urlPath:"triggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},CreateWorkflowTrigger:{httpMethod:"PUT",urlPath:"triggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rule:{required:!0,location:"body"}}},RestoreFailedWorkflowExecution:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"execution_name"},GraphName:{required:!0,location:"urlPath",sentAs:"x-workflow-graph-name"}}},CreateTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Template:{required:!0,location:"body"}}},CreateWorkflow:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"graph_name"},Workflow_create:{location:"urlPath",sentAs:"x-workflow-create"},Workflow:{required:!0,location:"body"}}},DeleteWorkflow:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"}}},UpdateWorkflow:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"},Graph_name:{required:!0,location:"body"}}},GetWorkflowList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name_prefix"},XObsLimit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsStart:{type:"number",location:"urlPath",sentAs:"x-workflow-start"}}},GetWorkflowListOutput:{data:{type:"body"},parameters:{workflows:{location:"body"}}},GetWorkflowTemplateList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowTemplateListOutput:{data:{type:"body"},parameters:{templates:{location:"body"}}},GetWorkflowInstanceList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"execution_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},Graph_name:{location:"urlPath",sentAs:"x-workflow-graph-name"},State:{location:"urlPath",sentAs:"x-workflow-execution-state"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowInstanceListOutput:{data:{type:"body"},parameters:{instances:{location:"body"}}},DeleteTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name"}}},GetActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsCategory:{type:"String",location:"urlPath",sentAs:"x-workflow-category"}}},GetActionTemplatesOutput:{data:{type:"body"},parameters:{templates:{location:"body"}}},GetWorkflowAuthorization:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},GetWorkflowAuthorizationOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAuthorization:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"}}},CreateOnlineDecom:{httpMethod:"PUT",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"},Decom:{required:!0,location:"body"}}},GetOnlineDecom:{httpMethod:"GET",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetOnlineDecomOutput:{data:{type:"body"},parameters:{Decom:{location:"body"}}},DeleteOnlineDecom:{httpMethod:"DELETE",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetPublicationTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetPublicationTemplatesOutput:{data:{type:"body"},parameters:{PublishedTemplates:{location:"body"}}},GetPublicationTemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetPublicationTemplateDetailOutput:{data:{type:"body"},parameters:{PublishTemplate:{location:"body"}}},GetWorkflowAgreements:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},GetWorkflowAgreementsOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAgreements:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},CreateMyActionTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},CreateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},GetMyActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetMyActionTemplatesOutput:{data:{type:"body"},parameters:{ActionTemplates:{location:"body"}}},GetMyactiontemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetMyactiontemplateDetailOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},UpdateMyActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},UpdateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},DeleteMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},ForbidMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},XObsForbid:{location:"urlPath",sentAs:"x-workflow-forbid"}}},UpdatePublicActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},PublicAction:{required:!0,location:"body"}}},GetOmPublicActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetOmPublicActionTemplatesOutput:{data:{type:"body"},parameters:{Templates:{location:"body"}}},SetBucketAlias:{httpMethod:"PUT",urlPath:"obsbucketalias",data:{xmlRoot:"CreateBucketAlias"},parameters:{Bucket:{required:!0,location:"uri"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}},GetBucketAlias:{httpMethod:"GET",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAliasOutput:{data:{type:"xml",xmlRoot:"AliasList"},parameters:{BucketAlias:{location:"xml",type:"object",sentAs:"BucketAlias",parameters:{Alias:{sentAs:"Alias"},BucketList:{sentAs:"Bucket",location:"xml",type:"array",wrapper:"BucketList",items:{type:"string"}}}}}},DeleteBucketAlias:{httpMethod:"DELETE",urlPath:"obsbucketalias",parameters:{Bucket:{required:!0,location:"uri"}}},BindBucketAlias:{httpMethod:"PUT",urlPath:"obsalias",data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},BindBucketAliasOutput:{data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},UnbindBucketAlias:{httpMethod:"DELETE",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},ListBucketsAlias:{httpMethod:"GET",urlPath:"obsbucketalias"},ListBucketsAliasOutput:{data:{type:"xml",xmlRoot:"ListBucketAliasResult"},parameters:{BucketAliasList:{location:"xml",sentAs:"BucketAliasList",type:"object",parameters:{BucketAlias:{location:"xml",type:"array",sentAs:"BucketAlias",items:{type:"object",parameters:{Alias:{sentAs:"Alias"},CreationDate:{sentAs:"CreationDate"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}}}}},Owner:{location:"xml",sentAs:"Owner",type:"object",parameters:{ID:{sentAs:"ID"}}}}}};function Ht(e){return Ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ht(e)}function zt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Wt=function(e,t){var r=new Ae("sha1"===e?"SHA-1":"sha512"===e?"SHA-512":"SHA-256","TEXT");return r.setHMACKey(t,t instanceof ArrayBuffer?"ARRAYBUFFER":"TEXT"),{update:function(e){return r.update(e),this},digest:function(e){return"hex"===e?r.getHMAC("HEX"):"base64"===e?r.getHMAC("B64"):r.getHMAC("ARRAYBUFFER")}}},Vt=function(e){if("md5"===e)return{update:function(e){return this.message?this.message+=e:this.message=e,this},digest:function(e){return"hex"===e?rt()(this.message):"base64"===e||"rawbase64"===e?(window.btoa?window.btoa:et.encode)(rt()(this.message,!1,!0)):rt()(this.message,!1,!0)}};var t=new Ae("sha1"===e?"SHA-1":"sha512"===e?"SHA-512":"SHA-256","TEXT");return{update:function(e){return t.update(e),this},digest:function(e){return"hex"===e?t.getHash("HEX"):"base64"===e?t.getHash("B64"):t.getHash("ARRAYBUFFER")}}},Qt="e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",Yt=["inventory","acl","backtosource","policy","torrent","logging","location","storageinfo","quota","storageclass","storagepolicy","mirrorbacktosource","requestpayment","versions","versioning","versionid","uploads","uploadid","partnumber","website","notification","replication","lifecycle","deletebucket","delete","cors","restore","tagging","append","position","response-content-type","response-content-language","response-expires","response-cache-control","response-content-disposition","response-content-encoding","x-image-process","x-oss-process","encryption","obsworkflowtriggerpolicy","x-workflow-limit","x-workflow-prefix","x-workflow-start","x-workflow-template-name","x-workflow-graph-name","x-workflow-execution-state","x-workflow-category","x-workflow-prefix","x-workflow-create","directcoldaccess","customdomain","cdnnotifyconfiguration","metadata","dispolicy","obscompresspolicy","template_name","template_name_prefix","x-workflow-status","x-workflow-type","x-workflow-forbid","sfsacl","obsbucketalias","obsalias"],Xt=["content-type","content-md5","content-length","content-language","expires","origin","cache-control","content-disposition","content-encoding","x-default-storage-class","location","date","etag","host","last-modified","content-range","x-reserved","access-control-allow-origin","access-control-allow-headers","access-control-max-age","access-control-allow-methods","access-control-expose-headers","connection","x-obs-location-clustergroup-id"],$t={"content-length":"ContentLength",date:"Date","x-reserved":"Reserved"},Jt=["STANDARD","WARM","COLD"],Zt=["STANDARD","STANDARD_IA","GLACIER"],er=["private","public-read","public-read-write","public-read-delivered","public-read-write-delivered"],tr=["private","public-read","public-read-write","authenticated-read","bucket-owner-read","bucket-owner-full-control","log-delivery-write"],rr=["Everyone","LogDelivery"],nr=["http://acs.amazonaws.com/groups/global/AllUsers","http://acs.amazonaws.com/groups/global/AuthenticatedUsers","http://acs.amazonaws.com/groups/s3/LogDelivery"],or=["ObjectCreated","ObjectRemoved","ObjectCreated:*","ObjectCreated:Put","ObjectCreated:Post","ObjectCreated:Copy","ObjectCreated:CompleteMultipartUpload","ObjectRemoved:*","ObjectRemoved:Delete","ObjectRemoved:DeleteMarkerCreated"],ir=["ObjectCreated","ObjectRemoved","s3:ObjectCreated:*","s3:ObjectCreated:Put","s3:ObjectCreated:Post","s3:ObjectCreated:Copy","s3:ObjectCreated:CompleteMultipartUpload","s3:ObjectRemoved:*","s3:ObjectRemoved:Delete","s3:ObjectRemoved:DeleteMarkerCreated"],ar=["CreateBucket","SetBucketAlias","BindBucketAlias","UnbindBucketAlias","DeleteBucketAlias","GetBucketAlias"],sr="HeadApiVersion",cr={signature:"obs",headerPrefix:"x-obs-",headerMetaPrefix:"x-obs-meta-",authPrefix:"OBS"},ur={signature:"v2",headerPrefix:"x-amz-",headerMetaPrefix:"x-amz-meta-",authPrefix:"AWS"};function lr(e,t,r){if(0===(e=String(e)).length)return"";if(r)return e;var n;if(t){n=[];var o,i=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return zt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?zt(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}(e);try{for(i.s();!(o=i.n()).done;){var a=o.value;n.push(t.indexOf(a)>=0?a:encodeURIComponent(a))}}catch(e){i.e(e)}finally{i.f()}n=n.join("")}else n=encodeURIComponent(e);return n.replace(/!/g,"%21").replace(/\*/g,"%2A").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29")}function pr(e){return JSON?JSON.stringify(e):""}function dr(e,t){var r={};for(var n in t)if({}.hasOwnProperty.call(t,n)){var o=String(n).toLowerCase();0===o.indexOf(e)&&(r[o.slice(e.length)]=t[n])}return r}function hr(e){return"[object Function]"===Object.prototype.toString.call(e)}function fr(e){return"[object Object]"===Object.prototype.toString.call(e)}function mr(e,t){if("object"===Ht(e))return t(null,e);try{return t(null,nt.parseString(e))}catch(e){return t(e,null)}}function yr(e){var t=new Date(Date.parse(e)),r=t.getUTCHours(),n=t.getUTCMinutes(),o=t.getUTCSeconds(),i=t.getUTCDate(),a=t.getUTCMonth()+1,s="";return s+=t.getUTCFullYear()+"-",a<10&&(s+="0"),s+=a+"-",i<10&&(s+="0"),s+=i+"T",r<10&&(s+="0"),s+=r+":",n<10&&(s+="0"),s+=n+":",o<10&&(s+="0"),s+(o+"Z")}function gr(e){var t=new Date(Date.parse(e)),r=t.getUTCHours(),n=t.getUTCMinutes(),o=t.getUTCSeconds(),i=t.getUTCDate(),a=t.getUTCMonth()+1,s="",c="";return s+=t.getUTCFullYear(),a<10&&(s+="0"),s+=a,i<10&&(s+="0"),c+=(s+=i)+"T",r<10&&(c+="0"),c+=r,n<10&&(c+="0"),c+=n,o<10&&(c+="0"),[s,c+=o+"Z"]}function Ar(e){var t=[],r={};for(var n in e)({}).hasOwnProperty.call(e,n)&&(t.push(n.toLowerCase()),r[n.toLowerCase()]=e[n]);t=t.sort();for(var o="",i="",a=0;a<t.length;a++)0!==a&&(o+=";"),o+=t[a],i+=t[a]+":"+r[t[a]]+"\n";return[o,i]}function vr(e,t,r,n){var o=Wt("sha256","AWS4"+t).update(e).digest(),i=Wt("sha256",o).update(r).digest(),a=Wt("sha256",i).update("s3").digest(),s=Wt("sha256",a).update("aws4_request").digest();return Wt("sha256",s).update(n).digest("hex")}function xr(e,t,r,n,o){var i="AWS4-HMAC-SHA256\n";return i+=t+"\n",i+=e+"/"+n+"/s3/aws4_request\n",vr(e,r,n,i+=Vt("sha256").update(o).digest("hex"))}function br(e){this.log=e,this.ak=null,this.sk=null,this.securityToken=null,this.isSecure=!0,this.server=null,this.pathStyle=!1,this.signatureContext=null,this.isSignatureNegotiation=!0,this.bucketSignatureCache={},this.region="region",this.port=null,this.timeout=300,this.obsSdkVersion="3.22.3",this.isCname=!1,this.bucketEventEmitters={},this.useRawXhr=!1}br.prototype.encodeURIWithSafe=lr,br.prototype.mimeTypes={"7z":"application/x-7z-compressed",aac:"audio/x-aac",ai:"application/postscript",aif:"audio/x-aiff",asc:"text/plain",asf:"video/x-ms-asf",atom:"application/atom+xml",avi:"video/x-msvideo",bmp:"image/bmp",bz2:"application/x-bzip2",cer:"application/pkix-cert",crl:"application/pkix-crl",crt:"application/x-x509-ca-cert",css:"text/css",csv:"text/csv",cu:"application/cu-seeme",deb:"application/x-debian-package",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dvi:"application/x-dvi",eot:"application/vnd.ms-fontobject",eps:"application/postscript",epub:"application/epub+zip",etx:"text/x-setext",flac:"audio/flac",flv:"video/x-flv",gif:"image/gif",gz:"application/gzip",htm:"text/html",html:"text/html",ico:"image/x-icon",ics:"text/calendar",ini:"text/plain",iso:"application/x-iso9660-image",jar:"application/java-archive",jpe:"image/jpeg",jpeg:"image/jpeg",jpg:"image/jpeg",js:"text/javascript",json:"application/json",latex:"application/x-latex",log:"text/plain",m4a:"audio/mp4",m4v:"video/mp4",mid:"audio/midi",midi:"audio/midi",mov:"video/quicktime",mp3:"audio/mpeg",mp4:"video/mp4",mp4a:"audio/mp4",mp4v:"video/mp4",mpe:"video/mpeg",mpeg:"video/mpeg",mpg:"video/mpeg",mpg4:"video/mp4",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",pbm:"image/x-portable-bitmap",pdf:"application/pdf",pgm:"image/x-portable-graymap",png:"image/png",pnm:"image/x-portable-anymap",ppm:"image/x-portable-pixmap",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",ps:"application/postscript",qt:"video/quicktime",rar:"application/x-rar-compressed",ras:"image/x-cmu-raster",rss:"application/rss+xml",rtf:"application/rtf",sgm:"text/sgml",sgml:"text/sgml",svg:"image/svg+xml",swf:"application/x-shockwave-flash",tar:"application/x-tar",tif:"image/tiff",tiff:"image/tiff",torrent:"application/x-bittorrent",ttf:"application/x-font-ttf",txt:"text/plain",wav:"audio/x-wav",webm:"video/webm",wma:"audio/x-ms-wma",wmv:"video/x-ms-wmv",woff:"application/x-font-woff",wsdl:"application/wsdl+xml",xbm:"image/x-xbitmap",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xml:"application/xml",xpm:"image/x-xpixmap",xwd:"image/x-xwindowdump",yaml:"text/yaml",yml:"text/yaml",zip:"application/zip"},br.prototype.refresh=function(e,t,r){this.ak=e?String(e).trim():null,this.sk=t?String(t).trim():null,this.securityToken=r?String(r).trim():null},br.prototype.initFactory=function(e,t,r,n,o,i,a,s,c,u,l,p,d,h,f,m){if(this.refresh(e,t,u),this.urlPrefix=d||"",this.regionDomains=h||null,this.setRequestHeaderHook=f||null,!n)throw new Error("Server is not set");0===(n=String(n).trim()).indexOf("https://")?(n=n.slice("https://".length),r=!0):0===n.indexOf("http://")&&(n=n.slice("http://".length),r=!1);for(var y=n.lastIndexOf("/");y>=0;)y=(n=n.slice(0,y)).lastIndexOf("/");(y=n.indexOf(":"))>=0&&(s=n.slice(y+1),n=n.slice(0,y)),this.server=n,/^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$/.test(this.server)&&(o=!0),void 0!==r&&(this.isSecure=r),void 0!==o&&(this.pathStyle=o),i=void 0!==i?String(i).trim().toLowerCase():"obs",void 0!==l&&(this.isSignatureNegotiation=l),this.isCname=p,(this.pathStyle||this.isCname)&&(this.isSignatureNegotiation=!1,"obs"===i&&(i="v2")),this.signatureContext="obs"===i?cr:ur,void 0!==a&&(this.region=String(a)),this.port=s?parseInt(s,10):this.isSecure?443:80,void 0!==c&&(this.timeout=parseInt(c,10)),void 0!==m&&(this.useRawXhr=m)},br.prototype.SseKmsAdapter=function(e,t){e=e||"";var r=(e=String(e)).indexOf("aws:");return"obs"===t.signature?0===r?e.slice(4):e:0===r?e:"aws:"+e},br.prototype.BucketAdapter=function(e,t){e=e||"";var r=(e=String(e)).indexOf("arn:aws:s3:::");return"obs"===t.signature?0===r?e.slice("arn:aws:s3:::".length):e:0===r?e:"arn:aws:s3:::"+e},br.prototype.EventAdapter=function(e,t){return e=e||"",e=String(e),"obs"===t.signature?or.indexOf(e)>=0?e:ir.indexOf(e)>=0?e.substring(3):"":ir.indexOf(e)>=0?e:or.indexOf(e)>=0?"s3:"+e:""},br.prototype.URIAdapter=function(e,t){return e=e||"",e=String(e),"obs"===t.signature?rr.indexOf(e)>=0?e:"AllUsers"===e||"http://acs.amazonaws.com/groups/global/AllUsers"===e?"Everyone":"":nr.indexOf(e)>=0?e:"Everyone"===e||"AllUsers"===e?"http://acs.amazonaws.com/groups/global/AllUsers":"AuthenticatedUsers"===e?"http://acs.amazonaws.com/groups/global/AuthenticatedUsers":"LogDelivery"===e?"http://acs.amazonaws.com/groups/s3/LogDelivery":""},br.prototype.StorageClassAdapter=function(e,t){return e=e||"",e=String(e).toUpperCase(),"obs"===t.signature?Jt.indexOf(e)>=0?e:"STANDARD_IA"===e?"WARM":"GLACIER"===e?"COLD":"":Zt.indexOf(e)>=0?e:"WARM"===e?"STANDARD_IA":"COLD"===e?"GLACIER":""},br.prototype.ACLAdapter=function(e,t){return e=e||"",e=String(e).toLowerCase(),"obs"===t.signature?er.indexOf(e)>=0?e:"":("public-read-delivered"===e?e="public-read":"public-read-write-delivered"===e&&(e="public-read-write"),tr.indexOf(e)>=0?e:"")},br.prototype.doExec=function(e,t,r){var n=this.makeParam(e,t);if("err"in n)return r(n.err,null);this.sendRequest(e,n,r)},br.prototype.doNegotiation=function(e,t,r,n,o,i){var a=null,s=this;if(o&&t.Bucket){var c=this.bucketSignatureCache[t.Bucket];if(c&&c.signatureContext&&c.expireTime>(new Date).getTime()){t.signatureContext=c.signatureContext;var u=this.makeParam(e,t);return"err"in u?r(u.err,null):(u.signatureContext=c.signatureContext,this.sendRequest(e,u,r))}if((a=this.bucketEventEmitters[t.Bucket])||(a={s:0,n:function(){for(;this.e&&this.e.length>0;)this.e.shift()()}},this.bucketEventEmitters[t.Bucket]=a),a.s)return void a.e.push((function(){s.doNegotiation(e,t,r,n,o,i)}));a.e=[],a.s=1}this.doExec(sr,n?{Bucket:t.Bucket}:{},(function(o,c){if(o)return r(o,null),void(a&&(a.s=0,a.n()));if(n&&404===c.CommonMsg.Status||c.CommonMsg.Status>=500)return r(o,c),void(a&&(a.s=0,a.n()));var u=ur;c.CommonMsg.Status<300&&c.InterfaceResult&&c.InterfaceResult.ApiVersion>="3.0"&&(u=cr),i&&(s.bucketSignatureCache[t.Bucket]={signatureContext:u,expireTime:(new Date).getTime()+15+60*Math.ceil(5*Math.random())*1e3}),a&&(a.s=0,a.n()),t.signatureContext=u;var l=s.makeParam(e,t);if("err"in l)return r(l.err,null);l.signatureContext=u,s.sendRequest(e,l,r)}))},br.prototype.exec=function(e,t,r){var n=this;n.isSignatureNegotiation&&e!==sr?"ListBuckets"===e?n.doNegotiation(e,t,r,!1,!1,!1):ar.indexOf(e)>-1?n.doNegotiation(e,t,(function(o,i){if(!o&&400===i.CommonMsg.Status&&"Unsupported Authorization Type"===i.CommonMsg.Message&&t.signatureContext&&"v2"===t.signatureContext.signature){t.signatureContext=ur;var a=n.makeParam(e,t);return"err"in a?r(a.err,null):(a.signatureContext=t.signatureContext,void n.sendRequest(e,a,r))}r(o,i)}),!1,!0,!1):n.doNegotiation(e,t,r,!0,!0,!0):n.doExec(e,t,r)},br.prototype.sliceBlob=function(e,t,r,n){return n=n||e.type,e.mozSlice?e.mozSlice(t,r,n):e.webkitSlice?e.webkitSlice(t,r,n):e.slice(t,r,n)},br.prototype.toXml=function(e,t,r,n,o){var i="";if(null!==r)return i+this.buildXml(e,t,r,n,o);for(var a in t)if(a in e){var s=t[a].sentAs||a;i+=this.buildXml(e,t[a],a,s,o)}return i},br.prototype.buildXml=function(e,t,r,n,o){var i="",a=t.type;if("array"===a)for(var s=0;s<e[r].length;s++)if("object"===t.items.type){if(!e[r][s])return i;var c=this.toXml(e[r][s],t.items.parameters,null,null,o);""!==c&&(i+="<"+n+">"+c+"</"+n+">")}else"adapter"===t.items.type?i+="<"+n+">"+String(this[r+"Adapter"](e[r][s],o)).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">":"array"!==t.items.type&&(i+="<"+n+">"+String(e[r][s]).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">");else if("object"===a){if(!e[r])return i;var u=this.toXml(e[r],t.parameters,null,null,o);""!==u&&(i+="<"+n,"data"in t&&("xsiNamespace"in t.data&&(i+=' xmlns:xsi="'+t.data.xsiNamespace+'"'),"xsiType"in t.data&&(i+=' xsi:type="'+e[r][t.data.xsiType]+'"')),i+=">",i+=u+"</"+n+">")}else"adapter"===a?i+="<"+n+">"+String(this[r+"Adapter"](e[r],o)).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">":"ignore"!==a&&(i+="<"+n+">"+String(e[r]).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">");if(i&&t.wrapper){var l=t.wrapper;i="<"+l+">"+i+"</"+l+">"}return i},br.prototype.jsonToObject=function(e,t,r,n){var o={};if(null!==r)this.buildObject(e,t,r,o,n);else for(var i in e)({}).hasOwnProperty.call(e,i)&&this.buildObject(e,t,i,o,n);return o},br.prototype.buildObject=function(e,t,r,n,o){var i=function(t){return t&&e[r].decode&&o?decodeURIComponent(t.replace(/\+/g,"%20")):t};if(fr(t)){var a=!0,s=e[r].wrapper;if(s&&s in t&&(a=fr(t=t[s])),a){var c=e[r].sentAs||r;if(c in t)if("object"===e[r].type)n[r]=this.jsonToObject(e[r].parameters,t[c],null,o);else if("array"===e[r].type){var u=[];if(function(e){return"[object Array]"===Object.prototype.toString.call(e)}(t[c]))for(var l=0;l<t[c].length;l++)u[l]="object"===e[r].items.type?this.jsonToObject(e[r].items.parameters,t[c][l],null,o):i(t[c][l]["#text"]);else u[0]="object"===e[r].items.type?this.jsonToObject(e[r].items.parameters,t[c],null,o):i(t[c]["#text"]||"");n[r]=u}else n[r]=i(t[c]["#text"])}}void 0===n[r]&&("object"===e[r].type?n[r]=e[r].parameters?this.jsonToObject(e[r].parameters,null,null,o):{}:"array"===e[r].type&&(n[r]=[]))},br.prototype.makeParam=function(e,t){var r=t.signatureContext||this.signatureContext,n="obs"===r.signature?Pt[e]:Gt[e],o=n.httpMethod,i="/",a="",s="",c={},u={};for(var l in u.$requestParam=t,"urlPath"in n&&(a+="?",a+=n.urlPath),n.parameters)if({}.hasOwnProperty.call(n.parameters,l)){var p=n.parameters[l];if("Bucket"===l&&this.isCname)continue;var d=t[l];if("callback"===p.type&&void 0===d&&p.parameters&&(void 0!==t.CallbackUrl||void 0!==t.CallbackBody)){d={};for(var h=0,f=Object.keys(p.parameters);h<f.length;h++){var m=f[h],y=p.parameters[m],g=t[m];if(y.required&&(null==g||"[object String]"===Object.prototype.toString.call(g)&&""===g))return u.err=m+" is a required element!",this.log.runLog("error",e,u.err),u;d[m.slice(0,1).toLowerCase()+m.slice(1)]=g}}if(p.required&&(null==d||"[object String]"===Object.prototype.toString.call(d)&&""===d))return u.err=l+" is a required element!",this.log.runLog("error",e,u.err),u;if(null!=d){if("srcFile"===p.type||"dstFile"===p.type){u[p.type]=d;continue}"plain"===p.type&&(u[l]=d);var A=p.sentAs||l;if(p.withPrefix&&(A=r.headerPrefix+A),"uri"===p.location)"/"!==i&&(i+="/"),i+=d;else if("header"===p.location){var v=p.encodingSafe||" ;/?:@&=+$,";if("object"===p.type){if(r.headerMetaPrefix===A)for(var x in d)if({}.hasOwnProperty.call(d,x)){var b=d[x];c[0===(x=String(x).trim().toLowerCase()).indexOf(A)?x:A+x]=lr(b,v)}}else if("array"===p.type){var w=[];for(var P in d)({}).hasOwnProperty.call(d,P)&&(w[P]=lr(d[P],v));c[A]=w}else if("password"===p.type){var k=window.btoa?window.btoa:et.encode;c[A]=k(d),c[p.pwdSentAs||A+"-MD5"]=this.rawBufMD5(d)}else if("number"===p.type&&Number(d))c[A]=lr(String(d),v);else if("boolean"===p.type)c[A]=lr(d?"true":"false",v);else if("callback"===p.type)c[A]=et.encode(JSON.stringify(d));else if("adapter"===p.type){var S=this[l+"Adapter"](d,r);S&&(c[A]=lr(String(S),v))}else c[A]=lr(String(d),v,p.skipEncoding)}else if("urlPath"===p.location){var C=""===a?"?":"&",T=d;("number"!==p.type||"number"===p.type&&Number(T)>=0)&&(a+=C+lr(A,"/")+"="+lr(String(T),"/"))}else if("xml"===p.location){var E=this.toXml(t,p,l,A,r);E&&(s+=E)}else"body"===p.location&&(s=d)}}var M="file"===u.dstFile;if("Content-Type"in c||M||(c["Content-Type"]="binary/octet-stream"),"data"in n&&"xmlRoot"in n.data&&(s||n.data.xmlAllowEmpty)){var O=n.data.xmlRoot;s="<"+O+">"+s+"</"+O+">",c["Content-Type"]="application/xml"}if(M&&(u.rawUri=i),c.Host=this.server+(80===this.port||443===this.port?"":":"+this.port),!this.pathStyle&&!this.isCname){var B=i.split("/");if(B.length>=2&&B[1]){c.Host=B[1]+"."+c.Host;var R=i.replace(B[1],"");0===R.indexOf("//")&&(R=R.slice(1)),"v4"===r.signature?i=R:"/"===R&&(i+="/"),u.requestUri=lr(R,"/")}}if(u.method=o,u.uri=lr(i,"/"),u.urlPath=a,s&&(n.data&&n.data.md5&&(c["Content-MD5"]=this.bufMD5(s),c["Content-Length"]=0===s.length?"0":String(s.length)),u.xml=s,this.log.runLog("debug",e,"request content:"+s)),u.headers=c,"srcFile"in u&&(u.srcFile instanceof window.File||u.srcFile instanceof window.Blob)){var I=u.srcFile.size;if("Content-Length"in u.headers||"PartSize"in u||"Offset"in u){var D,j=u.Offset;j=j&&j>=0&&j<I?j:0,D=(D="PartSize"in u?u.PartSize:"Content-Length"in u.headers?parseInt(u.headers["Content-Length"],10):I)&&D>0&&D<=I-j?D:I-j,u.PartSize=D,u.Offset=j,u.headers["Content-Length"]=String(u.PartSize)}}return u},br.prototype.parseCommonHeaders=function(e,t,r){for(var n in $t)({}).hasOwnProperty.call($t,n)&&(e.InterfaceResult[$t[n]]=t[n]);e.InterfaceResult.RequestId=t[r.headerPrefix+"request-id"],e.InterfaceResult.Id2=t[r.headerPrefix+"id-2"],e.CommonMsg.RequestId=e.InterfaceResult.RequestId,e.CommonMsg.Id2=e.InterfaceResult.Id2},br.prototype.contrustCommonMsg=function(e,t,r,n){for(var o in e.InterfaceResult={},this.parseCommonHeaders(e,r,n),t)if("header"===t[o].location){var i=t[o].sentAs||o;if(t[o].withPrefix&&(i=n.headerPrefix+i),"object"===t[o].type)e.InterfaceResult[o]=dr(i,r);else{var a=null;i in r?a=r[i]:i.toLowerCase()in r&&(a=r[i.toLowerCase()]),null!==a&&(e.InterfaceResult[o]=a)}}},br.prototype.getRequest=function(e,t,r,n,o,i){var a=this.regionDomains,s={},c=this.log,u="obs"===r.signature?Pt[e+"Output"]:Gt[e+"Output"],l=(u=u||{}).parameters||{};s.CommonMsg={Status:t.status,Code:"",Message:"",HostId:"",RequestId:"",InterfaceResult:null};var p=t.headers,d=pr(p);c.runLog("info",e,"get response start, statusCode:"+t.status),c.runLog("debug",e,"response msg :statusCode:"+t.status+", headers:"+d);var h=function(){var t="Status:"+s.CommonMsg.Status+", Code:"+s.CommonMsg.Code+", Message:"+s.CommonMsg.Message;c.runLog("debug",e,"exec interface "+e+" finish, "+t),i(null,s)};if(t.status>=300&&t.status<400&&304!==t.status&&n<=5){var f=p.location||p.Location;if(f){var m="http code is 3xx, need to redirect to "+f;c.runLog("warn",e,m);var y=new Error("redirect");return y.location=f,y.bucketLocation=p["x-amz-bucket-region"]||p["x-obs-bucket-region"],i(y)}var g=p["x-amz-bucket-region"]||p["x-obs-bucket-location"];if(g&&a[g]){var A=(this.isSecure?"https://":"http://")+a[g];hr(this.setRequestHeaderHook)&&this.setRequestHeaderHook(p,o,e,a[g]);var v="get redirect code 3xx, need to redirect to"+A;c.runLog("error",e,v);var x=new Error("redirect");return x.location=A,i(x)}c.runLog("error",e,"get redirect code 3xx, but no location in headers")}if(t.status<300){var b=t.data;this.contrustCommonMsg(s,l,p,r);var w="Status: "+s.CommonMsg.Status+", headers: "+d;if(b&&(w+="body length: "+b.length,c.runLog("debug",e,"response body length:"+b.length)),c.runLog("debug",e,w),b&&"data"in u){if(o.CallbackUrl&&u.CallbackResponse)return s.InterfaceResult[u.CallbackResponse.sentAs]=b,void h();if("xml"===u.data.type){var P=this;return mr(b,(function(t,r){if(t)return c.runLog("error",e,"change xml to json err ["+pr(t)+"]"),i(t,null);var n=r;u.data.xmlRoot&&u.data.xmlRoot in n&&(n=r[u.data.xmlRoot]);var o=!!n.EncodingType;if(fr(n))for(var a in l)"xml"===l[a].location&&(s.InterfaceResult[a]=P.jsonToObject(l,n,a,o)[a]);h()}))}if("body"===u.data.type)for(var k in l)if("body"===l[k].location){s.InterfaceResult[k]=b;break}}return h()}var S=t.data,C="Status: "+s.CommonMsg.Status+", headers: "+d;return""!==S&&(C+="body: "+S,c.runLog("debug",e,"response body :"+S)),s.CommonMsg.RequestId=p[r.headerPrefix+"request-id"],s.CommonMsg.Id2=p[r.headerPrefix+"id2"],s.CommonMsg.Indicator=p["x-reserved-indicator"],c.runLog("info",e,"request finished with request id:"+s.CommonMsg.RequestId),c.runLog("debug",e,C),S?mr(S,(function(t,r){if(t)c.runLog("error",e,"change xml to json err ["+pr(t)+"]"),s.CommonMsg.Message=t.message;else if(r){if("Error"in r){var n=r.Error;for(var o in n)n[o]&&n[o]["#text"]&&(s.CommonMsg[o]=n[o]["#text"])}else{var i=r;"code"in i&&(s.CommonMsg.Code=i.code),"message"in i&&(s.CommonMsg.Message=i.message),"hostId"in i&&(s.CommonMsg.HostId=i.hostId),"request_id"in i&&i.request_id&&(s.CommonMsg.RequestId=i.request_id)}c.runLog("error",e,"request error with error code:"+s.CommonMsg.Code+", error message:"+s.CommonMsg.Message+", request id:"+s.CommonMsg.RequestId)}h()})):h()},br.prototype.makeRequest=function(e,t,r,o){var i=this.log,a=t.xml||null,s=t.signatureContext||this.signatureContext;if(delete t.headers.Authorization,"file"===t.dstFile){var c={};if(t.urlPath)for(var u=t.urlPath.slice(1).split("&"),l=0;l<u.length;l++)if(-1===u[l].indexOf("="))c[u[l]]="";else{var p=u[l].split("=");c[p[0]]=p[1]}var d=t.rawUri.split("/")[1],h=t.rawUri.slice(("/"+d+"/").length),f={CommonMsg:{Status:0,Code:"",Message:"",HostId:""},InterfaceResult:{}},m=("obs"===s.signature?Pt[e+"Output"]:Gt[e+"Output"]).parameters;for(var y in m)if("body"===m[y].location){f.InterfaceResult[y]=this.createSignedUrlSync({Method:t.method,Bucket:d,Key:h,Expires:3600,Headers:t.headers,QueryParams:c,signatureContext:s});break}return o(null,f)}var g,A=t.$requestParam.RequestDate,v=Object.prototype.toString.call(A);if("[object Date]"===v)g=A;else if("[object String]"===v)try{(g=new Date).setTime(Date.parse(A))}catch(e){}g||(g=new Date);var x=g.toUTCString(),b="v4"===s.signature.toLowerCase();t.headers[s.headerPrefix+"date"]=b?gr(x)[1]:x;var w=(t.requestUri?t.requestUri:t.uri)+t.urlPath;this.ak&&this.sk&&e!==sr&&(this.securityToken&&(t.headers[s.headerPrefix+"security-token"]=this.securityToken),b?this.v4Auth(t,e,s):this.doAuth(t,e,s));var P=t.headers;hr(this.setRequestHeaderHook)&&this.setRequestHeaderHook(P,t.$requestParam,e);var k=P.Host,S=t.method,C={};for(var T in P)({}).hasOwnProperty.call(P,T)&&(C[T]=P[T]);C.Authorization="****";var E="method:"+S+", path:"+w+"headers:"+pr(C);a&&(E+="body:"+a),i.runLog("info",e,"prepare request parameters ok,then Send request to service start"),i.runLog("debug",e,"request msg:"+E);var M=t.protocol?0===t.protocol.toLowerCase().indexOf("https"):this.isSecure,O=t.port||this.port;delete P.Host,delete P["Content-Length"];var B="text";!t.dstFile||"file"===t.dstFile||"arraybuffer"!==t.dstFile&&"blob"!==t.dstFile||(B=String(t.dstFile));var R=g.getTime(),I=this,D=function(t){try{var r=pr(t);i.runLog("error",e,"Send request to service error ["+r+"]")}catch(r){t.toString&&i.runLog("error",e,"Send request to service error ["+t.toString()+"]")}i.runLog("info",e,"http cost "+((new Date).getTime()-R)+" ms"),o(t,null)};if(this.useRawXhr){var j=null;try{j=new XMLHttpRequest}catch(e){try{j=new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{j=new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}}if(null===j)return o(new Error("XHR is not available"),null);if(t.srcFile){if(!(t.srcFile instanceof window.File||t.srcFile instanceof window.Blob))return o(new Error("source file must be an instance of window.File or window.Blob"),null);try{var L=t.srcFile;if(t.Offset>=0&&t.PartSize>0)L=this.sliceBlob(L,t.Offset,t.Offset+t.PartSize);else if("ContentLength"in t){var _=parseInt(t.ContentLength,10);_>0&&(L=this.sliceBlob(L,0,_))}a=L}catch(e){return o(e)}}for(var q in j.open(S,(M?"https://"+this.urlPrefix+k:"http://"+this.urlPrefix+k)+w),j.withCredentials=!1,P)({}).hasOwnProperty.call(P,q)&&j.setRequestHeader(q,P[q]);j.timeout=1e3*I.timeout,j.responseType=B,t.$requestParam.cancelHook=function(){j.abort()},j.onreadystatechange=function(){if(4===j.readyState&&j.status>=200){i.runLog("info",e,"http cost "+((new Date).getTime()-R)+" ms");for(var n=j.getAllResponseHeaders().trim().split(/[\r\n]+/),a={},c=0;c<n.length;c++){var u=n[c].split(": "),l=u.shift(),p=u.join(": ");a[l.toLowerCase()]=p}var d=j.response;d||""!==B&&"text"!==B||(d=j.responseText);var h={status:j.status,headers:a,data:d};I.getRequest(e,h,s,r,t.$requestParam,o)}};var N=!1,F=function(e){N||(N=!0,D(e))};j.ontimeout=function(){F(new Error("timeout of "+j.timeout+"ms exceed"))},j.onerror=function(){F(new Error("Network Error"))},j.onabort=function(){F(new Error("Cancel"))},j.upload&&(j.upload.ontimeout=function(){F(new Error("timeout of "+j.timeout+"ms exceed"))},j.upload.onerror=function(){F(new Error("Network Error"))},j.upload.onabort=function(e){F(new Error("Cancel"))}),hr(t.ProgressCallback)&&("GET"!==S&&j.upload?"PUT"!==S&&"POST"!==S||(j.upload.onprogress=function(e){e.lengthComputable&&t.ProgressCallback(e.loaded,e.total,((new Date).getTime()-R)/1e3)}):j.onprogress=function(e){e.lengthComputable&&t.ProgressCallback(e.loaded,e.total,((new Date).getTime()-R)/1e3)}),j.send(a)}else{var U=null,K=null;if(hr(t.ProgressCallback)){var G=function(e){e.lengthComputable&&t.ProgressCallback(e.loaded,e.total,((new Date).getTime()-R)/1e3)};"GET"===S?K=G:"PUT"!==S&&"POST"!==S||(U=G)}var H=":"+O;k.indexOf(":")>=0&&(H="");var z="",W=M?"https://":"http://";if(this.urlPrefix&&hr(this.setRequestHeaderHook)&&"InitiateMultipartUpload"!==e&&"UploadPart"!==e&&"CompleteMultipartUpload"!==e){var V=!0;(t.$requestParam.hasRegion||t.$requestParam.redirectRegion)&&(V=!1);var Q="";5443===O&&(Q="-5443"),V?t.$requestParam.Bucket?(-1!==t.$requestParam.Bucket.indexOf(".")&&(z=W+this.urlPrefix+"/bucket"+Q),z=W+this.urlPrefix+"/bucket"+Q):z="/"===w.split("?")[0]?W+this.urlPrefix+Q:W+this.urlPrefix+"/place"+Q:z=t.$requestParam.Bucket?W+this.urlPrefix+"/region-bucket"+Q:W+this.urlPrefix+"/region"+Q}else z=W+k+H;var Y={method:S,url:z+w,withCredentials:!1,headers:P,validateStatus:function(e){return e>=200},maxRedirects:0,responseType:B,data:a,timeout:1e3*this.timeout,onUploadProgress:U,onDownloadProgress:K,cancelToken:new(n().CancelToken)((function(e){t.$requestParam.cancelHook=e}))};if(t.srcFile){if(!(t.srcFile instanceof window.File||t.srcFile instanceof window.Blob))return o(new Error("source file must be an instance of window.File or window.Blob"),null);var X=t.srcFile;try{if(t.Offset>=0&&t.PartSize>0)X=this.sliceBlob(X,t.Offset,t.Offset+t.PartSize);else if("ContentLength"in t){var $=parseInt(t.ContentLength,10);$>0&&(X=this.sliceBlob(X,0,$))}}catch(e){return o(e)}Y.data=X}n().request(Y).then((function(n){i.runLog("info",e,"http cost "+((new Date).getTime()-R)+" ms"),I.getRequest(e,n,s,r,t.$requestParam,o)})).catch((function(e){D(e)}))}},br.prototype.sendRequest=function(e,r,n,o){void 0===o&&(o=1);var i=this;i.makeRequest(e,r,o,(function(a,s){if(a&&"redirect"===a.message){var c=function(e){var r=t().parse(e);return{hostname:r.hostname,port:r.port,host:r.hostname,protocol:r.protocol?r.protocol+":":"",query:r.query,path:r.path+(r.query?"?"+r.query:""),pathname:r.path,search:r.query?"?"+r.query:""}}(a.location);a.bucketLocation&&-1!==c.hostname.indexOf(r.$requestParam.Bucket)&&(r.$requestParam.redirectRegion=a.bucketLocation),r.headers.Host=c.hostname,r.protocol=c.protocol,r.port=c.port||(r.protocol&&0===r.protocol.toLowerCase().indexOf("https")?443:80),i.sendRequest(e,r,n,o+1)}else n(a,s)}))},br.prototype.doAuth=function(e,t,r){for(var n=["Content-MD5","Content-Type"],o=e.method+"\n",i=0;i<n.length;i++)n[i]in e.headers&&(o+=e.headers[n[i]]),o+="\n";r.headerPrefix+"date"in e.headers||(o+=e.headers.Date),o+="\n";var a=[];for(var s in e.headers)if({}.hasOwnProperty.call(e.headers,s)){var c=s.toLowerCase();0===c.indexOf(r.headerPrefix)&&a.push({key:c,value:e.headers[s]})}a=a.sort((function(e,t){return e.key<t.key?-1:e.key>t.key?1:0}));for(var u=0;u<a.length;u++){var l=a[u].key,p=0===l.indexOf(r.headerMetaPrefix)?a[u].value.trim():a[u].value;o+=l+":"+p+"\n"}var d=e.uri;if(this.isCname&&("/"===d?d+=e.headers.Host+"/":0===d.indexOf("/")&&(d="/"+e.headers.Host+d)),e.urlPath){for(var h=e.urlPath.slice(1).split("&").sort(),f="",m=0;m<h.length;m++){var y=h[m].split("="),g=decodeURIComponent(y[0]);Yt.indexOf(g.toLowerCase())>=0&&(f+=""===f?"?":"&",f+=g,2===y.length&&y[1]&&(f+="="+decodeURIComponent(y[1])))}d+=f}o+=d,this.log.runLog("debug",t,"stringToSign:"+o),e.headers.Authorization=r.authPrefix+" "+this.ak+":"+Wt("sha1",this.sk).update(o).digest("base64")},br.prototype.v4Auth=function(e,t,r){e.headers[r.headerPrefix+"content-sha256"]=Qt;var n=e.headers,o=this.log,i=null,a=null;if(r.headerPrefix+"date"in n)i=(a=n[r.headerPrefix+"date"]).slice(0,a.indexOf("T"));else{var s=gr(n.Date);i=s[0],a=s[1]}var c=this.ak+"/"+i+"/"+this.region+"/s3/aws4_request",u=Ar(n),l=u[0],p=u[1],d="";if(e.urlPath){var h=e.urlPath.slice(1).split("&");h=h.sort();for(var f=0;f<h.length;f++)d+=h[f],-1===h[f].indexOf("=")&&(d+="="),f!==h.length-1&&(d+="&")}var m=e.method+"\n";m+=e.uri+"\n",m+=d+"\n",m+=p+"\n",m+=l+"\n",m+=Qt,o.runLog("debug",t,"canonicalRequest:"+m);var y=xr(i,a,this.sk,this.region,m);e.headers.Authorization="AWS4-HMAC-SHA256 Credential="+c+",SignedHeaders="+l+",Signature="+y},br.prototype.bufMD5=function(e){return Vt("md5").update(e).digest("base64")},br.prototype.rawBufMD5=function(e){return Vt("md5").update(e).digest("rawbase64")},br.prototype.createSignedUrlSync=function(e){return"v4"===(e.signatureContext||this.signatureContext).signature.toLowerCase()?this.createV4SignedUrlSync(e):this.createV2SignedUrlSync(e)},br.prototype.createV2SignedUrlSync=function(e){var t=(e=e||{}).signatureContext||this.signatureContext,r=e.Method?String(e.Method):"GET",n=e.Bucket?String(e.Bucket):null,o=e.Key?String(e.Key):null,i=e.SpecialParam?String(e.SpecialParam):null;"obs"===t.signature.toLowerCase()&&"storagePolicy"===i?i="storageClass":"v2"===t.signature.toLowerCase()&&"storageClass"===i&&(i="storagePolicy");var a=e.Policy?String(e.Policy):null,s=e.Prefix?String(e.Prefix):null,c=e.Expires?parseInt(e.Expires,10):300,u={};if(e.Headers&&e.Headers instanceof Object&&!(e.Headers instanceof Array))for(var l in e.Headers)({}).hasOwnProperty.call(e.Headers,l)&&(u[l]=e.Headers[l]);var p={};if(e.QueryParams&&e.QueryParams instanceof Object&&!(e.QueryParams instanceof Array))for(var d in e.QueryParams)({}).hasOwnProperty.call(e.QueryParams,d)&&(p[d]=e.QueryParams[d]);this.securityToken&&!p[t.headerPrefix+"security-token"]&&(p[t.headerPrefix+"security-token"]=this.securityToken);var h="",f="",m=this.server;this.isCname?f+="/"+m+"/":n&&(f+="/"+n,this.pathStyle?h+="/"+n:(m=n+"."+m,f+="/")),o&&(h+="/"+(o=lr(o,"/")),f.lastIndexOf("/")!==f.length-1&&(f+="/"),f+=o),""===f&&(f="/"),h+="?",i&&(p[i]=""),"v2"===t.signature.toLowerCase()?p.AWSAccessKeyId=this.ak:p.AccessKeyId=this.ak,c<0&&(c=300),c=parseInt((new Date).getTime()/1e3,10)+c,a&&s?(p.Policy=a,p.prefix=s):p.Expires=String(c);var y={};for(var g in u)if({}.hasOwnProperty.call(u,g)){var A=String(g).toLowerCase();("content-type"===A||"content-md5"===A||A.length>t.headerPrefix.length&&A.slice(0,t.headerPrefix.length)===t.headerPrefix)&&(y[A]=u[g])}var v=[];for(var x in p)({}).hasOwnProperty.call(p,x)&&v.push(x);v.sort();for(var b=a&&s,w=!1,P=[],k=a&&s?"":"/",S=0;S<v.length;S++){var C=v[S],T=p[C];if(h+=C=lr(C,k),(T=lr(T,k))&&(h+="="+T),(!b||"policy"!==C.toLowerCase())&&(Yt.indexOf(C.toLowerCase())>=0||0===C.toLowerCase().indexOf(t.headerPrefix))){w=!0;var E=T?C+"="+decodeURIComponent(T):C;P.push(E)}h+="&"}P=P.join("&"),w&&(P="?"+P),f+=P;var M=[r];if(M.push("\n"),"content-md5"in y&&M.push(y["content-md5"]),M.push("\n"),"content-type"in y&&M.push(y["content-type"]),M.push("\n"),b?M.push(a):M.push(String(c)),M.push("\n"),b)M.push(P);else{var O=[],B=0;for(var R in y)R.length>t.headerPrefix.length&&R.slice(0,t.headerPrefix.length)===t.headerPrefix&&(O[B++]=R);O=O.sort();for(var I=0;I<O.length;I++)M.push(O[I]),M.push(":"),M.push(y[O[I]]),M.push("\n");M.push(f)}var D=Wt("sha1",this.sk);return D.update(M.join("")),h+=b?"Signature="+lr(D.digest("base64")):"Signature="+lr(D.digest("base64"),"/"),{ActualSignedRequestHeaders:u,SignedUrl:(this.isSecure?"https":"http")+"://"+m+":"+this.port+h}},br.prototype.createV4SignedUrlSync=function(e){var t=(e=e||{}).signatureContext||this.signatureContext,r=e.Method?String(e.Method):"GET",n=e.Bucket?String(e.Bucket):null,o=e.Key?String(e.Key):null,i=e.SpecialParam?String(e.SpecialParam):null;"storageClass"===i&&(i="storagePolicy");var a=e.Expires?parseInt(e.Expires,10):300,s={};if(e.Headers&&e.Headers instanceof Object&&!(e.Headers instanceof Array))for(var c in e.Headers)({}).hasOwnProperty.call(e.Headers,c)&&(s[c]=e.Headers[c]);var u={};if(e.QueryParams&&e.QueryParams instanceof Object&&!(e.QueryParams instanceof Array))for(var l in e.QueryParams)({}).hasOwnProperty.call(e.QueryParams,l)&&(u[l]=e.QueryParams[l]);this.securityToken&&!u[t.headerPrefix+"security-token"]&&(u[t.headerPrefix+"security-token"]=this.securityToken);var p="",d="",h=this.server;n&&(this.pathStyle?(p+="/"+n,d+="/"+n):h=n+"."+h),o&&(p+="/"+(o=lr(o,"/")),d+="/"+o),""===d&&(d="/"),p+="?",i&&(u[i]=""),a<0&&(a=300);var f=gr(s.date||s.Date||(new Date).toUTCString()),m=f[0],y=f[1];s.Host=h+(80===this.port||443===this.port?"":":"+this.port),u["X-Amz-Algorithm"]="AWS4-HMAC-SHA256",u["X-Amz-Credential"]=this.ak+"/"+m+"/"+this.region+"/s3/aws4_request",u["X-Amz-Date"]=y,u["X-Amz-Expires"]=String(a);var g=Ar(s);u["X-Amz-SignedHeaders"]=g[0];var A={},v=[];for(var x in u)if({}.hasOwnProperty.call(u,x)){var b=u[x];x=lr(x,"/"),b=lr(b),A[x]=b,v.push(x),p+=x,b&&(p+="="+b),p+="&"}var w="";v.sort();for(var P=0;P<v.length;)w+=v[P]+"="+A[v[P]],++P!==v.length&&(w+="&");var k=r+"\n";return k+=d+"\n",k+=w+"\n",k+=g[1]+"\n",k+=g[0]+"\n",k+="UNSIGNED-PAYLOAD",p+="X-Amz-Signature="+lr(xr(m,y,this.sk,this.region,k)),{ActualSignedRequestHeaders:s,SignedUrl:(this.isSecure?"https":"http")+"://"+h+":"+this.port+p}},br.prototype.createPostSignatureSync=function(e){var t=e.signatureContext||this.signatureContext;if("v4"===t.signature)return this.createV4PostSignatureSync(e);var r=(e=e||{}).Bucket?String(e.Bucket):null,n=e.Key?String(e.Key):null,o=e.Expires?parseInt(e.Expires,10):300,i={};if(e.FormParams&&e.FormParams instanceof Object&&!(e.FormParams instanceof Array))for(var a in e.FormParams)({}).hasOwnProperty.call(e.FormParams,a)&&(i[a]=e.FormParams[a]);this.securityToken&&!i[t.headerPrefix+"security-token"]&&(i[t.headerPrefix+"security-token"]=this.securityToken);var s=new Date;s.setTime(parseInt((new Date).getTime(),10)+1e3*o),s=yr(s.toUTCString()),r&&(i.bucket=r),n&&(i.key=n);var c=[];c.push('{"expiration":"'),c.push(s),c.push('", "conditions":[');var u=!0,l=!0,p=["acl","bucket","key","success_action_redirect","redirect","success_action_status"];for(var d in i)if(d){var h=i[d];"bucket"===(d=String(d).toLowerCase())?u=!1:"key"===d&&(l=!1),Xt.indexOf(d)<0&&p.indexOf(d)<0&&0!==d.indexOf(t.headerPrefix)||(c.push('{"'),c.push(d),c.push('":"'),c.push(null!==h?String(h):""),c.push('"},'))}u&&c.push('["starts-with", "$bucket", ""],'),l&&c.push('["starts-with", "$key", ""],'),c.push("]}");var f=c.join("");c=window.btoa?window.btoa(f):et.encode(f);var m=Wt("sha1",this.sk).update(c).digest("base64");return{OriginPolicy:f,Policy:c,Signature:m,Token:this.ak+":"+m+":"+c}},br.prototype.createV4PostSignatureSync=function(e){var t=(e=e||{}).signatureContext||this.signatureContext,r=e.Bucket?String(e.Bucket):null,n=e.Key?String(e.Key):null,o=e.Expires?parseInt(e.Expires,10):300,i={};if(e.FormParams&&e.FormParams instanceof Object&&!(e.FormParams instanceof Array))for(var a in e.FormParams)({}).hasOwnProperty.call(e.FormParams,a)&&(i[a]=e.FormParams[a]);this.securityToken&&!i[t.headerPrefix+"security-token"]&&(i[t.headerPrefix+"security-token"]=this.securityToken);var s=gr((new Date).toUTCString()),c=s[0],u=s[1],l=this.ak+"/"+c+"/"+this.region+"/s3/aws4_request",p=new Date;p.setTime(parseInt((new Date).getTime(),10)+1e3*o),p=yr(p.toUTCString()),i["X-Amz-Algorithm"]="AWS4-HMAC-SHA256",i["X-Amz-Date"]=u,i["X-Amz-Credential"]=l,r&&(i.bucket=r),n&&(i.key=n);var d=[];d.push('{"expiration":"'),d.push(p),d.push('", "conditions":[');var h=!0,f=!0,m=["acl","bucket","key","success_action_redirect","redirect","success_action_status"];for(var y in i)if(y){var g=i[y];"bucket"===(y=String(y).toLowerCase())?h=!1:"key"===y&&(f=!1),Xt.indexOf(y)<0&&m.indexOf(y)<0&&0!==y.indexOf(t.headerPrefix)||(d.push('{"'),d.push(y),d.push('":"'),d.push(null!==g?String(g):""),d.push('"},'))}h&&d.push('["starts-with", "$bucket", ""],'),f&&d.push('["starts-with", "$key", ""],'),d.push("]}");var A=d.join("");d=window.btoa?window.btoa(A):et.encode(A);var v=vr(c,this.sk,this.region,d);return{OriginPolicy:A,Policy:d,Algorithm:i["X-Amz-Algorithm"],Credential:i["X-Amz-Credential"],Date:i["X-Amz-Date"],Signature:v}};var wr=br,Pr=Number.MAX_VALUE;function kr(){this.consoleLog=window.console,this._level=Pr}kr.prototype.setLevel=function(e){e&&(e="info"===(e=String(e).toLowerCase())?20:"warn"===e?30:"error"===e?40:"debug"===e?10:Pr,this._level=e)},kr.prototype.runLog=function(e,t,r){if(e){var n=[(new Date).toLocaleString(),e.toLowerCase(),t,r].join("|");"debug"===e.toLowerCase()&&this._level<=10?this.consoleLog.debug(n):"info"===e.toLowerCase()&&this._level<=20?this.consoleLog.info(n):"warn"===e.toLowerCase()&&this._level<=30?this.consoleLog.warn(n):"error"===e.toLowerCase()&&this._level<=40&&this.consoleLog.error(n)}};var Sr=kr,Cr=o(1062),Tr=o.t(Cr,2),Er=function(e){return"[object String]"===Object.prototype.toString.call(e)&&e.lastIndexOf("/")!==e.length-1&&(e+="/"),e},Mr={extend:function(e){e.prototype.dropFile=function(e,t){this.deleteObject(e,t)},e.prototype.dropFolder=function(e,t){var r=this;e=e||{};var n=function(e,t,r){e=e||function(){};var n=(new Date).getTime();return function(r,o){return t.runLog("info","dropFolder","ObsClient cost "+((new Date).getTime()-n)+" ms"),"[object String]"===Object.prototype.toString.call(r)?e(new Error(r),o):e(r,o)}}(t,r.log),o=function(e){return e=e||function(){},function(t,r,n){return"[object Error]"===Object.prototype.toString.call(n)?e(t,r,n):"[object String]"===Object.prototype.toString.call(n)?e(t,r,new Error(n)):n?n.CommonMsg.Status>300?e(t,r,new Error("status:"+n.CommonMsg.Status+", code:"+n.CommonMsg.Code+", message:"+n.CommonMsg.Message)):void e(t,r,n):void 0}}(e.EventCallback),i=e.TaskNum||1,a=0,s=[],c=function(e,t,n,c){if(c)return t.finished++,void n(t);var u=function(){a++,r.dropFile({Bucket:t.bucket,Key:e},(function(r,c){a--,t.finished++,function(){for(;a<i&&s.length>0;)s.shift()()}(),r?(o("dropFileFailed",e,r),t.subDeleted=!1):c.CommonMsg.Status>=300?(o("dropFileFailed",e,c),t.subDeleted=!1):o("dropFileSucceed",e,c),n(t)}))};a<i?u():s.push(u)},u=function(e,t,r){return function(n){!n.isTruncated&&n.finished===n.total&&n.subDeleted&&c(e,t,r,!1)}},l=Er(e.Prefix);!function e(t,o,l,p,d){a++,r.listObjects({Bucket:o,Prefix:l,Delimiter:"/",Marker:p},(function(r,p){if(a--,r)return n(r);if(p.CommonMsg.Status>=300)return n(null,p);if(t.total+=p.InterfaceResult.Contents.length,t.total+=p.InterfaceResult.CommonPrefixes.length,0!==t.total){t.isTruncated="true"===p.InterfaceResult.IsTruncated;for(var h=function(r){return function(){e({total:0,finished:0,isTruncated:!1,bucket:o,subDeleted:!0},o,r,null,u(r,t,d))}},f=0;f<p.InterfaceResult.CommonPrefixes.length;f++){var m=Er(p.InterfaceResult.CommonPrefixes[f].Prefix);a<i?e({total:0,finished:0,isTruncated:!1,bucket:o,subDeleted:!0},o,m,null,u(m,t,d)):s.push(h(m))}for(var y=0;y<p.InterfaceResult.Contents.length;y++){var g=p.InterfaceResult.Contents[y].Key;c(g,t,d,g.lastIndexOf("/")===g.length-1)}t.isTruncated&&(a<i?e(t,o,l,p.InterfaceResult.NextMarker,d):s.push((function(){e(t,o,l,p.InterfaceResult.NextMarker,d)})))}else d(t)}))}({total:0,finished:0,isTruncated:!1,bucket:e.Bucket,subDeleted:!0},e.Bucket,l,null,(function(e){if(!e.isTruncated&&e.finished===e.total)if(e.subDeleted)r.dropFile({Bucket:e.bucket,Key:l},(function(e,t){return e?(o("dropFileFailed",l,e),n(e)):t.CommonMsg.Status>=300?(o("dropFileFailed",l,t),n(null,t)):(o("dropFileSucceed",l,t),n(null,t))}));else{var t="drop folder "+l+" failed due to child file deletion failed";o("dropFileFailed",l,new Error(t)),n(t)}}))}}},Or=Mr,Br=5368709120,Rr=function(e){var t=[];if(t.push(e.bucket),t.push(e.key),t.push(e.sourceFile.name),t.push(String(e.partSize)),t.push(String(e.partCount)),t.push(String(e.fileStat.fileSize)),t.push(String(e.fileStat.lastModified)),e.uploadId&&t.push(e.uploadId),e.sseC&&t.push(e.sseC),e.sseCKey&&t.push(e.sseCKey),e.parts)for(var r=0;r<e.parts.length;r++){var n=e.parts[r];n&&(t.push(String(n.partNumber)),t.push(String(n.offset)),t.push(String(n.partSize)),t.push(String(n.isCompleted)),n.etag&&t.push(String(n.etag)))}return window.btoa(rt()(t.join(""),!1,!0))},Ir=function(e,t,r){e&&e.uploadId&&r.abortMultipartUpload({Bucket:e.bucket,Key:e.key,RequestDate:e.requestDate,UploadId:e.uploadId},(function(n,o){n?r.log.runLog("warn",t,"abort multipart upload failed, bucket:"+e.bucket+", key:"+e.key+", uploadId:"+e.uploadId+", err:"+n):o.CommonMsg.Status>=300?r.log.runLog("warn",t,"abort multipart upload failed, bucket:"+e.bucket+", key:"+e.key+", uploadId:"+e.uploadId+", status:"+o.CommonMsg.Status+", code:"+o.CommonMsg.Code+", message:"+o.CommonMsg.Message):(delete e.uploadId,r.log.runLog("warn",t,"abort multipart upload succeed, bucket:"+e.bucket+", key:"+e.key+", uploadId:"+e.uploadId))}))},Dr=function(e){if(!(e.finishedCount<e.uploadCheckpoint.partCount)){if(e.isAbort)return Ir(e.uploadCheckpoint,e.funcName,e.that),e.callback("uploadFile failed the upload task is aborted");if(e.isSuspend)return e.callback("the process of uploadFile is suspened, you can retry with the uploadCheckpoint");if(e.hasError)return e.callback("uploadFile finished with error, you can retry with the uploadCheckpoint");for(var t=[],r=0;r<e.uploadCheckpoint.partCount;r++){var n=e.uploadCheckpoint.parts[r];t.push({PartNumber:n.partNumber,ETag:n.etag})}e.that.completeMultipartUpload({Bucket:e.uploadCheckpoint.bucket,Key:e.uploadCheckpoint.key,RequestDate:e.uploadCheckpoint.requestDate,UploadId:e.uploadCheckpoint.uploadId,Parts:t,CallbackUrl:e.callbackUrl,CallbackHost:e.callbackHost,CallbackBody:e.callbackBody,CallbackBodyType:e.callbackBodyType},(function(t,r){var n={bucket:e.uploadCheckpoint.bucket,key:e.uploadCheckpoint.key,uploadId:e.uploadCheckpoint.uploadId};return t?(e.eventCallback("completeMultipartUploadFailed",n,t),e.callback(t)):r.CommonMsg.Status>=500?(e.eventCallback("completeMultipartUploadFailed",n,r),e.callback(null,r)):r.CommonMsg.Status>=300&&r.CommonMsg.Status<500?(e.eventCallback("completeMultipartUploadAborted",n,r),Ir(e.uploadCheckpoint,e.funcName,e.that),e.callback(null,r)):(e.eventCallback("completeMultipartUploadSucceed",n,r),void e.callback(null,r))}))}},jr=function(e){e.resumeCallback({cancel:function(){e.isSuspend=!0;for(var t=0;t<e.uploadPartParams.length;t++){var r=e.uploadPartParams[t].cancelHook;n=r,"[object Function]"===Object.prototype.toString.call(n)&&r()}var n}},e.uploadCheckpoint);var t=[],r=function(){for(;e.runningTask<e.taskNum&&t.length>0;)t.shift()();0===t.length&&Dr(e)},n=window.btoa?window.btoa:et.encode,o=function(o){return function(){if(e.runningTask++,e.isSuspend||e.isAbort)return e.runningTask--,e.finishedCount++,e.finishedCount+=t.length,t=[],r();var i,a,s,c,u=0,l=function(t){if(!u){u=1;var n,i={Bucket:e.uploadCheckpoint.bucket,Key:e.uploadCheckpoint.key,RequestDate:e.uploadCheckpoint.requestDate,PartNumber:o.partNumber,UploadId:e.uploadCheckpoint.uploadId,SourceFile:e.uploadCheckpoint.sourceFile,Offset:o.offset,PartSize:o.partSize,SseC:e.uploadCheckpoint.sseC,SseCKey:e.uploadCheckpoint.sseCKey,ProgressCallback:(n=o.partNumber,function(t,r,o){e.progressCallback(n,t)}),ContentMD5:t};e.uploadPartParams.push(i),e.that.uploadPart(i,(function(t,n){if(e.runningTask--,e.finishedCount++,e.isSuspend)return r();var i={partNumber:o.partNumber,bucket:e.uploadCheckpoint.bucket,key:e.uploadCheckpoint.key,uploadId:e.uploadCheckpoint.uploadId};t?(e.eventCallback("uploadPartFailed",i,t),e.hasError=!0):n.CommonMsg.Status>=500||400===n.CommonMsg.Status&&"BadDigest"===n.CommonMsg.Code?(e.eventCallback("uploadPartFailed",i,n),e.hasError=!0):n.CommonMsg.Status>=300&&n.CommonMsg.Status<500?(e.isAbort=!0,e.hasError=!0,e.eventCallback("uploadPartAborted",i,n)):(o.etag=n.InterfaceResult.ETag,o.isCompleted=!0,i.etag=o.etag,e.uploadCheckpoint.md5=Rr(e.uploadCheckpoint),e.eventCallback("uploadPartSucceed",i,n),e.that.log.runLog("debug",e.funcName,"Part "+String(o.partNumber)+" is finished, uploadId "+e.uploadCheckpoint.uploadId)),r()}))}};if(e.verifyMd5&&window.FileReader&&(e.uploadCheckpoint.sourceFile instanceof window.File||e.uploadCheckpoint.sourceFile instanceof window.Blob)){var p=(i=e.uploadCheckpoint.sourceFile,a=o.offset,s=o.offset+o.partSize,c=c||i.type,i.mozSlice?i.mozSlice(a,s,c):i.webkitSlice?i.webkitSlice(a,s,c):i.slice(a,s,c)),d=new window.FileReader;return d.onload=function(e){var t=function(e){e=new Uint8Array(e);for(var t,r=0;r<e.length;){var n=r+16384;n=n<=e.length?n:e.length,t?t+=String.fromCharCode.apply(null,e.slice(r,n)):t=String.fromCharCode.apply(null,e.slice(r,n)),r=n}return e=null,t}(e.target.result),r=n(rt()(t,!1,!0));t=null,l(r)},d.onerror=function(t){e.that.log.runLog("error",e.funcName,"Caculate md5 for part "+String(o.partNumber)+" failed"),l()},void d.readAsArrayBuffer(p)}l()}};if(!e.isSuspend){for(var i=0;i<e.uploadCheckpoint.partCount;i++){var a=e.uploadCheckpoint.parts[i];a.isCompleted?(e.finishedCount++,e.finishedBytes+=a.partSize):t.push(o(a))}return 0===t.length?Dr(e):r()}e.callback("the process of uploadFile is suspened, you can retry with the uploadCheckpoint")},Lr={extend:function(e){e.prototype.uploadFile=function(e,t){var r=this;e=e||{};var n="uploadFile",o=function(e,t,r){e=e||function(){};var n=(new Date).getTime();return function(r,o){return t.runLog("info","uploadFile","ObsClient cost "+((new Date).getTime()-n)+" ms"),"[object String]"===Object.prototype.toString.call(r)?e(new Error(r),o):e(r,o)}}(t,r.log),i=function(e){return e=e||function(){},function(t,r,n){return"[object Error]"===Object.prototype.toString.call(n)?e(t,r,n):"[object String]"===Object.prototype.toString.call(n)?e(t,r,new Error(n)):n?n.CommonMsg.Status>300?e(t,r,new Error("status:"+n.CommonMsg.Status+", code:"+n.CommonMsg.Code+", message:"+n.CommonMsg.Message)):void e(t,r,n):void 0}}(e.EventCallback),a=e.TaskNum||1,s=e.ProgressCallback||function(){},c=e.ResumeCallback||function(){},u=e.VerifyMd5||!1;r.log.runLog("info",n,"enter uploadFile...");var l=null;if(e.UploadCheckpoint&&e.UploadCheckpoint.sourceFile&&e.UploadCheckpoint.fileStat&&e.UploadCheckpoint.uploadId&&e.UploadCheckpoint.md5===Rr(e.UploadCheckpoint)?l=e.UploadCheckpoint:Ir(e.UploadCheckpoint,n,r),l){var p=l.sourceFile;if(!(p instanceof window.File||p instanceof window.Blob))return o("source file is not valid, must be an instanceof [File | Blob]");if(!p.mozSlice&&!p.webkitSlice&&!p.slice)return o("your browser cannot support the slice method for [File | Blob]")}else{var d=e.SourceFile;if(!(d instanceof window.File||d instanceof window.Blob))return o("source file is not valid, must be an instanceof [File | Blob]");if(!d.mozSlice&&!d.webkitSlice&&!d.slice)return o("your browser cannot support the slice method for [File | Blob]");r.log.runLog("debug",n,"Begin to uploadFile to OBS from file:"+d.name);var h=d.size,f=parseInt(e.PartSize,10),m=0,y=[];if(0===h)f=0,m=1,y.push({partNumber:1,offset:0,partSize:0,isCompleted:!1});else{if(f=isNaN(f)||f<102400?9437184:f>Br?Br:f,(m=Math.floor(h/f))>=1e4&&(f=Math.floor(h/1e4),h%1e4!=0&&(f+=1),m=Math.floor(h/f)),f>Br)return o("The source file "+d.name+" is too large");var g=h%f;0!==g&&m++;for(var A=0;A<m;A++)y.push({partNumber:A+1,offset:A*f,partSize:f,isCompleted:!1});0!==g&&(y[m-1].partSize=g)}r.log.runLog("debug",n,"Total parts count "+m),(l={bucket:e.Bucket,key:e.Key,sourceFile:d,partSize:f,partCount:m,parts:y}).fileStat={fileSize:h,lastModified:d.lastModified},e.SseC&&e.SseCKey&&(l.sseC=e.SseC,l.sseCKey=e.SseCKey),l.md5=Rr(l)}l.requestDate=e.RequestDate;var v={start:(new Date).getTime(),uploadCheckpoint:l,funcName:n,taskNum:a,callback:o,that:r,runningTask:0,finishedCount:0,hasError:!1,finishedBytes:0,isAbort:!1,resumeCallback:c,isSuspend:!1,partsLoaded:{},requestDate:e.RequestDate,uploadPartParams:[],verifyMd5:u,callbackUrl:e.CallbackUrl,callbackHost:e.CallbackHost,callbackBody:e.CallbackBody,callbackBodyType:e.CallbackBodyType,eventCallback:function(e,t,r){v.isSuspend||i(e,t,r)},progressCallback:function(e,t){v.isSuspend||(v.finishedBytes+=t,v.partsLoaded[e]&&(v.finishedBytes-=v.partsLoaded[e]),v.partsLoaded[e]=t,s(v.finishedBytes,v.uploadCheckpoint.fileStat.fileSize,((new Date).getTime()-v.start)/1e3))}};if(!l.uploadId){var x=e.ContentType;return!x&&l.key&&(x=r.util.mimeTypes[l.key.substring(l.key.lastIndexOf(".")+1)]),!x&&l.sourceFile.name&&(x=r.util.mimeTypes[l.sourceFile.name.substring(l.sourceFile.name.lastIndexOf(".")+1)]),void r.initiateMultipartUpload({Bucket:e.Bucket,Key:e.Key,RequestDate:e.RequestDate,ACL:e.ACL,Metadata:e.Metadata,WebsiteRedirectLocation:e.WebsiteRedirectLocation,StorageClass:e.StorageClass,ContentType:x,Expires:e.Expires,SseKms:e.SseKms,SseKmsKey:e.SseKmsKey,SseC:e.SseC,SseCKey:e.SseCKey},(function(t,i){var a={bucket:e.Bucket,key:e.Key};if(t)return v.eventCallback("initiateMultipartUploadFailed",a,t),o(t);if(i.CommonMsg.Status>=300)return v.eventCallback("initiateMultipartUploadFailed",a,i),o(null,i);var s=i.InterfaceResult.UploadId;l.uploadId=s,l.md5=Rr(l),v.uploadCheckpoint=l,a.uploadId=s,r.log.runLog("info",n,"Claim a new upload id "+s),v.eventCallback("initiateMultipartUploadSucceed",a,i),jr(v)}))}jr(v)}}},_r=Lr;function qr(e){this.factory(e)}function Nr(e){return e.slice(0,1).toUpperCase()+e.slice(1)}var Fr=["createBucket","listBuckets","getBucketMetadata","headBucket","deleteBucket","setBucketQuota","getBucketQuota","getBucketStorageInfo","setBucketPolicy","getBucketPolicy","deleteBucketPolicy","setBucketVersioningConfiguration","getBucketVersioningConfiguration","putBackToSource","deleteBackToSource","getBackToSource","getBucketLocation","listVersions","listObjects","setBucketLifecycleConfiguration","getBucketLifecycleConfiguration","deleteBucketLifecycleConfiguration","setBucketAcl","getBucketAcl","setBucketLoggingConfiguration","getBucketLoggingConfiguration","setBucketWebsiteConfiguration","getBucketWebsiteConfiguration","deleteBucketWebsiteConfiguration","setBucketNotification","getBucketNotification","setBucketTagging","getBucketTagging","deleteBucketTagging","getBucketCors","deleteBucketCors","setBucketStoragePolicy","getBucketStoragePolicy","getObject","getObjectMetadata","setObjectMetadata","setObjectAcl","getObjectAcl","deleteObject","deleteObjects","listMultipartUploads","listParts","abortMultipartUpload","completeMultipartUpload","getBucketInventory","setBucketInventory","deleteBucketInventory","getBucketEncryption","setBucketEncryption","deleteBucketEncryption","getBucketRequesterPay","setBucketRequesterPay","setMirrorBackToSource","getMirrorBackToSource","deleteMirrorBackToSource","getWorkflowTrigger","deleteWorkflowTrigger","createWorkflowTrigger","restoreFailedWorkflowExecution","createTemplate","createWorkflow","getWorkflowList","deleteWorkflow","getWorkflowTemplateList","getWorkflowInstanceList","deleteTemplate","updateWorkflow","getActionTemplates","getWorkflowAuthorization","openWorkflowAuthorization","getBucketDirectColdAccess","setBucketDirectColdAccess","deleteBucketDirectColdAccess","getBucketCustomDomain","setBucketCustomDomain","deleteBucketCustomDomain","setBucketCors","getBucketReplication","setBucketReplication","deleteBucketReplication","getCDNNotifyConfiguration","setCdnNotifyConfiguration","getQuota","getBucketDisPolicy","setBucketDisPolicy","deleteBucketDisPolicy","createOnlineDecom","getOnlineDecom","getWorkflowAgreements","openWorkflowAgreements","deleteOnlineDecom","getMyActionTemplates","createMyActionTemplate","getMyactiontemplateDetail","updateMyActionTemplate","deleteMyActionTemplate","forbidMyActionTemplate","updatePublicActionTemplate","getOmPublicActionTemplates","setSFSAcl","getSFSAcl","deleteSFSAcl","setBucketAlias","bindBucketAlias","unbindBucketAlias","deleteBucketAlias","listBucketsAlias","getBucketAlias"];function Ur(e){return function(t,r){this.exec(Nr(e),t,r)}}for(var Kr=0;Kr<Fr.length;Kr++){var Gr=Fr[Kr];qr.prototype[Gr]=Ur(Gr)}function Hr(e){return"[object Function]"===Object.prototype.toString.call(e)}function zr(e){return function(t,r){if(Hr(t))e.call(this,null,t);else{if(!Hr(r)){var n=this;return new Promise((function(r,o){e.call(n,t,(function(e,t){if(e)return o(e);r(t)}))}))}e.call(this,t,r)}}}if(qr.prototype.createTemplate=function(e,t){e.ApiPath="v2/workflowtemplates",this.exec("CreateTemplate",e,t)},qr.prototype.createWorkflow=function(e,t){e.ApiPath="v2/workflows",this.exec("CreateWorkflow",e,t)},qr.prototype.restoreFailedWorkflowExecution=function(e,t){e.ApiPath="v2/workflowexecutions",this.exec("RestoreFailedWorkflowExecution",e,t)},qr.prototype.getWorkflowList=function(e,t){e.ApiPath="v2/workflows",this.exec("GetWorkflowList",e,t)},qr.prototype.deleteWorkflow=function(e,t){e.ApiPath="v2/workflows",this.exec("DeleteWorkflow",e,t)},qr.prototype.deleteTemplate=function(e,t){e.ApiPath="v2/workflowtemplates",this.exec("DeleteTemplate",e,t)},qr.prototype.getWorkflowTemplateList=function(e,t){e.ApiPath="v2/workflowtemplates",this.exec("GetWorkflowTemplateList",e,t)},qr.prototype.getWorkflowInstanceList=function(e,t){e.ApiPath="v2/workflowexecutions",this.exec("GetWorkflowInstanceList",e,t)},qr.prototype.updateWorkflow=function(e,t){e.ApiPath="v2/workflows",this.exec("UpdateWorkflow",e,t)},qr.prototype.getActionTemplates=function(e,t){e.ApiPath="v2/actiontemplates",this.exec("GetActionTemplates",e,t)},qr.prototype.getWorkflowAuthorization=function(e,t){e.ApiPath="v2/workflow-authorization",this.exec("GetWorkflowAuthorization",e,t)},qr.prototype.openWorkflowAuthorization=function(e,t){e.ApiPath="v2/workflow-authorization",this.exec("OpenWorkflowAuthorization",e,t)},qr.prototype.getPublicationTemplates=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/publicactiontemplates",this.exec("GetPublicationTemplates",e,t),this.util.pathStyle=!1},qr.prototype.getPublicationTemplateDetail=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/publicactiontemplates",this.exec("GetPublicationTemplateDetail",e,t),this.util.pathStyle=!1},qr.prototype.getWorkflowAgreements=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/workflow-agreements",this.exec("GetWorkflowAgreements",e,t),this.util.pathStyle=!1},qr.prototype.openWorkflowAgreements=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/workflow-agreements",this.exec("OpenWorkflowAgreements",e,t),this.util.pathStyle=!1},qr.prototype.createMyActionTemplate=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/myactiontemplates",this.exec("CreateMyActionTemplate",e,t),this.util.pathStyle=!1},qr.prototype.getMyActionTemplates=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/myactiontemplates",this.exec("GetMyActionTemplates",e,t),this.util.pathStyle=!1},qr.prototype.getMyactiontemplateDetail=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/myactiontemplates",this.exec("GetMyactiontemplateDetail",e,t),this.util.pathStyle=!1},qr.prototype.updateMyActionTemplate=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/myactiontemplates",this.exec("UpdateMyActionTemplate",e,t),this.util.pathStyle=!1},qr.prototype.deleteMyActionTemplate=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/myactiontemplates",this.exec("DeleteMyActionTemplate",e,t),this.util.pathStyle=!1},qr.prototype.forbidMyActionTemplate=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/myactiontemplates",this.exec("ForbidMyActionTemplate",e,t),this.util.pathStyle=!1},qr.prototype.updatePublicActionTemplate=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/ompublicactiontemplates",this.exec("UpdatePublicActionTemplate",e,t),this.util.pathStyle=!1},qr.prototype.getOmPublicActionTemplates=function(e,t){this.util.pathStyle=!0,e.ApiPath="v2/ompublicactiontemplates",this.exec("GetOmPublicActionTemplates",e,t),this.util.pathStyle=!1},qr.prototype.putObject=function(e,t){if("Body"in e&&"SourceFile"in e){var r="the input body and sourcefile exist at same time,please specify one of eigther a string or file to be send!";return this.log.runLog("error","PutObject",r),t(new Error(r),null)}if(!("ContentType"in e)&&("Key"in e&&(e.ContentType=this.util.mimeTypes[e.Key.substring(e.Key.lastIndexOf(".")+1)]),!e.ContentType&&"SourceFile"in e)){var n=e.SourceFile.name;e.ContentType=this.util.mimeTypes[n.substring(n.lastIndexOf(".")+1)]}this.exec("PutObject",e,t)},qr.prototype.appendObject=function(e,t){if("Body"in e&&"SourceFile"in e){var r="the input body and sourcefile exist at same time,please specify one of eigther a string or file to be send!";return this.log.isLevelEnabled("error")&&this.log.runLog("error","PutObject",r),t(new Error(r),null)}"ContentType"in e||("Key"in e&&(e.ContentType=this.util.mimeTypes[e.Key.substring(e.Key.lastIndexOf(".")+1)]),!e.ContentType&&"SourceFile"in e&&(e.ContentType=this.util.mimeTypes[e.SourceFile.substring(e.SourceFile.lastIndexOf(".")+1)])),this.exec("AppendObject",e,t)},qr.prototype.copyObject=function(e,t){var r="CopySource";if(r in e){var n=e[r],o=n.lastIndexOf("?versionId=");e[r]=o>0?this.util.encodeURIWithSafe(n.slice(0,o))+n.slice(o):this.util.encodeURIWithSafe(n)}this.exec("CopyObject",e,t)},qr.prototype.copyPart=function(e,t){var r="CopySource";if(r in e){var n=e[r],o=n.lastIndexOf("?versionId=");e[r]=o>0?this.util.encodeURIWithSafe(n.slice(0,o))+n.slice(o):this.util.encodeURIWithSafe(n)}this.exec("CopyPart",e,t)},qr.prototype.restoreObject=function(e,t){this.exec("RestoreObject",e,(function(e,r){!e&&r.InterfaceResult&&r.CommonMsg.Status<300&&(r.InterfaceResult.RestoreStatus=200===r.CommonMsg.Status?"AVALIABLE":"INPROGRESS"),t(e,r)}))},qr.prototype.initiateMultipartUpload=function(e,t){"ContentType"in e||"Key"in e&&(e.ContentType=this.util.mimeTypes[e.Key.substring(e.Key.lastIndexOf(".")+1)]),this.exec("InitiateMultipartUpload",e,t)},qr.prototype.uploadPart=function(e,t){if("Body"in e&&"SourceFile"in e){var r="the input body and sourcefile exist at same time, please specify one of eigther a string or file to be send!";return this.log.runLog("error","UploadPart",r),t(new Error(r),null)}this.exec("UploadPart",e,t)},Or.extend(qr),_r.extend(qr),Hr(Promise))for(var Wr in qr.prototype)if({}.hasOwnProperty.call(qr.prototype,Wr)){var Vr=qr.prototype[Wr];qr.prototype[Wr]=zr(Vr)}for(var Qr in qr.prototype.exec=function(e,t,r){var n=this.log;n.runLog("info",e,"enter "+e+"...");var o=(new Date).getTime();t=t||{},r=r||function(){},this.util.exec(e,t,(function t(i,a){t.$called||(t.$called=!0,!i||i instanceof Error||(i=new Error(i)),n.runLog("debug",e,"ObsClient cost "+((new Date).getTime()-o)+" ms"),r(i,a))}))},qr.prototype.initLog=function(e){e=e||{},this.log.setLevel(e.level);var t=["[OBS SDK Version="+this.util.obsSdkVersion];if(this.util.server){var r=this.util.port?":"+this.util.port:"";t.push("Endpoint="+(this.util.is_secure?"https":"http")+"://"+this.util.server+r)}t.push("Access Mode="+(this.util.path_style?"Path":"Virtual Hosting")+"]"),this.log.runLog("warn","init",t.join("];["))},qr.prototype.factory=function(e){this.log=new Sr,this.util=new wr(this.log),e=e||{},this.util.initFactory(e.access_key_id,e.secret_access_key,e.is_secure,e.server,e.path_style,e.signature,e.region,e.port,e.timeout,e.security_token,e.is_signature_negotiation,e.is_cname,e.url_prefix,e.region_domains,e.setRequestHeaderHook,e.useRawXhr)},qr.prototype.refresh=function(e,t,r){this.util.refresh(e,t,r)},qr.prototype.createSignedUrlSync=function(e){return this.util.createSignedUrlSync(e)},qr.prototype.createV2SignedUrlSync=function(e){return this.util.createV2SignedUrlSync(e)},qr.prototype.createV4SignedUrlSync=function(e){return this.util.createV4SignedUrlSync(e)},qr.prototype.createPostSignatureSync=function(e){return this.util.createPostSignatureSync(e)},qr.prototype.createV4PostSignatureSync=function(e){return this.util.createV4PostSignatureSync(e)},qr.prototype.enums=Tr,qr.prototype)({}).hasOwnProperty.call(qr.prototype,Qr)&&(qr.prototype[Nr(Qr)]=qr.prototype[Qr]);for(var Yr in qr.prototype)if({}.hasOwnProperty.call(qr.prototype,Yr)){var Xr=Yr.indexOf("Configuration");Xr>0&&Xr+"Configuration".length===Yr.length&&(qr.prototype[Yr.slice(0,Xr)]=qr.prototype[Yr])}var $r=qr}(),i.default}()}));