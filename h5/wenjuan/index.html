<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>问卷调查</title>
    <link rel="stylesheet" href="./libs/vant/index.css" />
    <!-- 引入 Vue 和 Vant 的 JS 文件 -->
    <script src="./libs/vue.global.js"></script>
    <script src="./libs/vant/vant.min.js"></script>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <link rel="stylesheet" href="./index.css">
</head>

<body style="max-width: 400px; overflow: hidden; margin: 0 auto; background: #F6F6F6; opacity: 0;">
    <div id="app">
        <div class="main" v-show="mainData">
            <div class="page-index" v-if="page == 'index'">
                <div class="center">
                    <img src="./<EMAIL>" alt="">
                    <h2>{{ mainData.title }}</h2>
                    <p class="text">{{ mainData.desc }}</p>
                </div>
                <div class="bottom">
                    <van-button :disabled="!agree" type="primary" size="large" @click="start">进入答卷</van-button>
                    <van-checkbox v-model="agree">同意</van-checkbox>
                </div>
                <van-popup closeable class="login" v-model:show="loginShow" teleport="body"
                    :safe-area-inset-bottom="true">
                    <h3>请先填写用户信息</h3>
                    <van-field size="large" label-align="right" label-width="100px" v-model="user.name"
                        label="姓名："></van-field>
                    <van-field size="large" label-align="right" label-width="100px" v-model="user.phone" type="tel"
                        label="手机号：">
                        <template #button>
                            <van-button v-if="codetime == 60" :disabled="user.phone == ''" style="margin-right: 20px;"
                                size="small" type="primary" @click="sendCode">发送验证码</van-button>
                            <van-button v-else style="margin-right: 20px;" size="small" disabled>{{ codetime
                                }}s后重试</van-button>
                        </template>
                    </van-field>
                    <van-field size="large" label-align="right" label-width="100px" v-model="user.code" label="验证码："
                        @update:model-value="getSchool"></van-field>
                    <van-field v-if="schoollist.length" size="large" label-align="right" label-width="100px"
                        :disabled="user.disabled" label="机构名称：">
                        <template #input>
                            <van-radio-group v-model="user.school_name" v-if="schoollist.length">
                                <van-radio size="small" :name="option.abbreviation"
                                    v-for="(option, option_index) in schoollist" :key="option_index">{{
                                    option.abbreviation }}
                                </van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                    <van-field v-else size="large" label-align="right" label-width="100px" :disabled="user.disabled"
                        v-model="user.school_name" label="机构名称：">
                    </van-field>
                    </van-radio-group>
                    <van-button
                        :disabled="user.name == '' || user.phone == '' || user.code == '' || user.school_name == ''"
                        style="width: 160px; position: relative; left: 50%; margin: 24px 0 24px -80px;" type="primary"
                        @click="login">提交</van-button>
                </van-popup>
            </div>
            <div class="page-form" v-show="page == 'form'">
                <h2>{{ mainData.title }}</h2>
                <p class="text">{{ mainData.desc }}</p>
                <van-form @submit="onSubmit">
                    <div class="item" :id="'item'+item.index" v-show="!jumpThis(item.index)"
                        :class="{require: item.is_required}" v-for="(item, index) in mainData.form" :key="index">
                        <p class="title"><span v-if="item.is_show_order">{{ index+1 }}：</span>{{ item.title }}</p>
                        <p class="text" v-if="item.is_show_desc">{{ item.desc }}</p>
                        <!-- 单选 -->
                        <template v-if="item.type == 101 || item.type == 303">
                            <van-radio-group v-model="item.value">
                                <van-radio :name="option.value" @click="jump(item, option)"
                                    v-for="(option, option_index) in item.option" :key="option_index">
                                    <div style="display: flex; align-items: center;">
                                        <span style="min-width: 50px;"> {{ option.title }}</span>
                                        <van-field v-if="item.value == option.value && option.input_status == 1"
                                            v-model="option.input_text" :placeholder="'请输入'"></van-field>
                                    </div>
                                </van-radio>
                            </van-radio-group>

                        </template>
                        <!-- 多选 -->
                        <template v-if="item.type == 102">
                            <van-checkbox-group v-model="item.value">
                                <van-checkbox shape="square" :name="option.value" @click="jump(item, option)"
                                    v-for="(option, option_index) in item.option" :key="option_index">
                                    <div style="display: flex; align-items: center;">
                                        <span style="min-width: 50px;"> {{ option.title }}</span>
                                        <van-field @click.stop=""
                                            v-if="item.value.indexOf(option.value) != -1 && option.input_status == 1"
                                            v-model="option.input_text" :placeholder="'请输入'"></van-field>
                                    </div>
                                </van-checkbox>
                            </van-checkbox-group>
                        </template>
                        <template v-if="item.type == 201 || item.type == 301 || item.type == 302">
                            <van-field v-model="item.value" :formatter="item.type == 302 ? formatter : ''"
                                :maxlength="item.max" :placeholder="item.placeholder"
                                :type="item.type == 302 ? 'tel' : 'text'"></van-field>
                        </template>
                        <template v-if="item.type == 202">
                            <van-field v-model="item.value" type="textarea" :maxlength="item.max" show-word-limit
                                rows="3" autosize :placeholder="item.placeholder"></van-field>
                        </template>
                        <template v-if="item.type == 304">
                            <van-field readonly v-model="item.value" @click="showPop(item)"
                                :placeholder="'请选择日期'"></van-field>
                        </template>
                        <template v-if="item.type == 305">
                            <van-field readonly v-model="item.province" @click="cityLevel = 1;showPop(item)"
                                :placeholder="'请选择省份'"></van-field>
                            <van-field v-if="item.level > 1" readonly v-model="item.city"
                                @click="cityLevel = 2;showPop(item)" :placeholder="'请选择城市'"></van-field>
                            <van-field v-if="item.level > 2" readonly v-model="item.area"
                                @click="cityLevel = 3;showPop(item)" :placeholder="'请选择区县'"></van-field>
                            <van-field v-if="item.level > 3" readonly v-model="item.street"
                                @click="cityLevel = 4;showPop(item)" :placeholder="'请选择街道'"></van-field>
                            <van-field v-if="item.level > 4" v-model="item.address" type="textarea"
                                :maxlength="item.max" show-word-limit rows="3" autosize
                                :placeholder="'请输入详细地址'"></van-field>
                        </template>
                        <template v-if="item.type == 306">
                            <canvas id="signature-pad" style="border:1px solid #000000;"></canvas>
                            <van-button style="margin: 20px 24px 0 0;" @click="signaturePad.clear()">清除签名</van-button>
                        </template>
                        <template v-if="item.type == 401">
                            <van-uploader :after-read="afterRead" v-model="item.value" multiple :max-count="item.max"
                                :max-size="10000 * 1024" @oversize="onOversize"></van-uploader>
                        </template>
                        <template v-if="item.type == 402">
                            <van-rate v-model="item.value"></van-rate>
                            <van-field v-if="item.is_required_text" v-model="item.text" type="textarea"
                                :maxlength="item.max" show-word-limit rows="3" autosize
                                :placeholder="item.placeholder"></van-field>
                        </template>
                        <van-divider></van-divider>
                    </div>
                    <div class="bottom" v-if="mainData.status == 1">
                        <van-button type="primary" native-type="submit" size="large">提交答卷</van-button>
                        <!-- <p class="text" style="text-align: center;">该问卷调查功能 由京学科技集团技术支持</p> -->
                    </div>
                </van-form>
                <van-popup v-model:show="popupShow" teleport="body" :safe-area-inset-bottom="true" position="bottom">
                    <van-date-picker v-if="activeItem.type == 304" :min-date="new Date(1900, 1, 1)"
                        v-model="currentDate" title="选择日期" @confirm="dateConfirm"
                        @cancel="popupCancel"></van-date-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 1"
                        title="请选择省份" :columns="province_list" @confirm="cityConfirm"
                        @cancel="popupCancel"></van-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 2"
                        title="请选择城市" :columns="city_list" @confirm="cityConfirm" @cancel="popupCancel"></van-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 3"
                        title="请选择区县" :columns="area_list" @confirm="cityConfirm" @cancel="popupCancel"></van-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 4"
                        title="请选择街道" :columns="street_list" @confirm="cityConfirm" @cancel="popupCancel"></van-picker>
                </van-popup>
            </div>
            <div class="page-finish" v-if="page == 'finish'">
                <img style="width: 30%" src="./<EMAIL>" alt="">
                <h3 style="margin: 40px 0; width: 250px; text-align: center;">{{ mainData.deal_way_msg ||
                    "您已完成本次答卷，感谢您的帮助与支持" }}</h3>
                <!-- <p class="text">分享给您的好友</p>
                <div class="bottom">
                    <van-button style="width: 300px" type="primary" size="large" @click="showShareTip">分享</van-button>
                </div> -->
                <!-- <van-share-sheet v-model:show="shareShow" title="立即分享给好友" :options="shareOptions" /> -->
            </div>
        </div>
    </div>
</body>

</html>
<script src="./libs/obs/esdk-obs-browserjs.3.22.3.min.js"></script>
<script src="./libs/signature_pad.umd.min.js"></script>
<script src="./libs/areaList.js"></script>
<script src="./libs/obs.js"></script>

<script>
    const { createApp, ref, reactive, computed, onMounted } = Vue;
    const { Area, Button, RadioGroup, Radio, Input, Popup, Toast, Cascader, Checkbox, CheckboxGroup, DatePicker, Form, Field, CellGroup, Picker, Uploader, Dialog, Loading, ShareSheet, Divider, ImagePreview, Rate, showSuccessToast, showFailToast, showLoadingToast, showToast, closeToast, showDialog } = vant;

    var searchParams = new URLSearchParams(window.location.search);
    let baseUrl = location.origin;

    const app = createApp({
        setup() {
            const id = ref('');
            const mainData = ref('');
            const agree = ref(false);
            const page = ref('index');
            const jumpIndex = ref({});
            const popupShow = ref(false);
            const shareShow = ref(false);
            const areaList = ref(windowAreaList)
            const shareOptions = ref([
                { name: '微信', icon: 'wechat' },
                { name: '朋友圈', icon: 'wechat-moments' },
                { name: '复制链接', icon: 'font-o' },
            ],)
            const city_list = ref([]);
            const area_list = ref([]);
            const street_list = ref([]);
            const province_list = ref([]);

            const user = reactive({
                login: false,
                name: '',
                phone: '',
                code: '',
                school_name: '',
                token: '',
                disabled: false
            })

            let loading = showLoadingToast('加载中...');

            let start_time = '';
            let isMini = ref(false);
            onMounted(async () => {
                start_time = new Date().getTime();
                id.value = searchParams.get('id');
                if (searchParams.get('p') && searchParams.get('n') && searchParams.get('s')) {
                    user.login = true;
                    user.name = searchParams.get('n');
                    user.phone = searchParams.get('p');
                    user.school_name = searchParams.get('s');
                    user.token = searchParams.get('t');
                }
                if (id.value) {
                    let response = await fetch(`${baseUrl}/api/jyform/detail?id=${id.value}&pv=1`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json', // 如果发送JSON数据
                            // 或者如果是表单数据：
                            // 'Content-Type': 'application/x-www-form-urlencoded'
                            'Authorization': "Bearer " + user.token
                        },
                    })
                    if (response.status == 200) {
                        document.body.style.opacity = 1;
                        let data = await response.json();
                        if (data.code == 200) {
                            loading.close();
                            if (data.data.form) {
                                for (const iterator of data.data.form) {
                                    if (iterator.type == 401) {
                                        iterator.value = [];
                                    }
                                }
                            }
                            mainData.value = data.data;
                            if (mainData.value.status == 0) {
                                showDialog({
                                    message: '该问卷还未发布',
                                }).then(() => {
                                    // on close
                                });
                                return;
                            }
                            if (mainData.value.status == 2) {
                                showDialog({
                                    message: '当前问卷调查已截止，感谢您的支持。',
                                }).then(() => {
                                    // on close
                                });
                                return;
                            }

                            setTimeout(() => {
                                signInit();
                            }, 300);
                        } else {
                            showFailToast(data.message);
                        }
                    }
                }
                if (user.login) {
                    page.value = 'form';
                }
            });

            const cityLevel = ref(1);
            async function getProvince(code, type) {
                let response = await fetch(`${baseUrl}/api/auth/areastreet?code=${code}&type=${type}`, {
                    method: 'GET'
                })
                if (response.status == 200) {
                    let data = await response.json();
                    if (data.code == 200) {
                        return data.data
                    } else {
                        showFailToast(data.message);
                    }
                }
            }

            const onOversize = (file) => {
                console.log(file);
                showToast('文件大小不能超过 10Mb');
            };

            const beforeRead = (file) => {
                if (file.type !== 'image/jpeg' && file.type !== "image/jpg" && file.type !== "image/png") {
                    showToast('请上传 jpg，png 格式图片');
                    return false;
                }
                return true;
            };

            const loginShow = ref(false);

            function start() {
                if (mainData.value.status == 0) {
                    showDialog({
                        message: '该问卷还未发布',
                    }).then(() => {
                        // on close
                    });
                    return;
                }
                if (mainData.value.status == 2) {
                    showDialog({
                        message: '当前问卷调查已截止，感谢您的支持。',
                    }).then(() => {
                        // on close
                    });
                    return;
                }
                if (mainData.value.is_auth == 1) {
                    loginShow.value = true;
                } else {
                    page.value = 'form';
                }
            }

            function jump(item, option) {
                // console.log(item, option);
                let target = item.jump_type == 1 ? option.jump_to : item.jump_to;
                console.log(target);
                if (target) {
                    let arr = [];
                    let start = 0;
                    for (let index = 0; index < mainData.value.form.length; index++) {
                        let el = mainData.value.form[index];
                        if (el.index == item.index) {
                            start = index;
                            break;
                        };

                    }

                    for (let index = start; index < mainData.value.form.length; index++) {
                        let el = mainData.value.form[index];
                        if (index > start) {
                            arr.push(el.index);
                            jumpIndex.value[el.index] = []
                        }
                        if (el.index == target) {
                            break;
                        };
                    }

                    console.log(start);
                    arr.pop();
                    jumpIndex.value[item.index] = arr;
                } else {
                    jumpIndex.value[item.index] = []
                }
                console.log(jumpIndex.value);
            }

            function jumpThis(index) {
                let jump = false;
                for (const key in jumpIndex.value) {
                    if (Object.hasOwnProperty.call(jumpIndex.value, key)) {
                        const element = jumpIndex.value[key];
                        if (element.length && element.indexOf(index) != -1) {
                            jump = true;
                        }
                    }
                }
                return jump;
            }

            const formatter = (value) => value.replace(/\D/g, '');

            const activeItem = ref('');
            let currentDate = ref('');

            function dateConfirm(value) {
                activeItem.value.value = value.selectedValues.join('-');
                popupShow.value = false
            }

            async function cityConfirm(value) {
                if (activeItem.value.value == '') {
                    activeItem.value.value = ['', '', '', ''];
                }
                if (cityLevel.value == 1) {
                    activeItem.value.province = value.selectedOptions[0].text;
                    activeItem.value.value[0] = value.selectedOptions[0].code;
                    activeItem.value.city = '';
                    activeItem.value.area = '';
                    activeItem.value.street = '';
                    city_list.value = await getProvince(value.selectedOptions[0].code, cityLevel.value + 1)
                } else if (cityLevel.value == 2) {
                    activeItem.value.city = value.selectedOptions[0].text;
                    activeItem.value.value[1] = value.selectedOptions[0].code;
                    activeItem.value.area = '';
                    activeItem.value.street = '';
                    area_list.value = await getProvince(value.selectedOptions[0].code, cityLevel.value + 1)
                    console.log(area_list.value);
                } else if (cityLevel.value == 3) {
                    activeItem.value.area = value.selectedOptions[0].text;
                    activeItem.value.value[2] = value.selectedOptions[0].code;
                    activeItem.value.street = '';
                    street_list.value = await getProvince(value.selectedOptions[0].code, cityLevel.value + 1)
                    console.log(street_list.value);
                } else if (cityLevel.value == 4) {
                    activeItem.value.street = value.selectedOptions[0].text;
                    activeItem.value.value[3] = value.selectedOptions[0].code;
                }
                popupShow.value = false
            }

            function popupCancel() {
                popupShow.value = false
            }

            function areaCancel() {
                popupShow.value = false
            }

            async function showPop(item) {
                if (item.type == 304) {
                    if (item.type == 304) {
                        if (item.value == '') {
                            let date = new Date();
                            currentDate.value = [date.getFullYear(), date.getMonth() + 1, date.getDate()]
                        } else {
                            currentDate.value = item.value.split('-');
                        }
                    }
                }
                if (item.type == 305 && province_list.value.length == 0) {
                    province_list.value = await getProvince(0, 1);
                }
                if (item.type == 305 && cityLevel.value == 2 && item.province == '') {
                    showToast('请先选择省份')
                    return
                }
                if (item.type == 305 && cityLevel.value == 3 && item.city == '') {
                    showToast('请先选择城市')
                    return
                }
                if (item.type == 305 && cityLevel.value == 4 && item.area == '') {
                    showToast('请先选择区县')
                    return
                }


                activeItem.value = item;
                popupShow.value = true;
            }


            const currentArea = ref('');

            async function afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                try {
                    let url = await windowUpload(file.file, `wenjuan`);
                    if (url) {
                        file.url = url;
                        file.status = 'success';
                        file.message = '';
                    }
                } catch (error) {
                    file.status = 'failed';
                    file.message = '上传失败';
                }
            }

            function showShareTip() {
                showDialog({
                    message: '请点击右上角分享给好友或朋友圈',
                }).then(() => {
                    // on close
                });
            }

            const onSubmit = async (values) => {
                let newData = mainData.value.form ? JSON.parse(JSON.stringify(mainData.value.form)) : [];
                for (const iterator of newData) {
                    if (iterator.type == 306 && signaturePad.value) {
                        let file = base64ToFile(signaturePad.value.toDataURL(), '电子签名.png');
                        console.log(file);
                        let url = await windowUpload(file, `wenjuan`);
                        if (url) {
                            iterator.value = url;
                        }
                    }
                    if (iterator.type == 401) {
                        iterator.value = iterator.value.map(e => { return e.url })
                    }
                    if (iterator.type == 305) {
                        iterator.value = iterator.province + iterator.city + iterator.area + iterator.street + iterator.address;
                    }
                }
                console.log(newData);
                let jumpArr = [];
                for (const key in jumpIndex.value) {
                    if (Object.hasOwnProperty.call(jumpIndex.value, key)) {
                        const element = jumpIndex.value[key];
                        jumpArr = [...jumpArr, ...element];
                    }
                }
                console.log(jumpArr);

                for (const iterator of newData) {
                    console.log(iterator.is_required, iterator.value,);
                    if (iterator.is_required && jumpArr.indexOf(iterator.index) == -1) {
                        if (
                            (iterator.type == 401 && iterator.value.length == 0) ||
                            (iterator.type == 402 && iterator.is_required_text && iterator.text == '') ||
                            (iterator.type == 102 && iterator.value.length == 0) ||
                            (iterator.type == 305 && iterator.province == '' && iterator.level == 1) ||
                            (iterator.type == 305 && iterator.city == '' && iterator.level == 2) ||
                            (iterator.type == 305 && iterator.area == '' && iterator.level == 3) ||
                            (iterator.type == 305 && iterator.street == '' && iterator.level == 4) ||
                            (iterator.type == 305 && iterator.address == '' && iterator.level == 5) ||
                            ((iterator.type != 401 && iterator.type != 402 && iterator.type != 102) && iterator.value == '')
                        ) {
                            showFailToast('请检查必填项');
                            const targetElement = document.getElementById("item" + iterator.index);
                            targetElement.scrollIntoView({ behavior: "smooth" });
                            return;
                        }
                    }
                }

                loading = showLoadingToast('提交中...');
                let response = await fetch(`${baseUrl}/api/jyform/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json', // 如果发送JSON数据
                        // 或者如果是表单数据：
                        // 'Content-Type': 'application/x-www-form-urlencoded'
                        'Authorization': "Bearer " + user.token
                    },
                    body: JSON.stringify({
                        name: user.name,
                        phone: user.phone,
                        school_name: user.school_name,
                        form: newData.length ? JSON.stringify(newData) : '',
                        form_id: id.value,
                        time_long: (new Date().getTime() - start_time) / 1000
                    })
                })
                if (response.status == 200) {
                    let data = await response.json();
                    if (data.code == 200) {
                        loading.close();
                        showSuccessToast('提交成功');
                        if (mainData.value.deal_way_msg) {
                            page.value = 'finish';
                        } else if (mainData.value.deal_way_url) {
                            location.href = mainData.value.deal_way_url;
                        } else {
                            page.value = 'finish';
                        }
                        // let wxresponse = await fetch(`${baseUrl}/api/wxjsconfig?url=${encodeURIComponent(window.location.href.split('#')[0])}`, {
                        //     method: 'GET'
                        // })
                        // if (wxresponse.status == 200) {
                        //     let data = await wxresponse.json();
                        //     console.log(data);
                        //     // wxShareInit(data);
                        // }
                    } else {
                        showFailToast(data.message);
                    }
                }

            };

            const codetime = ref(60);
            let codetimer = '';
            let schoollist = ref([]);
            async function sendCode() {
                codetimer = setInterval(() => {
                    codetime.value--;
                    if (codetime.value == 0) {
                        clearInterval(codetimer);
                        codetime.value = 60;
                    }
                }, 1000);
                let school = await fetch(`${baseUrl}/api/jyform/getschool?phone=${user.phone}`, {
                    method: 'GET'
                })
                if (school.status == 200) {
                    let data = await school.json();
                    if (data.code == 200) {
                        schoollist.value = data.data;
                        if (schoollist.value.length) {
                            user.school_name = '';
                        }
                    } else {
                        showFailToast(data.message);
                    }
                }
                let response = await fetch(`${baseUrl}/api/auth/getphonecode`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json', // 如果发送JSON数据
                        // 或者如果是表单数据：
                        // 'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: JSON.stringify({
                        phone: user.phone,
                    })
                })
                if (response.status == 200) {
                    let data = await response.json();
                    if (data.code == 200) {
                        showSuccessToast('发送成功');
                        codetimer = setInterval(() => {
                            codetime.value--;
                            if (codetime.value == 0) {
                                clearInterval(codetimer);
                                codetime.value = 60;
                            }
                        }, 1000);

                        let school = await fetch(`${baseUrl}/api/jyform/getschool?phone=${user.phone}`, {
                            method: 'GET'
                        })
                        if (school.status == 200) {
                            let data = await school.json();
                            if (data.code == 200) {
                                schoollist.value = data.data;
                                if (schoollist.value.length) {
                                    user.school_name = '';
                                }
                            } else {
                                showFailToast(data.message);
                            }
                        }
                    } else {
                        showFailToast(data.message);
                    }
                }
            }

            async function login() {
                let response = await fetch(`${baseUrl}/api/jyform/checkcode`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json', // 如果发送JSON数据
                        // 或者如果是表单数据：
                        // 'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: JSON.stringify({
                        phone: user.phone,
                        code: user.code
                    })
                })
                if (response.status == 200) {
                    let data = await response.json();
                    if (data.code == 200) {
                        loginShow.value = false;
                        page.value = 'form'
                    }
                }
            }

            function base64ToFile(base64String, fileName) {
                var byteString = atob(base64String.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                var blob = new Blob([ab], { type: 'application/octet-stream' });
                var file = new File([blob], fileName, { type: 'application/octet-stream' });
                return file;
            }

            function wxShareInit(res) {//详情分享
                wx.config({
                    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: res.data.appId, // 必填，公众号的唯一标识
                    timestamp: res.data.timestamp, // 必填，生成签名的时间戳
                    nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
                    signature: res.data.signature,// 必填，签名
                    jsApiList: ["onMenuShareAppMessage", "onMenuShareTimeline"] // 必填，需要使用的JS接口列表
                });

                wx.ready(function () {
                    // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
                    //分享给朋友
                    wx.onMenuShareAppMessage({
                        title: 'title', // 分享标题
                        desc: 'desc', // 分享描述
                        link: window.location.href.split('#')[0], // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        imgUrl: 'img', // 分享图标
                        type: '', // 分享类型,music、video或link，不填默认为link
                        dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
                        success: function () {
                            // 用户点击了分享后执行的回调函数
                        }
                    });

                    //分享到朋友圈
                    wx.onMenuShareTimeline({
                        title: 'title', // 分享标题
                        link: window.location.href.split('#')[0], // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        imgUrl: 'img', // 分享图标
                        success: function () {
                            // 用户点击了分享后执行的回调函数
                        }
                    });
                });
                wx.error(function (res) {
                    console.log(res);
                });
            }


            const signaturePad = ref('');
            function signInit() {
                var canvas = document.getElementById('signature-pad');
                console.log('canvas', canvas);
                if (canvas) {
                    signaturePad.value = new SignaturePad(canvas);
                    console.log('签名板', canvas, signaturePad.value);
                }
                // signaturePad.toDataURL();
                // signaturePad.clear();
                // signaturePad.isEmpty();
            }

            return {
                mainData,
                agree,
                start,
                page,
                jump,
                jumpIndex,
                jumpThis,
                formatter,
                popupShow,
                showPop,
                dateConfirm,
                cityConfirm,
                popupCancel,
                areaCancel,
                currentDate,
                activeItem,
                areaList,
                currentArea,
                afterRead,
                onSubmit,
                user,
                loginShow,
                sendCode,
                codetime,
                schoollist,
                shareOptions,
                shareShow,
                login,
                signaturePad,
                showShareTip,
                province_list,
                cityLevel,
                city_list,
                area_list,
                street_list,
                onOversize
            }
        }
    })
    app.use(Button);
    app.use(RadioGroup);
    app.use(Radio);
    app.use(Input);
    app.use(Popup);
    app.use(Toast);
    app.use(Cascader);
    app.use(Checkbox);
    app.use(CheckboxGroup);
    app.use(DatePicker);
    app.use(Form);
    app.use(Field);
    app.use(CellGroup);
    app.use(Picker);
    app.use(Uploader);
    app.use(Dialog);
    app.use(Loading);
    app.use(ShareSheet);
    app.use(Divider);
    app.use(ImagePreview);
    app.use(Rate);
    app.use(Area);
    app.mount('#app');
</script>