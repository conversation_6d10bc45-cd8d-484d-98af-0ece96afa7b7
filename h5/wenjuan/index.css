#app {
    /* touch-action: none; */
    background: #F6F6F6;
    height: 100%;
}

[v-clock] {
    display: none;
}

body {
    margin: 0;
    padding: 0;
    height: 100vh;
}

.main {
    overflow: auto;
    height: 100%;
}

.van-radio,
.page-form .van-checkbox,
.page-form .van-field {
    margin: 12px 0;
}

.page-form .van-field {
    padding: 10px 10px;
    border-radius: 4px;
    background: #f8f9fb;
    font-size: 16px;
}

.page-form .van-field input {
    height: 30px;
    line-height: 30px;
}

.page-form .van-cell:after {
    display: none;
}

.big-button {
    width: calc(100% - 48px)
}

h2,
p {
    margin: 12px 0;
}

.item {
    margin-top: 24px;
    position: relative;
}

.item.require::before {
    content: '*';
    position: absolute;
    color: red;
    left: -10px;
    top: 0;
}

.title {
    font-weight: bold;
}

.text {
    color: #666;
}

.page-index {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 120px;
    box-sizing: border-box;
}

.center {
    width: calc(100% - 48px);
    background: #fff;
    padding-bottom: 24px;
    position: relative;
    height: 100%;
    top: 40vh;
    transform: translateY(-50%);
}

.center::after {
    content: '京学集团技术支持';
    position: absolute;
    color: #AEB0BC;
    bottom: -40px;
    width: 100%;
    text-align: center;
}

.center img {
    width: 100%;
}

.center p,
.center h2 {
    padding: 0 24px;
}

.page-form {
    padding: 12px 24px;
    background: #fff;
}

.page-index .van-checkbox {
    margin-top: 12px;
}

.page-index .bottom {
    background: #fff;
    height: 140px;
    position: fixed;
    bottom: 0;
    padding: 20px 24px;
    overflow: hidden;
    max-width: 400px;
    left: 50%;
    width: 100%;
    transform: translateX(-50%);
    box-sizing: border-box;
}

.page-finish {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.login {
    border-radius: 8px;
}

.login h3 {
    text-align: center;
}

.login .van-field {
    padding: 12px 0;
    display: flex;
    align-items: center;
}

#signature-pad {
    width: 100%;
    height: 150px;
}
