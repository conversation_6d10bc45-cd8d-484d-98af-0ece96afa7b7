<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>问卷调查</title>
    <link rel="stylesheet" href="./libs/vant/index.css" />
    <!-- 引入 Vue 和 Vant 的 JS 文件 -->
    <script src="./libs/vue.global.js"></script>
    <script src="./libs/vant/vant.min.js"></script>
    <link rel="stylesheet" href="./index.css">
</head>

<body style="max-width: 400px; overflow: hidden; margin: 0 auto; background: #F6F6F6; opacity: 0;">
    <div id="app">
        <div class="main" v-show="mainData">
            <div class="page-form">
                <h2>{{ mainData.title }}</h2>
                <p class="text">{{ mainData.desc }}</p>
                <van-form @submit="onSubmit">
                    <div class="item" :id="'item'+item.index" v-show="!jumpThis(item.index)"
                        :class="{require: item.is_required}" v-for="(item, index) in mainData.form" :key="index">
                        <p class="title"><span v-if="item.is_show_order">{{ index+1 }}：</span>{{ item.title }}</p>
                        <p class="text" v-if="item.is_show_desc">{{ item.desc }}</p>
                        <!-- 单选 -->
                        <template v-if="item.type == 101 || item.type == 303">
                            <van-radio-group v-model="item.value">
                                <van-radio :name="option.value" @click="jump(item, option)"
                                    v-for="(option, option_index) in item.option" :key="option_index">
                                    <div style="display: flex; align-items: center;">
                                        <span style="min-width: 50px;"> {{ option.title }}</span>
                                        <van-field v-if="item.value == option.value && option.input_status == 1"
                                            v-model="option.input_text" :placeholder="'请输入'"></van-field>
                                    </div>
                                </van-radio>
                            </van-radio-group>

                        </template>
                        <!-- 多选 -->
                        <template v-if="item.type == 102">
                            <van-checkbox-group v-model="item.value">
                                <van-checkbox shape="square" :name="option.value" @click="jump(item, option)"
                                    v-for="(option, option_index) in item.option" :key="option_index">
                                    <div style="display: flex; align-items: center;">
                                        <span style="min-width: 50px;"> {{ option.title }}</span>
                                        <van-field @click.stop=""
                                            v-if="item.value.indexOf(option.value) != -1 && option.input_status == 1"
                                            v-model="option.input_text" :placeholder="'请输入'"></van-field>
                                    </div>
                                </van-checkbox>
                            </van-checkbox-group>
                        </template>
                        <template v-if="item.type == 201 || item.type == 301 || item.type == 302">
                            <van-field v-model="item.value" :formatter="item.type == 302 ? formatter : ''"
                                :maxlength="item.max" :placeholder="item.placeholder"
                                :type="item.type == 302 ? 'tel' : 'text'"></van-field>
                        </template>
                        <template v-if="item.type == 202">
                            <van-field v-model="item.value" type="textarea" :maxlength="item.max" show-word-limit
                                rows="3" autosize :placeholder="item.placeholder"></van-field>
                        </template>
                        <template v-if="item.type == 304">
                            <van-field readonly v-model="item.value" @click="showPop(item)"
                                :placeholder="'请选择日期'"></van-field>
                        </template>
                        <template v-if="item.type == 305">
                            <van-field readonly v-model="item.province" @click="cityLevel = 1;showPop(item)"
                                :placeholder="'请选择省份'"></van-field>
                            <van-field v-if="item.level > 1" readonly v-model="item.city"
                                @click="cityLevel = 2;showPop(item)" :placeholder="'请选择城市'"></van-field>
                            <van-field v-if="item.level > 2" readonly v-model="item.area"
                                @click="cityLevel = 3;showPop(item)" :placeholder="'请选择区县'"></van-field>
                            <van-field v-if="item.level > 3" readonly v-model="item.street"
                                @click="cityLevel = 4;showPop(item)" :placeholder="'请选择街道'"></van-field>
                            <van-field v-if="item.level > 4" v-model="item.address" type="textarea"
                                :maxlength="item.max" show-word-limit rows="3" autosize
                                :placeholder="'请输入详细地址'"></van-field>
                        </template>
                        <template v-if="item.type == 306">
                            <canvas id="signature-pad" style="border:1px solid #000000;"></canvas>
                            <van-button style="margin: 20px 24px 0 0;" @click="signaturePad.clear()">清除签名</van-button>
                        </template>
                        <template v-if="item.type == 401">
                            <van-uploader :after-read="afterRead" v-model="item.fileList" multiple
                                :max-count="item.max"></van-uploader>
                        </template>
                        <template v-if="item.type == 402">
                            <van-rate v-model="item.value"></van-rate>
                            <van-field v-if="item.is_required_text" v-model="item.text" type="textarea"
                                :maxlength="item.max" show-word-limit rows="3" autosize
                                :placeholder="item.placeholder"></van-field>
                        </template>
                        <van-divider></van-divider>
                    </div>
                </van-form>
                <van-popup v-model:show="popupShow" teleport="body" :safe-area-inset-bottom="true" position="bottom">
                    <van-date-picker v-if="activeItem.type == 304" v-model="currentDate" title="选择日期"
                        @confirm="dateConfirm" @cancel="popupCancel"></van-date-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 1"
                        title="请选择省份" :columns="province_list" @confirm="cityConfirm"
                        @cancel="popupCancel"></van-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 2"
                        title="请选择城市" :columns="city_list" @confirm="cityConfirm" @cancel="popupCancel"></van-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 3"
                        title="请选择区县" :columns="area_list" @confirm="cityConfirm" @cancel="popupCancel"></van-picker>
                    <van-picker :columns-field-names="{value:'code'}" v-show="activeItem.type == 305 && cityLevel == 4"
                        title="请选择街道" :columns="street_list" @confirm="cityConfirm" @cancel="popupCancel"></van-picker>
                </van-popup>
            </div>
        </div>
    </div>
</body>

</html>
<script src="./libs/obs/esdk-obs-browserjs.3.22.3.min.js"></script>
<script src="./libs/signature_pad.umd.min.js"></script>
<script src="./libs/areaList.js"></script>
<script src="./libs/obs.js"></script>

<script>
    const { createApp, ref, reactive, computed, onMounted } = Vue;
    const { Area, Button, RadioGroup, Radio, Input, Popup, Toast, Cascader, Checkbox, CheckboxGroup, DatePicker, Form, Field, CellGroup, Picker, Uploader, Dialog, Loading, ShareSheet, Divider, ImagePreview, Rate, showSuccessToast, showFailToast, showLoadingToast, showToast, closeToast, showDialog } = vant;

    var searchParams = new URLSearchParams(window.location.search);
    let baseUrl = location.href.indexOf('192.168') != -1 ? 'https://service.tuoyupt.com' : location.origin;

    const app = createApp({
        setup() {
            const id = ref('');
            const preview = ref(false);
            const mainData = ref('');
            const agree = ref(false);
            const page = ref(false);
            const jumpIndex = ref({});
            const popupShow = ref(false);
            const shareShow = ref(false);
            const areaList = ref(windowAreaList)
            const shareOptions = ref([
                { name: '微信', icon: 'wechat' },
                { name: '朋友圈', icon: 'wechat-moments' },
                { name: '复制链接', icon: 'font-o' },
            ],)
            const city_list = ref([]);
            const area_list = ref([]);
            const street_list = ref([]);
            const province_list = ref([]);

            const user = reactive({
                name: '',
                phone: '',
                code: '37160',
                school_name: '',
                disabled: false
            })

            let loading = showLoadingToast('加载中...');

            let start_time = '';
            let isMini = ref(false);
            onMounted(async () => {
                id.value = searchParams.get('id');
                if (id.value) {
                    let response = await fetch(`${baseUrl}/api/jyform/detail?id=${id.value}`, {
                        method: 'GET'
                    })
                    if (response.status == 200) {
                        document.body.style.opacity = 1;
                        let data = await response.json();
                        if (data.code == 200) {
                            loading.close();
                            mainData.value = data.data;
                            setTimeout(() => {
                                signInit();
                            }, 300);
                        } else {
                            showFailToast(data.message);
                        }
                    }
                }
            });

            const cityLevel = ref(1);
            async function getProvince(code, type) {
                let response = await fetch(`${baseUrl}/api/auth/areastreet?code=${code}&type=${type}`, {
                    method: 'GET'
                })
                if (response.status == 200) {
                    let data = await response.json();
                    if (data.code == 200) {
                        return data.data
                    } else {
                        showFailToast(data.message);
                    }
                }
            }

            function jump(item, option) {
                // console.log(item, option);
                let target = item.jump_type == 1 ? option.jump_to : item.jump_to;
                console.log(target);
                if (target) {
                    let arr = [];
                    let start = 0;
                    for (let index = 0; index < mainData.value.form.length; index++) {
                        let el = mainData.value.form[index];
                        if (el.index == item.index) {
                            start = index;
                            break;
                        };

                    }

                    for (let index = start; index < mainData.value.form.length; index++) {
                        let el = mainData.value.form[index];
                        if (index > start) {
                            arr.push(el.index);
                            jumpIndex.value[el.index] = []
                        }
                        if (el.index == target) {
                            break;
                        };
                    }

                    console.log(start);
                    arr.pop();
                    jumpIndex.value[item.index] = arr;
                } else {
                    jumpIndex.value[item.index] = []
                }
                console.log(jumpIndex.value);
            }

            function jumpThis(index) {
                let jump = false;
                for (const key in jumpIndex.value) {
                    if (Object.hasOwnProperty.call(jumpIndex.value, key)) {
                        const element = jumpIndex.value[key];
                        if (element.length && element.indexOf(index) != -1) {
                            jump = true;
                        }
                    }
                }
                return jump;
            }

            const formatter = (value) => value.replace(/\D/g, '');

            const activeItem = ref('');
            let currentDate = ref('');

            function dateConfirm(value) {
                activeItem.value.value = value.selectedValues.join('-');
                popupShow.value = false
            }

            async function cityConfirm(value) {
                if (activeItem.value.value == '') {
                    activeItem.value.value = ['', '', '', ''];
                }
                if (cityLevel.value == 1) {
                    activeItem.value.province = value.selectedOptions[0].text;
                    activeItem.value.value[0] = value.selectedOptions[0].code;
                    activeItem.value.city = '';
                    activeItem.value.area = '';
                    activeItem.value.street = '';
                    city_list.value = await getProvince(value.selectedOptions[0].code, cityLevel.value + 1)
                } else if (cityLevel.value == 2) {
                    activeItem.value.city = value.selectedOptions[0].text;
                    activeItem.value.value[1] = value.selectedOptions[0].code;
                    activeItem.value.area = '';
                    activeItem.value.street = '';
                    area_list.value = await getProvince(value.selectedOptions[0].code, cityLevel.value + 1)
                    console.log(area_list.value);
                } else if (cityLevel.value == 3) {
                    activeItem.value.area = value.selectedOptions[0].text;
                    activeItem.value.value[2] = value.selectedOptions[0].code;
                    activeItem.value.street = '';
                    street_list.value = await getProvince(value.selectedOptions[0].code, cityLevel.value + 1)
                    console.log(street_list.value);
                } else if (cityLevel.value == 4) {
                    activeItem.value.street = value.selectedOptions[0].text;
                    activeItem.value.value[3] = value.selectedOptions[0].code;
                }
                popupShow.value = false
            }

            function popupCancel() {
                popupShow.value = false
            }

            function areaCancel() {
                popupShow.value = false
            }

            async function showPop(item) {
                if (item.type == 304) {
                    if (item.type == 304) {
                        if (item.value == '') {
                            let date = new Date();
                            currentDate.value = [date.getFullYear(), date.getMonth() + 1, date.getDate()]
                        } else {
                            currentDate.value = item.value.split('-');
                        }
                    }
                }
                if (item.type == 305 && province_list.value.length == 0) {
                    province_list.value = await getProvince(0, 1);
                }
                if (item.type == 305 && cityLevel.value == 2 && item.province == '') {
                    showToast('请先选择省份')
                    return
                }
                if (item.type == 305 && cityLevel.value == 3 && item.city == '') {
                    showToast('请先选择城市')
                    return
                }
                if (item.type == 305 && cityLevel.value == 4 && item.area == '') {
                    showToast('请先选择区县')
                    return
                }


                activeItem.value = item;
                popupShow.value = true;
            }


            const currentArea = ref('');

            async function afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                try {
                    let url = await windowUpload(file.file, `wenjuan`);
                    if (url) {
                        file.url = url;
                        file.status = 'success';
                        file.message = '';
                    }
                } catch (error) {
                    file.status = 'failed';
                    file.message = '上传失败';
                }
            }

            function showShareTip() {
                showDialog({
                    message: '请点击右上角分享给好友或朋友圈',
                }).then(() => {
                    // on close
                });
            }

            const signaturePad = ref('');
            function signInit() {
                var canvas = document.getElementById('signature-pad');
                if (canvas) {
                    signaturePad.value = new SignaturePad(canvas);
                }
                // signaturePad.toDataURL();
                // signaturePad.clear();
                // signaturePad.isEmpty();
            }

            return {
                mainData,
                agree,
                page,
                jump,
                jumpIndex,
                jumpThis,
                formatter,
                popupShow,
                showPop,
                dateConfirm,
                cityConfirm,
                popupCancel,
                areaCancel,
                currentDate,
                activeItem,
                areaList,
                currentArea,
                afterRead,
                user,
                signaturePad,
                showShareTip,
                province_list,
                cityLevel,
                city_list,
                area_list,
                street_list
            }
        }
    })
    app.use(Button);
    app.use(RadioGroup);
    app.use(Radio);
    app.use(Input);
    app.use(Popup);
    app.use(Toast);
    app.use(Cascader);
    app.use(Checkbox);
    app.use(CheckboxGroup);
    app.use(DatePicker);
    app.use(Form);
    app.use(Field);
    app.use(CellGroup);
    app.use(Picker);
    app.use(Uploader);
    app.use(Dialog);
    app.use(Loading);
    app.use(ShareSheet);
    app.use(Divider);
    app.use(ImagePreview);
    app.use(Rate);
    app.use(Area);
    app.mount('#app');
</script>
