<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="./lib/WSPlayer/player.css">
  <link rel="stylesheet" href="./lib/WSPlayer/window.division.css">
  <title>视频监控</title>
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    #dh-real-player {
      opacity: 0;
      height: 100vh;
      width: 100vw;
    }

    #zw-real-player {
      opacity: 0;
      height: 100vh;
      width: 100vw;
    }
  </style>
</head>

<body>
  <div id="dh-real-player"></div>
  <video id="zw-real-player" controls></video>
</body>

<script type="text/javascript" src="./lib/WSPlayer/PlaySDKInterface.js"></script>
<script type="text/javascript" src="./lib/WSPlayer/WSPlayer.js"></script>
<script type="text/javascript" src="./icc/api.js"></script>
<script type="text/javascript" src="./icc/PlayerManager.js"></script>
<script type="text/javascript" src="./dhhls.min.js"></script>
<script src="./vconsole.min.js"></script>

<script type="module">
  // const vConsole = new VConsole();

  try {
    function getParams(url) {
      let arr = url.split('index.html?'); 
      return arr[1];
    }

    let player = {
      realPlayer: null, // 实时预览播放器
      recordPlayer: null // 录像回放播放器
    }

    let playerType = 'realPlayer' // 播放器类型 real-实时预览播放器 record-录像回放播放器
    initPlayer("dh-real-player");

    // 初始化播放器
    // 注意实时预览和录像回放必须要用不同的id，不能复用同一个
    function initPlayer(el) {
      player[playerType] = new PlayerManager({
        el: el, /** 实时预览容器id，创建多个播放器，传入不同的容器id即可 **/
        type: playerType.replace('Player', ''), /** real - 实时预览  record - 录像回放 **/
        maxNum: 25,  /** 一个播放器上限能播放的路数，可根据实际情况设置，支持 1 4 9 16 25 **/
        num: 1,   /** 初始化，页面显示的路数 **/
        prefixUrl: './lib', /** 解码库所在位置的前缀 **/
        isWebView: false,
        maxNum: 1,  /** 一个播放器上限能播放的路数，可根据实际情况设置，支持 1 4 9 16 25 **/
        num: 1,   /** 初始化，页面显示的路数 **/
        showControl: true, /** 是否显示工具栏，默认显示 **/
        showIcons: { // 自定义按钮，需要的请配置true, 不需要的按钮请配置false，所有的按钮属性都要写上
          streamChangeSelect: true, // 主辅码流切换
          talkIcon: true, // 对讲功能按钮
          localRecordIcon: true, // 录制视频功能按钮
          audioIcon: true, // 开启关闭声音按钮
          snapshotIcon: true, // 抓图按钮
          closeIcon: true, // 关闭视频按钮
        },
        openIvs: true, // true-开启智能帧/规则线/目标框, false-不显示
        showRecordProgressBar: true, // 录像回放时，录像的进度条是否需要
        useH265MSE: true, // true-表示硬解  false-软解 默认不传该字段
        receiveMessageFromWSPlayer: (methods, data, err) => { /* 回调函数，可以在以下回调函数里面做监听 */
          switch (methods) {
            // 一下是播放器的通用回调
            case 'initializationCompleted':
              console.log('初始化完成')
              // 初始化完成，可调用播放方法（适用于动态加载解码库）
              // 若回调未触发时就使用实时预览/录像回放，则无法播放。
              // 此时我们可以调用一个
              break;
            case 'selectWindowChanged': // 选中的窗口发生改变
              console.log(data, "改变选中的窗口")
              break;
            case 'windowNumChanged': // 播放器显示的路数发生改变
              console.log(data, "返回显示的窗口数量")
              break;
            case 'getVideoFrameInfo':
              // console.log("此处接收视频帧信息");
              break;
            case 'getAudioFrameInfo':
              // console.log("此处接收音频帧信息");
              break;
            case 'closeVideo': // 视频关闭回调
              // 点击关闭按钮引发的视频关闭进行提示
              // 切换视频引发的视频关闭不进行提示
              break;
            case 'statusChanged': // 视频状态发生改变
              break;
            case 'errorInfo': // 错误信息汇总
              console.log(data, "可打印查看错误消息");
              break;
            // 一下是实时预览扩充的回调
            case "changeStreamType": // 主/辅码流切换回调
              console.log("主/辅码流切换回调")
              break;
            case "realSuccess": // 实时预览成功
              console.log("实时预览成功")
              break;
            case "realError": // 实时预览失败
              console.log("实时预览失败", err)
              break;
            case "talkError": // 对讲失败
              console.log("对讲失败");
              break;
            // 以下是录像回放扩充的回调
            case 'recordSuccess': // 录像回放成功
              console.log("录像回放成功")
              break;
            case 'recordError': // 录像回放失败
              console.log("录像回放失败", err)
              break;
            case 'recordFinish': // 录像回放完成
              console.log("录像回放完成")
              break;
            default:
              break;
          }
        }
      })
    }


    setTimeout(async () => {
      const params = getParams(location.href) || 'school_id=2&class_id=10&channel_id=1000008$1$0$7';
      if (params) {
        console.log(params);
        let res = await fetch('/api/jkaccout/class/jkDetail?' + params).then(function (response) {
          //response.status表示响应的http状态码
          if (response.status === 200) {
            //json是返回的response提供的一个方法,会把返回的json字符串反序列化成对象,也被包装成一个Promise了
            return response.json();
          } else {
            return {}
          }
        });
        if (res.code == 200) {
          document.getElementById("zw-real-player").style.opacity = 1;
          document.getElementById("dh-real-player").style.opacity = 1;
          if (res.data[0].type == 4) {
            document.getElementById("zw-real-player").remove();

            player[playerType].realByUrl({
              rtspURL: res.data[0].rtsp, // 必传
              wsURL: res.data[0].wss, // 必传
              selectIndex: 0, // 播放窗口号
              channelId: res.data[0].port, // 必传, 通道id
              streamType: 1, // 必传, 码流类型，
              playerAdapter: 'selfAdaption', // 选传, selfAdaption 自适应 | stretching 拉伸
              channelData: {  // 建议传, 通道树信息
                // id: channelCode, // {String} 通道编码
                // deviceCode: deviceCode, // {String} 设备编码
                // deviceType: deviceType, // {String} 设备类型
                // channelSeq: channelSeq, // {String|Number} 通道序号
              },
            })
          } else {
            document.getElementById("dh-real-player").remove();
            let hls = new Hls();
            let videoUrl = res.data[0].url_list.playUrl;

            const videoElement = document.getElementById("zw-real-player");

            if (Hls.isSupported()) {
              console.log(Hls.isSupported());
              hls.attachMedia(videoElement);
              hls.on(Hls.Events.MEDIA_ATTACHED, function () {
                console.log("video and hls.js 已经绑定完成");
                hls.loadSource(videoUrl);
                hls.on(Hls.Events.MANIFEST_PARSED, function () {
                  console.log("m3u8文件解析完成");
                  videoElement.play();
                })
              });
            } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
              console.log("apple原生");
              // 如果支持原生播放
              videoElement.src = url;
              videoElement.addEventListenter('canplay', function () {
                //播放视频
                videoElement.play();
              })
            }
          }
        }
      }
    }, 300);
  } catch (error) {
    console.log(error);
  }
</script>

</html>