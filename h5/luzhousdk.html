<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录中...</title>
    <style>
        * {
            word-break: break-all;
        }
    </style>
    <script src="https://jcet.lzjczl.com/jmopenpub/jmopen_files/js/jssdk/index.js"></script>
    <script src="https://jcet.lzjczl.com/jmopenpub/jmopen_files/js/index.js"></script>
    <script>
        if (localStorage.get("token")) {
            location.href = '/parents/index.html'
        }
        if (lightAppJssdk) {
            getLZtoken();
        }

        function getLZtoken() {
            lightAppJssdk.user.getTicket({
                success: async function (data) {
                    if (data == '未登录') {
                        lightAppJssdk.user.loginapp({
                            success: function (data) {
                                getLZtoken();
                            },
                            fail: function (data) {
                                lightAppJssdk.notification.alert({
                                    message: "登录失败",
                                    title: "提示",//可传空
                                    buttonName: "确认",
                                    success: function (data) {
                                        /*回调*/
                                    },
                                    fail: function (data) {
                                        //错误返回
                                    }
                                });
                            }
                        });
                    } else {
                        let res = JSON.parse(data);
                        if (res.data.access_token) {
                            let response = await fetch(`/api/auth/jiuLogin`, {
                                method: 'POST',
                                data: {
                                    access_token: res.data.access_token
                                }
                            })
                            if (response.status == 200) {
                                let data = await response.json();
                                if (data.code == 200) {
                                    if (res.token) {
                                        uni.setStorageSync("token", res.token);
                                        uni.setStorageSync("user_info", res.user);
                                        uni.setStorageSync("jiulogin", 1);
                                        document.getElementById('alert').innerHTML = '登录成功'
                                        setTimeout(() => {
                                            location.href = '/parents/index.html'
                                        }, 500)
                                    }
                                } else {
                                    showFailToast();
                                    lightAppJssdk.notification.alert({
                                        message: data.message,
                                        title: "提示",//可传空
                                        buttonName: "确认",
                                        success: function (data) {
                                            /*回调*/
                                        },
                                        fail: function (data) {
                                            //错误返回
                                        }
                                    });
                                }
                            }
                        }
                    }
                },
                fail: function (data) {
                    alert(data);
                }
            });
        }
    </script>
</head>

<body style="padding: 0; margin: 0;">
    <div style="height: 100vh; width: 100vw; background: #f4f4f4; display: flex;
    align-items: center; justify-content: center;">
        <div style="padding: 24px 48px;background: rgba(0, 0, 0, 0.8);color: #fff;border-radius: 12px;">登录中...</div>
    </div>
</body>

</html>