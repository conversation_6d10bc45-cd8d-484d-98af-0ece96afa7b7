input1.value = arr2[index].address;
btn.click();
setTimeout(()=>{
    let lnglat = input2.value.split(',');
    let newLnglat = gaodeToBaidu(lnglat[0], lnglat[1]);
        schoolList.push({
            name: arr2[index].name,
            blng: input2.value.split(',')[0],
            blat: input2.value.split(',')[1],
            lng: newLnglat[0],
            lat: newLnglat[1],
        });
    index++;
    console.log(index);
    },500)

    const gaodeToBaidu = (lng, lat) => {
    const x_PI = (Math.PI * 3000.0) / 180.0;
    let z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
    let theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
    let bd_lng = z * Math.cos(theta) + 0.0065;
    let bd_lat = z * Math.sin(theta) + 0.006;
    return [bd_lng, bd_lat];
}