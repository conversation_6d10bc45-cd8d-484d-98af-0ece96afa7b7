!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.PlaySDKInterface=t():e.PlaySDKInterface=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}if(n.r(t),void 0===o)var o={};o.MD5=function(e){function t(e){var t=(e>>>0).toString(16);return"00000000".substr(0,8-t.length)+t}function n(e,t,n){return e&t|~e&n}function o(e,t,n){return n&e|~n&t}function a(e,t,n){return e^t^n}function i(e,t,n){return t^(e|~n)}function l(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]}function u(e){for(var t=[],n=0;n<e.length;n++)if(e.charCodeAt(n)<=127)t.push(e.charCodeAt(n));else for(var r=encodeURIComponent(e.charAt(n)).substr(1).split("%"),o=0;o<r.length;o++)t.push(parseInt(r[o],16));return t}function s(e){for(var t=new Array(e.length),n=0;n<e.length;n++)t[n]=e[n];return t}var c=null,f=null;function p(e,t){return 4294967295&e+t}return"string"==typeof e?c=u(e):e.constructor==Array?0===e.length?c=e:"string"==typeof e[0]?c=function(e){for(var t=[],n=0;n<e.length;n++)t=t.concat(u(e[n]));return t}(e):"number"==typeof e[0]?c=e:f=r(e[0]):"undefined"!=typeof ArrayBuffer?e instanceof ArrayBuffer?c=s(new Uint8Array(e)):e instanceof Uint8Array||e instanceof Int8Array?c=s(e):e instanceof Uint32Array||e instanceof Int32Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Float32Array||e instanceof Float64Array?c=s(new Uint8Array(e.buffer)):f=r(e):f=r(e),f&&alert("MD5 type mismatch, cannot process "+f),function(){function e(e,t,n,r){var o,a,i=v;v=y,y=S,S=p(S,(o=p(h,p(e,p(t,n))))<<(a=r)&4294967295|o>>>32-a),h=i}var r=c.length;c.push(128);var u=c.length%64;if(u>56){for(var s=0;s<64-u;s++)c.push(0);u=c.length%64}for(s=0;s<56-u;s++)c.push(0);c=c.concat(function(e){for(var t=[],n=0;n<8;n++)t.push(255&e),e>>>=8;return t}(8*r));var f=1732584193,d=4023233417,m=2562383102,g=271733878,h=0,S=0,y=0,v=0;for(s=0;s<c.length/64;s++){h=f;var _=64*s;e(n(S=d,y=m,v=g),3614090360,l(c,_),7),e(n(S,y,v),3905402710,l(c,_+4),12),e(n(S,y,v),606105819,l(c,_+8),17),e(n(S,y,v),3250441966,l(c,_+12),22),e(n(S,y,v),4118548399,l(c,_+16),7),e(n(S,y,v),1200080426,l(c,_+20),12),e(n(S,y,v),2821735955,l(c,_+24),17),e(n(S,y,v),4249261313,l(c,_+28),22),e(n(S,y,v),1770035416,l(c,_+32),7),e(n(S,y,v),2336552879,l(c,_+36),12),e(n(S,y,v),4294925233,l(c,_+40),17),e(n(S,y,v),2304563134,l(c,_+44),22),e(n(S,y,v),1804603682,l(c,_+48),7),e(n(S,y,v),4254626195,l(c,_+52),12),e(n(S,y,v),2792965006,l(c,_+56),17),e(n(S,y,v),1236535329,l(c,_+60),22),e(o(S,y,v),4129170786,l(c,_+4),5),e(o(S,y,v),3225465664,l(c,_+24),9),e(o(S,y,v),643717713,l(c,_+44),14),e(o(S,y,v),3921069994,l(c,_),20),e(o(S,y,v),3593408605,l(c,_+20),5),e(o(S,y,v),38016083,l(c,_+40),9),e(o(S,y,v),3634488961,l(c,_+60),14),e(o(S,y,v),3889429448,l(c,_+16),20),e(o(S,y,v),568446438,l(c,_+36),5),e(o(S,y,v),3275163606,l(c,_+56),9),e(o(S,y,v),4107603335,l(c,_+12),14),e(o(S,y,v),1163531501,l(c,_+32),20),e(o(S,y,v),2850285829,l(c,_+52),5),e(o(S,y,v),4243563512,l(c,_+8),9),e(o(S,y,v),1735328473,l(c,_+28),14),e(o(S,y,v),2368359562,l(c,_+48),20),e(a(S,y,v),4294588738,l(c,_+20),4),e(a(S,y,v),2272392833,l(c,_+32),11),e(a(S,y,v),1839030562,l(c,_+44),16),e(a(S,y,v),4259657740,l(c,_+56),23),e(a(S,y,v),2763975236,l(c,_+4),4),e(a(S,y,v),1272893353,l(c,_+16),11),e(a(S,y,v),4139469664,l(c,_+28),16),e(a(S,y,v),3200236656,l(c,_+40),23),e(a(S,y,v),681279174,l(c,_+52),4),e(a(S,y,v),3936430074,l(c,_),11),e(a(S,y,v),3572445317,l(c,_+12),16),e(a(S,y,v),76029189,l(c,_+24),23),e(a(S,y,v),3654602809,l(c,_+36),4),e(a(S,y,v),3873151461,l(c,_+48),11),e(a(S,y,v),530742520,l(c,_+60),16),e(a(S,y,v),3299628645,l(c,_+8),23),e(i(S,y,v),4096336452,l(c,_),6),e(i(S,y,v),1126891415,l(c,_+28),10),e(i(S,y,v),2878612391,l(c,_+56),15),e(i(S,y,v),4237533241,l(c,_+20),21),e(i(S,y,v),1700485571,l(c,_+48),6),e(i(S,y,v),2399980690,l(c,_+12),10),e(i(S,y,v),4293915773,l(c,_+40),15),e(i(S,y,v),2240044497,l(c,_+4),21),e(i(S,y,v),1873313359,l(c,_+32),6),e(i(S,y,v),4264355552,l(c,_+60),10),e(i(S,y,v),2734768916,l(c,_+24),15),e(i(S,y,v),1309151649,l(c,_+52),21),e(i(S,y,v),4149444226,l(c,_+16),6),e(i(S,y,v),3174756917,l(c,_+44),10),e(i(S,y,v),718787259,l(c,_+8),15),e(i(S,y,v),3951481745,l(c,_+36),21),f=p(f,h),d=p(d,S),m=p(m,y),g=p(g,v)}return function(e,n,r,o){for(var a="",i=0,l=0,u=3;u>=0;u--)i=255&(l=arguments[u]),i<<=8,i|=255&(l>>>=8),i<<=8,i|=255&(l>>>=8),i<<=8,a+=t(i|=l>>>=8);return a}(g,m,d,f).toUpperCase()}()};var a=function(e){return o.MD5(e)},i=0;function l(e){return s(u(c(e)))}function u(e){return p(d(f(e),8*e.length))}function s(e){for(var t,n=i?"0123456789ABCDEF":"0123456789abcdef",r="",o=0;o<e.length;o++)t=e.charCodeAt(o),r+=n.charAt(t>>>4&15)+n.charAt(15&t);return r}function c(e){for(var t,n,r="",o=-1;++o<e.length;)t=e.charCodeAt(o),n=o+1<e.length?e.charCodeAt(o+1):0,55296<=t&&t<=56319&&56320<=n&&n<=57343&&(t=65536+((1023&t)<<10)+(1023&n),o++),t<=127?r+=String.fromCharCode(t):t<=2047?r+=String.fromCharCode(192|t>>>6&31,128|63&t):t<=65535?r+=String.fromCharCode(224|t>>>12&15,128|t>>>6&63,128|63&t):t<=2097151&&(r+=String.fromCharCode(240|t>>>18&7,128|t>>>12&63,128|t>>>6&63,128|63&t));return r}function f(e){for(var t=Array(e.length>>2),n=0;n<t.length;n++)t[n]=0;for(n=0;n<8*e.length;n+=8)t[n>>5]|=(255&e.charCodeAt(n/8))<<24-n%32;return t}function p(e){for(var t="",n=0;n<32*e.length;n+=8)t+=String.fromCharCode(e[n>>5]>>>24-n%32&255);return t}function d(e,t){e[t>>5]|=128<<24-t%32,e[15+(t+64>>9<<4)]=t;for(var n=Array(80),r=1732584193,o=-271733879,a=-1732584194,i=271733878,l=-1009589776,u=0;u<e.length;u+=16){for(var s=r,c=o,f=a,p=i,d=l,y=0;y<80;y++){n[y]=y<16?e[u+y]:S(n[y-3]^n[y-8]^n[y-14]^n[y-16],1);var v=h(h(S(r,5),m(y,o,a,i)),h(h(l,n[y]),g(y)));l=i,i=a,a=S(o,30),o=r,r=v}r=h(r,s),o=h(o,c),a=h(a,f),i=h(i,p),l=h(l,d)}return Array(r,o,a,i,l)}function m(e,t,n,r){return e<20?t&n|~t&r:e<40?t^n^r:e<60?t&n|t&r|n&r:t^n^r}function g(e){return e<20?1518500249:e<40?1859775393:e<60?-1894007588:-899497514}function h(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function S(e,t){return e<<t|e>>>32-t}var y,v=l,_=function(e){e=e;var t=!1,n="",r="",o="",i=null,l=null,u=null,s=0,c=0,f=!1,p=!1,d=0,m=!1,g="",h={videoSSRC:0,audio1SSRC:0,audio2SSRC:0},S=!1,y=200,_=401,P=404,E=457,A=503,b="",w='Authorization: WSSE profile="UsernameToken"',C=[],T=1,M={},D="",R=null,I=null,U=null,k={},H={},L=null,O=null,F=null,B=null,G=null,x="play",V=null,N={},W=[],z=!1,Y=!1,q=0,j=0,K=3,X=0,J=8,Z=!1;function Q(){}function $(e,t){var n=e.split("#")[0];if(n){var r=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),o=n.match(r);if(null!==o)return unescape(o[2])}return null}function ee(i,l,u,s){return t?function(t,n,o,a){var i="";switch(t){case"OPTIONS":case"TEARDOWN":case"GET_PARAMETER":case"SET_PARAMETERS":i=t+" "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\n"+b+"\r\n";break;case"DESCRIBE":if(i=t+" "+r+" RTSP/1.0\r\nCSeq: "+T,!0===p&&-1===r.indexOf("subtype=5")){i+="\r\nRequire: www.onvif.org/ver20/backchannel"}i=i+"\r\n"+b+"\r\n",le(),ue();break;case"SETUP":e.log("trackID: "+n),i=t+" "+r+"/trackID="+n+" RTSP/1.0\r\nCSeq: "+T+"\r\n"+b+"Transport: DH/AVP/TCP;unicast;interleaved="+2*n+"-"+(2*n+1)+"\r\n",i+=0!=I?"Session: "+I+"\r\n\r\n":"\r\n",le(),ue();break;case"PLAY":i=t+" "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\nSession: "+I+";timeout=60\r\n",null!=a&&a>=0?(i+="Range: npt="+a+"-\r\n",i+=b+"\r\n"):i+=b+"\r\n",le(),ue(),Z||(X=setTimeout((function(){X&&(clearTimeout(X),X=0),q&&(clearTimeout(q),q=0),j&&(clearTimeout(j),j=0),B({errorCode:"409",description:"Rtsp Not Response"})}),1e3*J));break;case"PAUSE":i=t+" "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\nSession: "+I+"\r\n\r\n";break;case"SCALE":i="PLAY "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\nSession: "+I+"\r\n",i+=a<8?"Speed: "+a+"\r\n":"Scale: "+a+"\r\n",i+=b+"\r\n"}return i}(i,l,0,s):function(e,t,i,l){var u="",s="",c=a(H.username+"::"+H.password),f=n.split("://")[1].split("/")[0].split(":")[0]+":8086",g=!1;if(m){var h=$(o,"beginTime"),S=$(o,"endTime"),y=h?h.replace(/-|:|\s/g,"_"):"",_=S?S.replace(/-|:|\s/g,"_"):"";0!=y.length&&0!=_.length&&(g=!0)}var P=o&&o.replace(/&beginTime=\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}&endTime=\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/,"");switch(e){case"PLAY":var E=function(){for(var e=[],t=0;t<32;t++)e.push(String.fromCharCode(256*Math.random()|0));return a(e.join(""))}(),A=(M=new Date,R=M.getTimezoneOffset(),I=M.getTime()+60*R*1e3,U=new Date(I),k=U.getFullYear(),L=U.getMonth()+1,O=U.getDate(),F=U.getHours(),B=U.getMinutes(),G=U.getSeconds(),k+"-"+(x=function(e){return e<10?"0"+e:e})(L)+"-"+x(O)+"T"+x(F)+":"+x(B)+":"+x(G)+"Z"),C=function(e){var t,n,r,o,a,i,l,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s="",c=0;for(;c<e.length;)t=e[c++],n=e[c++],r=e[c++],o=t>>2,a=(3&t)<<4|n>>4,i=(15&n)<<2|r>>6,l=63&r,isNaN(n)?i=l=64:isNaN(r)&&(l=64),s=s+u.charAt(o)+u.charAt(a)+u.charAt(i)+u.charAt(l);return s}(function(e){var t,n=e.trim(),r="0x"===n.substr(0,2).toLowerCase()?n.substr(2):n,o=r.length;if(o%2!=0)return"";for(var a=[],i=0;i<o;i+=2)t=parseInt(r.substr(i,2),16),a.push(t);return a}(v(E+A+c.toUpperCase())));u="GET "+r+(g?"&starttime=".concat(y,"&endtime=").concat(_,"&"):"&")+"method=0"+(P.length>0?"?sourceId=".concat(P):"")+" HTTP/1.1\r\n",u+="Accept-Sdp: Private\r\n",u+=b+"\r\n",u+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+f+"\r\n",l?u+="Range: npt="+l+"-\r\n":d?u+="Range: npt="+d+"-\r\n":"ContinuePlay"!==D&&(u+="Range: npt=0.000000-\r\n"),p&&(s+="v=0\r\n",s+="o=- 2208989105 2208989105 IN IP4 0.0.0.0\r\n",s+="s=Media Server\r\n",s+="c=IN IP4 0.0.0.0\r\n",s+="t=0 0\r\n",s+="a=control:*\r\n",s+="a=packetization-supported:DH\r\n",s+="a=rtppayload-supported:DH\r\n",s+="a=range:npt=now-\r\n",s+="m=audio 0 RTP/AVP 8\r\n",s+="a=control:trackID=5\r\n",s+="a=rtpmap:8 PCMA/16000\r\n",u+="Private-Length: "+(s+="a=sendonly\r\n").length+"\r\n",u+="Private-Type: application/sdp\r\n"),u+=b===w?'WSSE: UsernameToken Username="'+H.username+'", PasswordDigest="'+C+'", Nonce="'+E+'", Created="'+A+'"\r\n\r\n':"\r\n",u+=s;break;case"PAUSE":u="GET "+r+(g?"&starttime=".concat(y,"&endtime=").concat(_,"&"):"&")+"method=1"+(P.length>0?"?sourceId=".concat(P):"")+" HTTP/1.1\r\n",u+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+f+"\r\n\r\n";break;case"KEEP_LIVE":u="GET "+r+(g?"&starttime=".concat(y,"&endtime=").concat(_,"&"):"&")+"method=2"+(P.length>0?"?sourceId=".concat(P):"")+" HTTP/1.1\r\n",u+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+f+"\r\n\r\n";break;case"STOP":case"TEARDOWN":u="GET "+r+(g?"&starttime=".concat(y,"&endtime=").concat(_,"&"):"&")+"method=3?sourceId="+P+" HTTP/1.1\r\n",u+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+f+"\r\n\r\n";break;case"SCALE":u="GET "+r+(g?"&starttime=".concat(y,"&endtime=").concat(_,"&"):"&")+"method=0"+(P.length>0?"?sourceId=".concat(P):"")+" HTTP/1.1\r\n",u+=l<8?"Speed: "+l+"\r\n":"Scale: "+l+"\r\n",u+="Connection: keep-alive\r\nCseq: "+T+"\r\nHost: "+f+"\r\n\r\n"}var M,R,I,U,k,L,O,F,B,G,x;return u}(i,0,0,s)}function te(n){e.log(n);var o,l=n.search("CSeq: ")+5;if(T=parseInt(n.slice(l,l+10))+1,(o=function(e){var t={},n=0,r=0,o=null,a=null;if(-1!==e.search("Content-Type: application/sdp")){var i=e.split("\r\n\r\n");a=i[0]}else a=e;var l=a.split("\r\n"),u=l[0].split(" ");u.length>2&&(t.ResponseCode=parseInt(u[1]),t.ResponseMessage=u[2]);if(t.ResponseCode===y){for(n=1;n<l.length;n++)if("Public"===(o=l[n].split(":"))[0])t.MethodsSupported=o[1].split(",");else if("CSeq"===o[0])t.CSeq=parseInt(o[1]);else if("Content-Type"===o[0])t.ContentType=o[1],-1!==t.ContentType.search("application/sdp")&&(t.SDPData=ce(e));else if("Content-Length"===o[0])t.ContentLength=parseInt(o[1]);else if("Content-Base"===o[0]){var s=l[n].search("Content-Base:");-1!==s&&(t.ContentBase=l[n].substr(s+13))}else if("Session"===o[0]){var c=o[1].split(";");t.SessionID=parseInt(c[0])}else if("Transport"===o[0]){var f=o[1].split(";");for(r=0;r<f.length;r++){var p=f[r].search("interleaved=");if(-1!==p){var d=f[r].substr(p+12).split("-");d.length>1&&(t.RtpInterlevedID=parseInt(d[0]),t.RtcpInterlevedID=parseInt(d[1]))}}}else if("RTP-Info"===o[0]){o[1]=l[n].substr(9);var m=o[1].split(",");for(t.RTPInfoList=[],r=0;r<m.length;r++){var g=m[r].split(";"),h={},S=0;for(S=0;S<g.length;S++){var v=g[S].search("url=");-1!==v&&(h.URL=g[S].substr(v+4)),-1!==(v=g[S].search("seq="))&&(h.Seq=parseInt(g[S].substr(v+4)))}t.RTPInfoList.push(h)}}}else if(t.ResponseCode===_)for(n=1;n<l.length;n++)if("CSeq"===(o=l[n].split(":"))[0])t.CSeq=parseInt(o[1]);else if("WWW-Authenticate"===o[0]){var P=o[1].split(",");for(r=0;r<P.length;r++){var E=P[r].search("Digest realm=");if(-1!==E){var A=P[r].substr(E+13).split('"');t.Realm=A[1]}if(-1!==(E=P[r].search("nonce="))){var b=P[r].substr(E+6).split('"');t.Nonce=b[1]}}}return t}(n)).ResponseCode===_&&(t&&""===b||!t&&b===w))!function(e){var n,o,i=H.username,l=H.password;n={Method:D.toUpperCase(),Realm:e.Realm,Nonce:e.Nonce,Uri:r},o=function(e,t,n,r,o,i){var l,u;return l=a(e+":"+r+":"+t).toLowerCase(),u=a(i+":"+n).toLowerCase(),a(l+":"+o+":"+u).toLowerCase()}(i,l,n.Uri,n.Realm,n.Nonce,n.Method),b='Authorization: Digest username="'+i+'", realm="'+n.Realm+'",',b+=' nonce="'+n.Nonce+'", uri="'+n.Uri+'", response="'+o+'"',t?(b+="\r\n",re(ee("OPTIONS",null))):re(ee("PLAY",null))}(o);else if(o.ResponseCode===y)if("UnAuthorized"===D){M=ce(n),void 0!==o.ContentBase&&(M.ContentBase=o.ContentBase);var u=0;for(u=0;u<M.Sessions.length;u+=1){var s={};"JPEG"===M.Sessions[u].CodecMime||"H264"===M.Sessions[u].CodecMime||"H265"===M.Sessions[u].CodecMime||"H264-SVC"==M.Sessions[u].CodecMime||"RAW"==M.Sessions[u].CodecMime?(s.codecName=M.Sessions[u].CodecMime,"H264-SVC"===M.Sessions[u].CodecMime&&(s.codecName="H264"),"H265"===M.Sessions[u].CodecMime||M.Sessions[u].CodecMime,s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),void 0!==M.Sessions[u].Framerate&&(s.Framerate=parseInt(M.Sessions[u].Framerate)),C.push(s)):"PCMU"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G726-16")||-1!==M.Sessions[u].CodecMime.search("G726-24")||-1!==M.Sessions[u].CodecMime.search("G726-32")||-1!==M.Sessions[u].CodecMime.search("G726-40")||"PCMA"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G723.1")||-1!==M.Sessions[u].CodecMime.search("G729")||-1!==M.Sessions[u].CodecMime.search("MPA")||-1!==M.Sessions[u].CodecMime.search("L16")?("PCMU"===M.Sessions[u].CodecMime?s.codecName="G.711Mu":"G726-16"===M.Sessions[u].CodecMime?s.codecName="G.726-16":"G726-24"===M.Sessions[u].CodecMime?s.codecName="G.726-24":"G726-32"===M.Sessions[u].CodecMime?s.codecName="G.726-32":"G726-40"===M.Sessions[u].CodecMime?s.codecName="G.726-40":"PCMA"===M.Sessions[u].CodecMime?s.codecName="G.711A":"G723.1"===M.Sessions[u].CodecMime?s.codecName="G.723":"G729"===M.Sessions[u].CodecMime?s.codecName="G.729":"MPA"===M.Sessions[u].CodecMime?s.codecName="mpeg2":"L16"===M.Sessions[u].CodecMime&&(s.codecName="PCM"),s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"mpeg4-generic"===M.Sessions[u].CodecMime||"MPEG4-GENERIC"===M.Sessions[u].CodecMime?(s.codecName="mpeg4-generic",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"vnd.onvif.metadata"===M.Sessions[u].CodecMime?(s.codecName="MetaData",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):"stream-assist-frame"===M.Sessions[u].CodecMime?(s.codecName="stream-assist-frame",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):("audio"===M.Sessions[u].Type&&B({errorCode:201,description:"Audio format not supported"}),e.log("Unknown codec type:",M.Sessions[u].CodecMime,M.Sessions[u].ControlURL))}for(var c=0;c<C.length;c++)C[c].RtpInterlevedID=2*(C[c].trackID.split("=")[1]-0);R=0,clearInterval(U),U=setInterval((function(){return"GET_PARAMETER"===x&&"PAUSE"!=D?f++:f=0,6===f&&(i.fileOver=!0,fe(),O()),re(ee("KEEP_LIVE",null))}),4e4),D="Playing"}else{if("Options"===D)return D="Describe",ee("DESCRIBE",null);if("Describe"===D){M=ce(n),void 0!==o.ContentBase&&(M.ContentBase=o.ContentBase);u=0;for(u=0;u<M.Sessions.length;u+=1){s={};"JPEG"===M.Sessions[u].CodecMime||"H264"===M.Sessions[u].CodecMime||"H265"===M.Sessions[u].CodecMime||"H264-SVC"==M.Sessions[u].CodecMime?(s.codecName=M.Sessions[u].CodecMime,"H264-SVC"==M.Sessions[u].CodecMime&&(s.codecName="H264"),M.Sessions[u].CodecMime,s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),void 0!==M.Sessions[u].Framerate&&(s.Framerate=parseInt(M.Sessions[u].Framerate)),C.push(s)):"PCMU"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G726-16")||-1!==M.Sessions[u].CodecMime.search("G726-24")||-1!==M.Sessions[u].CodecMime.search("G726-32")||-1!==M.Sessions[u].CodecMime.search("G726-40")||"PCMA"===M.Sessions[u].CodecMime||-1!==M.Sessions[u].CodecMime.search("G723.1")||-1!==M.Sessions[u].CodecMime.search("G729")||-1!==M.Sessions[u].CodecMime.search("MPA")||-1!==M.Sessions[u].CodecMime.search("L16")?("PCMU"===M.Sessions[u].CodecMime?s.codecName="G.711Mu":"G726-16"===M.Sessions[u].CodecMime?s.codecName="G.726-16":"G726-24"===M.Sessions[u].CodecMime?s.codecName="G.726-24":"G726-32"===M.Sessions[u].CodecMime?s.codecName="G.726-32":"G726-40"===M.Sessions[u].CodecMime?s.codecName="G.726-40":"PCMA"===M.Sessions[u].CodecMime?s.codecName="G.711A":"G723.1"===M.Sessions[u].CodecMime?s.codecName="G.723":"G729"===M.Sessions[u].CodecMime?s.codecName="G.729":"MPA"===M.Sessions[u].CodecMime?s.codecName="mpeg2":"L16"===M.Sessions[u].CodecMime&&(s.codecName="PCM"),s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"mpeg4-generic"===M.Sessions[u].CodecMime||"MPEG4-GENERIC"===M.Sessions[u].CodecMime?(s.codecName="mpeg4-generic",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),s.Bitrate=parseInt(M.Sessions[u].Bitrate),s.TalkTransType=M.Sessions[u].TalkTransType,C.push(s)):"vnd.onvif.metadata"===M.Sessions[u].CodecMime?(s.codecName="MetaData",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):"stream-assist-frame"===M.Sessions[u].CodecMime?(s.codecName="stream-assist-frame",s.trackID=M.Sessions[u].ControlURL,s.ClockFreq=M.Sessions[u].ClockFreq,s.Port=parseInt(M.Sessions[u].Port),C.push(s)):("audio"===M.Sessions[u].Type&&B({errorCode:201,description:"Audio format not supported"}),e.log("Unknown codec type:",M.Sessions[u].CodecMime,M.Sessions[u].ControlURL))}return R=0,D="Setup",W.length?ee("SETUP",W[R].split("=")[1]-0):ee("SETUP",0)}if("Setup"===D)return I=o.SessionID,R<W.length?(null!=C[R]&&(C[R].RtpInterlevedID=o.RtpInterlevedID,C[R].RtcpInterlevedID=o.RtcpInterlevedID),I?W[R+=1]?ee("SETUP",W[R].split("=")[1]):(D="Play",ee("PLAY",null)):ee("SETUP",W[R].split("=")[1])):I?(D="Play",ee("PLAY",null,0,d)):(R+=1,ee("SETUP",0));if("Play"===D){I=o.SessionID,clearInterval(U),U=setInterval((function(){return"GET_PARAMETER"===x&&"PAUSE"!=D?f++:f=0,6===f&&(i.fileOver=!0,fe(),O()),re(ee("GET_PARAMETER",null))}),4e4);var f=0;D="Playing",S=!0}else"ContinuePlay"===D?D="Playing":"Playing"===D||e.log("unknown rtsp state:"+D)}else if(o.ResponseCode===A)if("Setup"===D&&(t&&"sendonly"===C[R].TalkTransType||!t&&-1!==C[R].trackID.search("trackID=t"))){if(C[R].RtpInterlevedID=-1,C[R].RtcpInterlevedID=-1,R+=1,p=!1,B({errorCode:"504",description:"Talk Service Unavilable",place:"RtspClient.js"}),R<C.length)return ee("SETUP",C[R].trackID);D="Play",ee("PLAY",null,0,d)}else B({errorCode:"503",description:"Service Unavilable"});else if(o.ResponseCode===P){if("Describe"===D||"Options"===D)return void B({errorCode:404,description:"rtsp not found"})}else if(o.ResponseCode===E)return void e.log("RTP disconnection detect!!!")}function ne(t){null!==i&&i.readyState===WebSocket.OPEN?S&&i.send(t):e.log("SendRtpData - Websocket does not exist")}function re(n){if(null!=n&&null!=n&&""!=n)if(null!==i&&i.readyState===WebSocket.OPEN){if(t&&!f)-1!==n.search("DESCRIBE")&&(z=!0,f=!0);null!=n&&i.send(oe(n))}else e.log("websocket未连接")}function oe(e){for(var t=e.length,n=new Uint8Array(new ArrayBuffer(t)),r=0;r<t;r++)n[r]=e.charCodeAt(r);return n}function ae(e){var t=e.split("//")[1].split("/")[0];if(n.includes("?serverIp="))n=n.slice(0,n.indexOf("serverIp="))+"serverIp="+t;else{var r="https:"===location.protocol?"wss":"ws";n=n.includes("8554")?"".concat(r,"://").concat(t):e.replace("rtsp",r)}}function ie(e){var a=new Uint8Array(e.data),f=new Uint8Array(a.length);for(f.set(a,0),s=f.length,q&&(clearTimeout(q),q=0),j&&(clearTimeout(j),j=0);s>0;)if(36!==f[0]){var p=String.fromCharCode.apply(null,f),d=null;if(p.includes("302 Moved"))return ae(r=p.slice(p.indexOf("rtsp://"),p.indexOf("\r\n\r\n"))),Q.prototype.disconnect(),void Q.prototype.connect(n,r,o,N);if((-1!==p.indexOf("OffLine:File Over")||-1!==p.indexOf("OffLine: File Over")||-1!==p.indexOf("OffLine:Internal Error")||p.includes("is_session_end: true"))&&(i.fileOver=!0,F()),-1!==p.indexOf("OffLine:Internal Error")&&B({errorCode:500,description:"Internal Error"}),-1!==p.indexOf("OffLine:KmsUnavailable")&&B({errorCode:203,description:"KmsUnavailable"}),"Describe"===D&&-1!==p.indexOf("a=control:trackID=")&&(W=p.split("a=control:").filter((function(e){return e.startsWith("trackID=")})).map((function(e){return e.split("\r\n")[0]}))||[]),-1!==p.indexOf("Range: npt=")){var m=p.split("Range: ")[1].split("\r\n")[0],S=m.split("npt=")[1].split("-")[0],y=m.split("-")[1];G(y-S)}var v=p.indexOf("interleaved=");if(-1!=v){var _=p.slice(v),P=_.indexOf("-");if(-1!=P){var E=_.slice(12,P);if(E/=2,-1!=(v=p.indexOf("ssrc="))){var A=p.slice(v+5,v+5+8),b=parseInt(A,16);0!=b&&(0==E?h.videoSSRC=b:1==E?h.audio1SSRC=b:2==E&&(h.audio2SSRC=b))}}}z||!Y&&-1!==p.indexOf("200 OK")?(z&&(g=p),d=p.lastIndexOf("\r\n"),z=!1,Y=!0):d=p.search("\r\n\r\n");var w=-1;if(-1===(w=t?p.search("RTSP"):p.search("HTTP")))return void(f=new Uint8Array);if(-1===d)return void(s=f.length);l=f.subarray(w,d+6),f=f.subarray(d+6),re(te(String.fromCharCode.apply(null,l))),s=f.length}else{if(x="PLAY",clearTimeout(V),V=setTimeout((function(){x="GET_PARAMETER"}),1e4),u=f.subarray(0,6),!((c=u[2]<<24|u[3]<<16|u[4]<<8|u[5])+6<=f.length))return void(s=f.length);var C=f.subarray(6,c+6);se(u,C),f=f.subarray(c+6),s=f.length}}function le(){q&&clearTimeout(q),X&&(clearTimeout(X),X=0),q=setTimeout((function(){B({errorCode:"407",description:"Request Timeout"})}),3e4)}function ue(){j&&clearTimeout(j),j=setTimeout((function(){X&&(clearTimeout(X),X=0),B({errorCode:"408",description:"Short Request Timeout"})}),1e3*K)}function se(e,t){X&&(clearTimeout(X),X=0),L(t),!0}function ce(e){var n={};n.Sessions=[];var r=null;-1!==e.search("Content-Type: application/sdp")||-1!==e.search("Private-Type: application/sdp")?r=e.split("\r\n\r\n")[1]:r=e;var o=r.split("\r\n"),a=0,i=!1;for(a=0;a<o.length;a++){var l=o[a].split("=");if(l.length>0)switch(l[0]){case"a":var u=l[1].split(":");if(u.length>1){if("control"===u[0]){var s=o[a].search("control:");!0===i?-1!==s&&(n.Sessions[n.Sessions.length-1].ControlURL=o[a].substr(s+8)):-1!==s&&(n.BaseURL=o[a].substr(s+8))}else if("rtpmap"===u[0]){var c=u[1].split(" ");n.Sessions[n.Sessions.length-1].PayloadType=c[0];var f=c[1].split("/");n.Sessions[n.Sessions.length-1].CodecMime=f[0],f.length>1&&(n.Sessions[n.Sessions.length-1].ClockFreq=f[1])}else if("framesize"===u[0]){var d=u[1].split(" ");if(d.length>1){var m=d[1].split("-");n.Sessions[n.Sessions.length-1].Width=m[0],n.Sessions[n.Sessions.length-1].Height=m[1]}}else if("framerate"===u[0])n.Sessions[n.Sessions.length-1].Framerate=u[1];else if("fmtp"===u[0]){var g=o[a].split(" ");if(g.length<2)continue;for(var h=1;h<g.length;h++){var S=g[h].split(";"),y=0;for(y=0;y<S.length;y++){var v=S[y].search("mode=");if(-1!==v&&(n.Sessions[n.Sessions.length-1].mode=S[y].substr(v+5)),-1!==(v=S[y].search("config="))&&(n.Sessions[n.Sessions.length-1].config=S[y].substr(v+7),k.config=n.Sessions[n.Sessions.length-1].config,k.clockFreq=n.Sessions[n.Sessions.length-1].ClockFreq,k.bitrate=n.Sessions[n.Sessions.length-1].Bitrate),-1!==(v=S[y].search("sprop-vps="))&&(n.Sessions[n.Sessions.length-1].VPS=S[y].substr(v+10)),-1!==(v=S[y].search("sprop-sps="))&&(n.Sessions[n.Sessions.length-1].SPS=S[y].substr(v+10)),-1!==(v=S[y].search("sprop-pps="))&&(n.Sessions[n.Sessions.length-1].PPS=S[y].substr(v+10)),-1!==(v=S[y].search("sprop-parameter-sets="))){var _=S[y].substr(v+21).split(",");_.length>1&&(n.Sessions[n.Sessions.length-1].SPS=_[0],n.Sessions[n.Sessions.length-1].PPS=_[1])}}}}}else 1===u.length&&("recvonly"===u[0]?n.Sessions[n.Sessions.length-1].TalkTransType="recvonly":"sendonly"===u[0]&&(n.Sessions[n.Sessions.length-1].TalkTransType="sendonly"));break;case"m":var P=l[1].split(" "),E={};E.Type=P[0],E.Port=P[1],E.Payload=P[3],n.Sessions.push(E),i=!0;break;case"b":if(!0===i){var A=l[1].split(":");n.Sessions[n.Sessions.length-1].Bitrate=A[1]}}}if(!t&&p)for(var b=0;b<n.Sessions.length;b+=1)"1"!==n.Sessions[b].ControlURL.split("=")[1]&&"2"!==n.Sessions[b].ControlURL.split("=")[1]||n.Sessions.splice(b,1);return n}function fe(){re(ee("TEARDOWN",null)),clearInterval(U),U=null,null!==i&&(i.onerror=null),null!==i&&i.readyState===WebSocket.OPEN&&(i.close(),i=null,I=null)}return Q.prototype={connect:function(e,a,l,u){N=u,t=u.bRtspFlag,m=u.bPlayBack,Z=u.bBroadcast,K=u.nShortTimeout||3,J=u.nRtspResponseTimeout||8,D=t?"Options":"UnAuthorized",i||(n=e,r=a,o=l,p=u.bTalkService,d=u.nRange,(i=new WebSocket(n)).binaryType="arraybuffer",i.fileOver=!1,i.addEventListener("message",ie,!1),i.onopen=function(){if(t){var e="OPTIONS "+r+" RTSP/1.0\r\nCSeq: "+T+"\r\n";e+="User-Agent: PlaySDK Client/1.0\r\n";var n=oe(e+="\r\n");i.send(n)}else b=w,re(ee("PLAY",null,null))},i.onerror=function(e){B({errorCode:202,description:"WebSocket Error"})},i.onclose=function(e){i&&!i.fileOver&&B({errorCode:204,description:"WebSocket Close"})})},disconnect:function(){q&&(clearTimeout(q),q=0),j&&(clearTimeout(j),j=0),re(ee("TEARDOWN",null,null)),clearInterval(U),U=null,null!==i&&(i.onerror=null),null!==i&&i.readyState===WebSocket.OPEN&&(i.close(),i=null,I=null)},controlPlayer:function(t){var n="";switch(t.command,t.command){case"PLAY":if(D="Play",null!=t.range){n=ee("PLAY",null,null,t.range);break}n=ee("PLAY",null,null);break;case"PLAY_SEEK":n=ee("PLAY",null,null,t.data);break;case"PAUSE":if("PAUSE"===D)break;D="PAUSE",n=ee("PAUSE",null,null);break;case"SCALE":n=ee("SCALE",null,null,t.data);break;case"TEARDOWN":n=ee("TEARDOWN",null,null);break;default:e.log("未知指令: "+t.command)}""!=n&&re(n)},setRTSPURL:function(e){r=e},setCallback:function(e,t){switch(e){case"Disconnect":O=t;break;case"PlayBackStreamRange":G=t;break;case"Error":B=t}},setUserInfo:function(e,t){H.username=e,H.password=t},setRtpDataCallback:function(e){L=e},setStreamFinishCallback:function(e){F=e},sendRtpData:function(e){ne(e)},GetSdpInfo:function(){return g},GetSSRC:function(){return h}},new Q};function P(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],u=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return E(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?E(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function A(e,t,n,r){return y._RTSP_PutStream(e,t,n,r)}function b(e,t,n,r){return y._RTSV_PutStream(e,t,n,r)}var w=function(){var e=null,t=!1,n=!1,r=null,o=null,a=null,i=null;function l(){}return l.prototype={StartStream:function(l,u){return null==l.strDeviceID&&(l.strDeviceID=0),(t=u)?function(e,t,n,r,o,a){var i=y.intArrayFromString(t).concat(0),l=y._malloc(i.length);y.HEAPU8.set(i,l);var u=y.intArrayFromString(n).concat(0),s=y._malloc(u.length);y.HEAPU8.set(u,s);var c=y.intArrayFromString(r).concat(0),f=y._malloc(c.length);y.HEAPU8.set(c,f);var p=y._RTSP_StartStream(e,l,s,f,o,a);y._free(l),y._free(s),y._free(f)}(e=(y=window.SCModule)._RTSP_InitSession(),l.strRtspvUrl,l.strRtspvUri,l.strDeviceID,l.bTalkService,!1):(null!=l.nAudioType&&null!=l.nFrequency&&null!=l.nChannelNum||(l.nAudioType=14,l.nFrequency=8e3,l.nChannelNum=1),function(e,t,n,r,o,a,i,l,u,s,c){var f=y.intArrayFromString(t).concat(0),p=y._malloc(f.length);y.HEAPU8.set(f,p);var d=y.intArrayFromString(n).concat(0),m=y._malloc(d.length);y.HEAPU8.set(d,m);var g=y.intArrayFromString(r).concat(0),h=y._malloc(g.length);if(y.HEAPU8.set(g,h),o&&y._RTSV_InitTalkInfo(e,a,i,l),u){var S=y.intArrayFromString(s).concat(0),v=y._malloc(S.length);y.HEAPU8.set(S,v);var _=y.intArrayFromString(c).concat(0),P=y._malloc(_.length);y.HEAPU8.set(_,P),y._RTSV_SetWSSEInfo(e,u,v,P),y._free(v),y._free(P)}var E=y._RTSV_StartStream(e,p,m,h);y._free(p),y._free(m),y._free(h)}(e=(y=window.SCModule)._RTSV_InitSession(),l.strRtspvUrl,l.strRtspvUri,l.strDeviceID,l.bTalkService,l.nAudioType,l.nFrequency,l.nChannelNum,!1,null,null)),(n=l.bTalkService)&&(r=y._malloc(10240),o=new Uint8Array(y.HEAPU8.buffer,r,10240),a=y._malloc(1048576),i=new Uint8Array(y.HEAPU8.buffer,a,1048576)),e},StopStream:function(){var a;t?(a=e,y._RTSP_StopStream(a)):function(e){y._RTSV_StopStream(e)}(e),e=null,n&&(o=null,y._free(r))},PauseStream:function(n){var r;n?t?(r=e,y._RTSP_PauseStream(r)):function(e){y._RTSV_PauseStream(e)}(e):t?function(e){y._RTSP_ResumeStream(e)}(e):function(e){y._RTSV_ResumeStream(e)}(e)},PlayControl:function(n,r,o){var a=!1;o>=8&&(a=!0),t?function(e,t,n,r,o){y._RTSP_PlayControl(e,t,n,r,o)}(e,n,r,o,a):function(e,t,n,r,o){y._RTSV_PlayControl(e,t,n,r,o)}(e,n,r,o,a)},PutStream:function(n,r){if(null!=o){var a=n.subarray(6);if(5==r)o.set(a),t?A(e,o.byteOffset,a.length,r):b(e,o.byteOffset,a.length,r);else{if(a.length>1048576)return void console.warn("StreamClient buffer not enough, DataLen:"+a.length);i.set(a),t?A(e,i.byteOffset,a.length,r):b(e,i.byteOffset,a.length,r)}}},GetPlayInfo:function(){var n=0;if(t){var r=P(function(e){var t=y._malloc(8),n=y._malloc(8);y._RTSP_GetPlayInfo(e,t,n);var r=y.HEAPF64[t>>3],o=y.HEAPF64[n>>3];return y._free(t),y._free(n),[r,o]}(e),2),o=r[0];n=r[1]-o}return n},SetMsgWaitTimeout:function(n){t?function(e,t){y._RTSP_SetMsgTimeout(e,t)}(e,n):function(e,t){y._RTSV_SetMsgTimeout(e,t)}(e,n)}},new l},C="Opera",T="Chrome",M="Firefox",D="Edge",R="Edg",I="IE",U="Safari";function k(){var e=navigator.userAgent;return e.includes("Edge")?D:e.includes("Edg")?R:e.includes("Firefox")?M:e.includes("Chrome")?T:e.includes("Safari")?U:e.includes("compatible")&&e.includes("MSIE")&&e.includes("Opera")?I:e.includes("Opera")?C:""}function H(e){return navigator.userAgent.split(e)[1].split(".")[0].slice(1)}var L=function(){var e=2;function t(){}return t.prototype={setPrintLogLevel:function(t){e=t},fatal:function(t){e>=1&&console.error(t)},error:function(t){e>=2&&console.error(t)},warn:function(t){e>=3&&console.warn(t)},info:function(t){e>=4&&console.info(t)},trace:function(t){e>=5&&console.log(t)},log:function(t){e>=6&&console.log(t)}},new t};function O(){var e=navigator.userAgent.toLowerCase(),t=navigator.appName,n=null;return"Microsoft Internet Explorer"===t||e.indexOf("trident")>-1||e.indexOf("edge/")>-1?(n="ie","Microsoft Internet Explorer"===t?(e=/msie ([0-9]{1,}[\.0-9]{0,})/.exec(e),n+=parseInt(e[1])):e.indexOf("trident")>-1?n+=11:e.indexOf("edge/")>-1&&(n="edge")):e.indexOf("safari")>-1?n=e.indexOf("chrome")>-1?"chrome":"safari":e.indexOf("firefox")>-1&&(n="firefox"),n}(function(){function e(){}e.createFromElementId=function(t){for(var n=document.getElementById(t),r="",o=n.firstChild;o;)3===o.nodeType&&(r+=o.textContent),o=o.nextSibling;var a=new e;return a.type=n.type,a.source=r,a},e.createFromSource=function(t,n){var r=new e;return r.type=t,r.source=n,r}})(),function(){function e(e){this.gl=e,this.program=this.gl.createProgram()}e.prototype={attach:function(e){this.gl.attachShader(this.program,e.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(e){return this.gl.getAttribLocation(this.program,e)},setMatrixUniform:function(e,t){var n=this.gl.getUniformLocation(this.program,e);this.gl.uniformMatrix4fv(n,!1,t)}}}(),function(){var e=null;function t(e,t,n){this.gl=e,this.size=t,this.texture=e.createTexture(),e.bindTexture(e.TEXTURE_2D,this.texture),this.format=n||e.LUMINANCE,e.texImage2D(e.TEXTURE_2D,0,this.format,t.w,t.h,0,this.format,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)}t.prototype={fill:function(e,t){var n=this.gl;n.bindTexture(n.TEXTURE_2D,this.texture),t?n.texSubImage2D(n.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,n.UNSIGNED_BYTE,e):n.texImage2D(n.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,n.UNSIGNED_BYTE,e)},bind:function(t,n,r){var o=this.gl;e||(e=[o.TEXTURE0,o.TEXTURE1,o.TEXTURE2]),o.activeTexture(e[t]),o.bindTexture(o.TEXTURE_2D,this.texture),o.uniform1i(o.getUniformLocation(n.program,r),t)}}}();function F(e){this.buffer=e,this.previous=null,this.next=null}var B=function(e){F.call(this,e)};function G(e){var t=e||25;function n(){this.first=null,this.size=0}return n.prototype={enqueue:function(e){this.size>=t&&this.clear();var n=new B(e);if(null===this.first)this.first=n;else{for(var r=this.first;null!==r.next;)r=r.next;r.next=n}return this.size+=1,n},dequeue:function(){var e=null;return null!==this.first&&(e=this.first,this.first=this.first.next,this.size-=1),e},clear:function(){for(var e=null;null!==this.first;)e=this.first,this.first=this.first.next,this.size-=1,e.buffer=null,e=null;this.size=0,this.first=null}},new n}var x=function(e){var t=[],n={},r=e;function o(){for(var e in t)t[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)];0,1==r?n.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]):2==r&&(n.FTYP=new Uint8Array([105,115,111,109,0,0,2,0,105,115,111,109,105,115,111,50,97,118,99,49,109,112,52,49])),n.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),n.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),n.STSC=n.STCO=n.STTS,n.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),n.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),n.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),n.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),n.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}t={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],hev1:[],hvcC:[]};var a=function(e){for(var t=8,n=Array.prototype.slice.call(arguments,1),r=0;r<n.length;r++)t+=n[r].byteLength;var o=new Uint8Array(t),a=0;o[a++]=t>>>24&255,o[a++]=t>>>16&255,o[a++]=t>>>8&255,o[a++]=255&t,o.set(e,a),a+=4;for(r=0;r<n.length;r++)o.set(n[r],a),a+=n[r].byteLength;return o},i=function(e){return a(t.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),(n=e.config,r=n.length,o=new Uint8Array([0,0,0,0,3,23+r,0,1,0,4,15+r,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([r]).concat(n).concat([6,1,2])),a(t.esds,o)));var n,r,o},l=function(e){return"audio"===e.type?a(t.stsd,n.STSD_PREFIX,i(e)):a(t.stsd,n.STSD_PREFIX,function(e){var n=e.vps||[],o=e.sps||[],i=e.pps||[],l=[],u=[],s=[],c=0;for(c=0;c<n.length;c++)l.push((65280&n[c].byteLength)>>>8),l.push(255&n[c].byteLength),l=l.concat(Array.prototype.slice.call(n[c]));for(c=0;c<o.length;c++)u.push((65280&o[c].byteLength)>>>8),u.push(255&o[c].byteLength),u=u.concat(Array.prototype.slice.call(o[c]));for(c=0;c<i.length;c++)s.push((65280&i[c].byteLength)>>>8),s.push(255&i[c].byteLength),s=s.concat(Array.prototype.slice.call(i[c]));return 1==r?a(t.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a(t.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([o.length]).concat(u).concat([i.length]).concat(s)))):2==r?a(t.hev1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a(t.hvcC,new Uint8Array([1,e.general_profile_flag,(4278190080&e.general_profile_compatibility_flags)>>>24,(16711680&e.general_profile_compatibility_flags)>>>16,(65280&e.general_profile_compatibility_flags)>>>8,255&e.general_profile_compatibility_flags,(0xff0000000000&e.general_constraint_indicator_flags)>>>40,(0xff00000000&e.general_constraint_indicator_flags)>>>32,(4278190080&e.general_constraint_indicator_flags)>>>24,(16711680&e.general_constraint_indicator_flags)>>>16,(65280&e.general_constraint_indicator_flags)>>>8,255&e.general_constraint_indicator_flags,e.general_level_idc,240,0,252,252|e.chroma_format_idc,248|e.bitDepthLumaMinus8,248|e.bitDepthChromaMinus8,0,0,e.rate_layers_nested_length,3].concat([32,0,1]).concat(l).concat([33,0,1]).concat(u).concat([34,0,1]).concat(s)))):void 0}(e))},u=function(e){var r=null;return r="audio"===e.type?a(t.smhd,n.SMHD):a(t.vmhd,n.VMHD),a(t.minf,r,a(t.dinf,a(t.dref,n.DREF)),function(e){return a(t.stbl,l(e),a(t.stts,n.STTS),a(t.stsc,n.STSC),a(t.stsz,n.STSZ),a(t.stco,n.STCO))}(e))},s=function(e){return a(t.mdia,function(e){var n=e.timescale,r=e.duration;return a(t.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,85,196,0,0]))}(e),function(e){var r=null;return r="audio"===e.type?n.HDLR_AUDIO:n.HDLR_VIDEO,a(t.hdlr,r)}(e),u(e))},c=function(e){return a(t.trak,function(e){var n=e.id,r=e.duration,o=e.width,i=e.height;return a(t.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,o>>>8&255,255&o,0,0,i>>>8&255,255&i,0,0]))}(e),s(e))},f=function(e){return a(t.mvex,function(e){var n=e.id,r=new Uint8Array([0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return a(t.trex,r)}(e))},p=function(e){var n,r,o=(n=e.timescale,r=e.duration,a(t.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))),i=c(e),l=f(e);return a(t.moov,o,i,l)},d=function(e,n){return"audio"===e.type?audioTrun(e,n):function(e,n){var r,o=null,i=null,l=0,u=n;if(null===(r=e.samples||[])[0].frameDuration)for(u+=24+4*r.length,o=trunHeader(r,u),l=0;l<r.length;l++)i=r[l],o=o.concat([(4278190080&i.size)>>>24,(16711680&i.size)>>>16,(65280&i.size)>>>8,255&i.size]);else for(o=function(e,t){return[0,0,3,5,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t,0,0,0,0]}(r,u+=24+4*r.length+4*r.length),l=0;l<r.length;l++)i=r[l],o=o.concat([(4278190080&i.frameDuration)>>>24,(16711680&i.frameDuration)>>>16,(65280&i.frameDuration)>>>8,255&i.frameDuration,(4278190080&i.size)>>>24,(16711680&i.size)>>>16,(65280&i.size)>>>8,255&i.size]);return a(t.trun,new Uint8Array(o))}(e,n)},m=function(e,n){return a(t.moof,function(e){var n=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return a(t.mfhd,n)}(e),function(e){var n,r,o;return n=a(t.tfhd,new Uint8Array([0,2,0,0,0,0,0,1])),r=a(t.tfdt,new Uint8Array([0,0,0,0,e.baseMediaDecodeTime>>>24&255,e.baseMediaDecodeTime>>>16&255,e.baseMediaDecodeTime>>>8&255,255&e.baseMediaDecodeTime])),72,o=d(e,72),a(t.traf,n,r,o)}(n))};return o.prototype={initSegment:function(e){var r=a(t.ftyp,n.FTYP),o=p(e),i=new Uint8Array(r.byteLength+o.byteLength);return i.set(r,0),i.set(o,r.byteLength),i},mediaSegment:function(e,n,r,o){var i=m(e,n),l=function(e){return a(t.mdat,e)}(r),u=null;return(u=new Uint8Array(i.byteLength+l.byteLength)).set(i),u.set(l,i.byteLength),u}},new o};function V(e){var t=0,n=null,r=e;function o(){t=0,n=new W}function a(e,n){var r=n,o=t+r>>3;return r=t+n&7,e[o]>>7-(7&r)&1}function i(e,n){var r=t>>3,o=8*(r+1)-t;if(o<8)for(var a=0;a<3;a++){var i=e[r+a];i=0==a?i>>o<<o:2==a?i&255>>8-o|1<<o:0,e.set([i],r+a)}else e.set([0],r),e.set([1],r+1)}function l(e,t){if(t<=25)var n=u(e,t);else n=u(e,16)<<t-16|u(e,t-16);return n}function u(e,n){var r=0;if(1===n)r=a(e,0);else for(var o=0;o<n;o++)r=(r<<1)+a(e,o);return t+=n,r}function s(e,n){for(var r=0,o=0,i=n;t+i<8*e.length&&!a(e,i++);)r++;if(0===r)return t+=1,0;o=1<<r;for(var l=r-1;l>=0;l--,i++)o|=a(e,i)<<l;return t+=2*r+1,o-1}function c(e,t){var n=s(e,t);return 1&n?(n+1)/2:-n/2}function f(e){n.put("cpb_cnt_minus1",s(e,0)),n.put("bit_rate_scale",u(e,4)),n.put("cpb_size_scale",u(e,4));for(var t=n.get("cpb_cnt_minus1"),r=new Array(t),o=new Array(t),a=new Array(t),i=0;i<=t;i++)r[i]=s(e,0),o[i]=s(e,0),a[i]=u(e,1);n.put("bit_rate_value_minus1",r),n.put("cpb_size_value_minus1",o),n.put("cbr_flag",a),n.put("initial_cpb_removal_delay_length_minus1",u(e,5)),n.put("cpb_removal_delay_length_minus1",u(e,5)),n.put("dpb_output_delay_length_minus1",u(e,5)),n.put("time_offset_length",u(e,5))}function p(e,t){var n=Number(e).toString(16);for(t=null==t?t=2:t;n.length<t;)n="0"+n;return n}return o.prototype={parse:function(e){if(t=0,n.clear(),1==r){n.put("forbidden_zero_bit",u(e,1)),n.put("nal_ref_idc",u(e,2)),n.put("nal_unit_type",u(e,5)),n.put("profile_idc",u(e,8)),n.put("profile_compatibility",u(e,8)),n.put("level_idc",u(e,8)),n.put("seq_parameter_set_id",s(e,0));var o=n.get("profile_idc");if((100===o||110===o||122===o||244===o||44===o||83===o||86===o||118===o||128===o||138===o||139===o||134===o)&&(n.put("chroma_format_idc",s(e,0)),3===n.get("chroma_format_idc")&&n.put("separate_colour_plane_flag",u(e,1)),n.put("bit_depth_luma_minus8",s(e,0)),n.put("bit_depth_chroma_minus8",s(e,0)),n.put("qpprime_y_zero_transform_bypass_flag",u(e,1)),n.put("seq_scaling_matrix_present_flag",u(e,1)),n.get("seq_scaling_matrix_present_flag"))){for(var a=3!==n.get("chroma_format_idc")?8:12,p=new Array(a),d=0;d<a;d++)if(p[d]=u(e,1),p[d])for(var m=d<6?16:64,g=8,h=8,S=0;S<m;S++)h&&(h=(g+c(e,0)+256)%256),g=0===h?g:h;n.put("seq_scaling_list_present_flag",p)}if(n.put("log2_max_frame_num_minus4",s(e,0)),n.put("pic_order_cnt_type",s(e,0)),0===n.get("pic_order_cnt_type"))n.put("log2_max_pic_order_cnt_lsb_minus4",s(e,0));else if(1===n.get("pic_order_cnt_type")){n.put("delta_pic_order_always_zero_flag",u(e,1)),n.put("offset_for_non_ref_pic",c(e,0)),n.put("offset_for_top_to_bottom_field",c(e,0)),n.put("num_ref_frames_in_pic_order_cnt_cycle",s(e,0));for(var y=0;y<n.get("num_ref_frames_in_pic_order_cnt_cycle");y++)n.put("num_ref_frames_in_pic_order_cnt_cycle",c(e,0))}n.put("num_ref_frames",s(e,0)),n.put("gaps_in_frame_num_value_allowed_flag",u(e,1)),n.put("pic_width_in_mbs_minus1",s(e,0)),n.put("pic_height_in_map_units_minus1",s(e,0)),n.put("frame_mbs_only_flag",u(e,1)),0===n.get("frame_mbs_only_flag")&&n.put("mb_adaptive_frame_field_flag",u(e,1)),n.put("direct_8x8_interence_flag",u(e,1)),n.put("frame_cropping_flag",u(e,1)),1===n.get("frame_cropping_flag")&&(n.put("frame_cropping_rect_left_offset",s(e,0)),n.put("frame_cropping_rect_right_offset",s(e,0)),n.put("frame_cropping_rect_top_offset",s(e,0)),n.put("frame_cropping_rect_bottom_offset",s(e,0))),n.put("vui_parameters_present_flag",u(e,1)),n.get("vui_parameters_present_flag")&&function(e){n.put("aspect_ratio_info_present_flag",u(e,1)),n.get("aspect_ratio_info_present_flag")&&(n.put("aspect_ratio_idc",u(e,8)),255===n.get("aspect_ratio_idc")&&(i(e),n.put("sar_width",u(e,16)),i(e),n.put("sar_height",u(e,16)))),n.put("overscan_info_present_flag",u(e,1)),n.get("overscan_info_present_flag")&&n.put("overscan_appropriate_flag",u(e,1)),n.put("video_signal_type_present_flag",u(e,1)),n.get("video_signal_type_present_flag")&&(n.put("video_format",u(e,3)),n.put("video_full_range_flag",u(e,1)),n.put("colour_description_present_flag",u(e,1)),n.get("colour_description_present_flag")&&(n.put("colour_primaries",u(e,8)),n.put("transfer_characteristics",u(e,8)),n.put("matrix_coefficients",u(e,8)))),n.put("chroma_loc_info_present_flag",u(e,1)),n.get("chroma_loc_info_present_flag")&&(n.put("chroma_sample_loc_type_top_field",s(e,0)),n.put("chroma_sample_loc_type_bottom_field",s(e,0))),n.put("timing_info_present_flag",u(e,1)),n.get("timing_info_present_flag")&&(n.put("num_units_in_tick",u(e,32)),n.put("time_scale",u(e,32)),n.put("fixed_frame_rate_flag",u(e,1))),n.put("nal_hrd_parameters_present_flag",u(e,1)),n.get("nal_hrd_parameters_present_flag")&&f(e),n.put("vcl_hrd_parameters_present_flag",u(e,1)),n.get("vcl_hrd_parameters_present_flag")&&f(e),(n.get("nal_hrd_parameters_present_flag")||n.get("vcl_hrd_parameters_present_flag"))&&n.put("low_delay_hrd_flag",u(e,1)),n.put("pic_struct_present_flag",u(e,1)),n.put("bitstream_restriction_flag",u(e,1)),n.get("bitstream_restriction_flag")&&(n.put("motion_vectors_over_pic_boundaries_flag",u(e,1)),n.put("max_bytes_per_pic_denom",s(e,0)),n.put("max_bits_per_mb_denom",s(e,0)))}(e)}else if(2==r){var v=new ArrayBuffer(256),_=new Uint8Array(v);!function(e,t,n,r){for(var o=0,a=0;o+2<t&&a+2<r;++o)0==e[o]&&0==e[o+1]&&3==e[o+2]?(n[a++]=e[o++],n[a++]=e[o++]):n[a++]=e[o];for(;o<t&&a<r;)n[a++]=e[o++]}(e,e.length,_,256);var P=[],E=[];u(_,4);var A=u(_,3);for(n.put("temporalIdNested",u(_,1)),n.put("general_profile_space",u(_,2)),n.put("general_tier_flag",u(_,1)),n.put("general_profile_idc",u(_,5)),n.put("general_profile_compatibility_flags",l(_,32)),n.put("general_constraint_indicator_flags",(b=_,(w=48)<=32?l(b,w):l(b,w-32)<<32|l(b,32))),n.put("general_level_idc",u(_,8)),d=0;d<A&&d<6;d++)P[d]=u(_,1),E[d]=u(_,1);if(A>0)for(d=A;d<8;d++)u(_,2);for(d=0;d<A&&d<6;d++)E[d]&&u(_,8);s(_,0);n.put("chroma_format_idc",s(_,0));s(_,0),s(_,0);u(_,1),s(_,0),s(_,0),s(_,0),s(_,0),n.put("bitDepthLumaMinus8",s(_,0)+8),n.put("bitDepthChromaMinus8",s(_,0)+8),v=null,_=null}var b,w;return!0},getSizeInfo:function(){var e=0,t=0;0===n.get("chroma_format_idc")?e=t=0:1===n.get("chroma_format_idc")?e=t=2:2===n.get("chroma_format_idc")?(e=2,t=1):3===n.get("chroma_format_idc")&&(0===n.get("separate_colour_plane_flag")?e=t=1:1===n.get("separate_colour_plane_flag")&&(e=t=0));var r=n.get("pic_width_in_mbs_minus1")+1,o=n.get("pic_height_in_map_units_minus1")+1,a=(2-n.get("frame_mbs_only_flag"))*o,i=0,l=0,u=0,s=0;1===n.get("frame_cropping_flag")&&(i=n.get("frame_cropping_rect_left_offset"),l=n.get("frame_cropping_rect_right_offset"),u=n.get("frame_cropping_rect_top_offset"),s=n.get("frame_cropping_rect_bottom_offset"));var c=16*r*(16*a);return{width:16*r-e*(i+l),height:16*a-t*(2-n.get("frame_mbs_only_flag"))*(u+s),decodeSize:c}},getSpsValue:function(e){return n.get(e)},getCodecInfo:function(){if(1==r)return"avc1."+n.get("profile_idc").toString(16)+(n.get("profile_compatibility")<15?"0"+n.get("profile_compatibility").toString(16):n.get("profile_compatibility").toString(16))+n.get("level_idc").toString(16);if(2==r){var e="hev1.";switch(n.get("general_profile_space")){case 0:e+="";break;case 1:e+="A";break;case 2:e+="B";break;case 3:e+="C"}e+=n.get("general_profile_idc"),e+=".";for(var t=n.get("general_profile_compatibility_flags"),o=0,a=0;a<32&&(o|=1&t,31!=a);a++)o<<=1,t>>=1;e+=p(o,0),e+=".",0===n.get("general_tier_flag")?e+="L":e+="H",e+=n.get("general_level_idc");var i=n.get("general_constraint_indicator_flags"),l=[(65280&i)>>>8,(16711680&i)>>>16,(4278190080&i)>>>24,(0xff00000000&i)>>>32,(0xff0000000000&i)>>>40],u=!1,s="";for(a=5;0<=a;a--)(l[a]||u)&&(s="."+p(l[a],0)+s,u=!0);return e+=s}}},new o}var N,W=function(){this.map={}};W.prototype={put:function(e,t){this.map[e]=t},get:function(e){return this.map[e]},containsKey:function(e){return e in this.map},containsValue:function(e){for(var t in this.map)if(this.map[t]===e)return!0;return!1},isEmpty:function(e){return 0===this.size()},clear:function(){for(var e in this.map)delete this.map[e]},remove:function(e){delete this.map[e]},keys:function(){var e=new Array;for(var t in this.map)e.push(t);return e},values:function(){var e=new Array;for(var t in this.map)e.push(this.map[t]);return e},size:function(){var e=0;for(var t in this.map)e++;return e}};function z(e){return N._RENDER_Destroy(e)}var Y;function q(e,t,n){return N._RENDER_AlgoCommand(e,t,n)}var j=5,K=6,X=7,J=16,Z=18,Q=19,$=21,ee=22,te=25,ne=28,re=1,oe=2,ae=3,ie=4;function le(e,t){Y._DRAW_CleanScreen(e),Y._DRAW_DrawByRenderHandle(e,0,t)}function ue(e){this.buffer=e,this.previous=null,this.next=null}var se=function(e,t,n,r,o){var a=n,i=e,l=r,u=null,s=0,c=0,f=!1,p=null,d=0,m=0,g=!1,h=!1,S=1,y=!1,v=0,_=0,P=0,E=o,A=-1,b=0,w=!1,C=null,T=!1,M=!1,D={},R=120,I=80,U=240;function k(){p=new G(a),f=!1}var H=function(e){if(null!==u){if(!w){w=!0;var t={decodeMode:"canvas"};t.width=v,t.height=_,1==b?t.encodeMode="H264":2==b&&(t.encodeMode="H265"),C&&C(t)}return A=e.buffer.nFrameID,function(e,t,n,r){var o,a=N._malloc(80),i=0,l=null,u=0,s=null,c=0,f=null;9!=t.ImageFormat&&(i=t.width0*t.height0,l=N._malloc(i),N.writeArrayToMemory(t.DataY,l),u=t.width1*t.height1,s=N._malloc(u),N.writeArrayToMemory(t.DataU,s),c=t.width2*t.height2,f=N._malloc(c),N.writeArrayToMemory(t.DataV,f)),N.HEAP32[a/4+0]=0,N.HEAP32[a/4+1]=t.ImageFormat,N.HEAP32[a/4+2]=0,N.HEAP32[a/4+3]=0,9!=t.ImageFormat?(N.HEAP32[a/4+4]=l,N.HEAP32[a/4+5]=s,N.HEAP32[a/4+6]=f):(N.HEAP32[a/4+4]=t.DataY,N.HEAP32[a/4+5]=0,N.HEAP32[a/4+6]=0),N.HEAP32[a/4+7]=t.width0,N.HEAP32[a/4+8]=t.width1,N.HEAP32[a/4+9]=t.width2,N.HEAP32[a/4+10]=t.height0,N.HEAP32[a/4+11]=t.height1,N.HEAP32[a/4+12]=t.height2,N.HEAP32[a/4+13]=t.width0,N.HEAP32[a/4+14]=t.width1,N.HEAP32[a/4+15]=t.width2,N.HEAP32[a/4+16]=t.height0,N.HEAP32[a/4+17]=t.height1,N.HEAP32[a/4+18]=t.height2,N.HEAP32[a/4+19]=0;var p=0,d=0;if(0!==n){p=N._malloc(16),N.HEAP32[p/4+0]=n.left,N.HEAP32[p/4+1]=n.top,N.HEAP32[p/4+2]=n.right,N.HEAP32[p/4+3]=n.bottom}if(0!==r){d=N._malloc(16),N.HEAP32[d/4+0]=r.left,N.HEAP32[d/4+1]=r.top,N.HEAP32[d/4+2]=r.right,N.HEAP32[d/4+3]=r.bottom}o=N._RENDER_DrawImage(e,a,p,d),9!=t.ImageFormat&&(N._free(l),N._free(s),N._free(f)),N._free(a),0!==p&&N._free(p),0!==d&&N._free(d)}(u,e.buffer.Image,0,0),y&&le(P,e.buffer.nFrameID),delete e.buffer,e.buffer=null,e.previous=null,e.next=null,e=null,T&&!M&&(0==q(u,1,!0)&&0==function(e,t){var n=N._malloc(20),r=null;null!==t.PanoARCustomParams&&(r=N._malloc(12),N.HEAPF32[r/4+0]=t.PanoARCustomParams.VerFieldViewAngle,N.HEAPF32[r/4+1]=t.PanoARCustomParams.HoriFieldViewAngle,N.HEAPF32[r/4+2]=t.PanoARCustomParams.DownPressAngle),N.HEAP32[n/4+0]=t.PanoARMode,N.HEAP32[n/4+1]=t.ImageStride,N.HEAP32[n/4+2]=t.ImageWidth,N.HEAP32[n/4+3]=t.ImageHeight,N.HEAP32[n/4+4]=r;var o=N._RENDER_SetAlgoParams(e,n);return N._free(n),null!==r&&N._free(r),o}(u,D)?console.log("[Trace]RENDER_AlgoCommand and RENDER_SetAlgoParams success!"):console.log("[Error]RENDER_AlgoCommand or RENDER_SetAlgoParams failed!"),M=!0),n=u,N._RENDER_Present(n),!0}var n;return!1},L=function e(t){if(!0===f){if(0===s||t-s<R){if(0===s&&(s=t),null!==p){if(!g)null!==(n=p.dequeue())&&null!==n.buffer&&null!==n.buffer.dataY&&(g=!0,H(n));window.requestAnimationFrame(e)}return}if(l)return null!==(n=p.dequeue())&&null!==n.buffer&&null!==n.buffer.Image.DataY&&H(n),void window.requestAnimationFrame(e);0===c&&(c=t);var n,r=t-c;if(r>m)null!==(n=p.dequeue())&&null!==n.buffer&&null!==n.buffer.Image.DataY&&(h&&(d=r-m),m=n.buffer.nCostTime,m-=d,H(n),c=t,h=!0);window.requestAnimationFrame(e)}};return k.prototype={draw:function(e,t,n,r,o){if(!0===f){if(E)return(a={}).Image={ImageFormat:0,DataY:e,DataU:t,DataV:n,width0:v,width1:v/2,width2:v/2,height0:_,height1:_/2,height2:_/2},a.nFrameID=r,void H(new ue(a));if(null!==p)if(document.hidden&&p.size>=25)p.clear();else{var a;(a={}).Image={ImageFormat:0,DataY:e,DataU:t,DataV:n,width0:v,width1:v/2,width2:v/2,height0:_,height1:_/2,height2:_/2},a.nFrameID=r,0==o&&(o=25);var i=1e3/o,u=p.size*i;l||(S=u>U?1.2:u<I?.8:1),a.nCostTime=1e3/o/S,a.nCostTime<20&&(a.nCostTime=20),p.enqueue(a)}}},drawWebCodecs:function(e,t,n,r,o){if(!0===f){if(E)return(a={}).Image={ImageFormat:9,DataY:e,DataU:0,DataV:0,width0:r,width1:0,width2:0,height0:o,height1:0,height2:0},a.nFrameID=t,void H(new ue(a));if(null!==p)if(document.hidden&&p.size>=25)p.clear();else{var a;(a={}).Image={ImageFormat:9,DataY:e,DataU:0,DataV:0,width0:r,width1:0,width2:0,height0:o,height1:0,height2:0},a.nFrameID=t,0==n&&(n=25);var i=1e3/n,u=p.size*i;l||(S=u>U?1.2:u<I?.8:1),a.nCostTime=1e3/n/S,a.nCostTime<20&&(a.nCostTime=20),p.enqueue(a)}}},resize:function(e,t){this.stopRendering(),null!==p&&(p.clear(),p=null),null!==u&&(z(u),u=null),i&&(u=function(e){var t=(N=window.REModule)._malloc(12),n=N.allocateUTF8(e);N.HEAP32[t/4+0]=10,N.HEAP32[t/4+1]=n,N.HEAP32[t/4+2]=0;var r=N._RENDER_Create(t);return N._free(n),N._free(t),r}(i.id)),p=new G(a),this.startRendering(),v=e,_=t},initStartTime:function(){0===s&&this.startRendering()},startRendering:function(){0===s&&(f=!0,window.requestAnimationFrame(L))},pause:function(){f=!1},play:function(){f=!0},stopRendering:function(){f=!1,s=0},setPlaySpeed:function(e){S=e},setEncodeType:function(e){b=e},setBeginDrawCallback:function(e){C=e},terminate:function(){f=!1,s=0,w=!1,null!==p&&(p.clear(),p=null),u&&z(u),u=null,A=-1},getVideoBufferQueueSize:function(){return p.size},OpenIVS:function(e,t){(function(e){(Y=window.IVSModule)._DRAW_Open(e)})(P=e),function(e,t){var n=Y.allocateUTF8(t);Y._DRAW_SetWebCanvas(e,n),Y._free(n)}(P,t),y=!0},CloseIVS:function(){var e;y&&(y=!1,e=P,Y._DRAW_Clean(e),function(e){Y._DRAW_Close(e)}(P))},SetIvsEnable:function(e,t){y&&function(e,t,n){Y._DRAW_SetEnable(e,t,n)}(P,e,t)},CleanScreen:function(e,t,n,r){null!=u&&(p.clear(),function(e,t,n,r,o){var a=N._malloc(16);N.HEAP32[a/4+0]=t,N.HEAP32[a/4+1]=n,N.HEAP32[a/4+2]=r,N.HEAP32[a/4+3]=o;var i=N._RENDER_ClearScreen(e,a);N._free(a)}(u,e,t,n,r))},DrawIVS:function(e,t,n,r){if(y){var o=function(e){var t=0,n=0;switch(e){case J:t=te,n=1;break;case Z:t=ne,n=1}return{bDeal:n,nIvsDrawType:t}}(t),a=o.bDeal,i=o.nIvsDrawType;if(a)!function(e,t,n,r,o){var a=Y._malloc(r);Y.writeArrayToMemory(n,a);var i=Y._DRAW_InputIVSData(e,t,a,r,o);Y._free(a)}(P,i,e,n,r);else switch(t){case j:!function(e,t,n,r){var o=Y._malloc(n);Y.writeArrayToMemory(t,o);var a=Y._DRAW_InputJsonData(e,o,n,r);Y._free(o)}(P,e,n,r);break;case K:!function(e,t,n,r,o){var a=Y._malloc(r);Y.writeArrayToMemory(n,a);var i=Y._DRAW_InputTrackData(e,t,a,r,o);Y._free(a)}(P,0,e,n,r);break;case X:case Q:case $:case ee:!function(e,t,n,r,o){var a=!1,i=Y._malloc(r);if(ee==t){Y.HEAP32[i/4+0]=n.nId,Y.HEAP16[i/2+2]=n.wCustom,Y.HEAPU8[i+6]=n.chState,Y.HEAPU8[i+7]=n.chCount;for(var l=Y._malloc(12*n.chCount),u=l,s=new Array(n.chCount),c=new Array(n.chCount),f=new Array(n.chCount),p=0;p<n.chCount;p++){if(Y.HEAP32[u/4+0]=n.pElement[p].nStructType,Y.HEAP32[u/4+1]=n.pElement[p].nStructLength,f[p]=Y._malloc(n.pElement[p].nStructLength),re==n.pElement[p].nStructType)Y.HEAPU8[f[p]+0]=n.pElement[p].pStruct.chType,Y.HEAPU8[f[p]+1]=n.pElement[p].pStruct.chWidth,Y.HEAPU8[f[p]+2]=n.pElement[p].pStruct.chStyle,Y.HEAP16[f[p]/2+2]=n.pElement[p].pStruct.wRadius,Y.HEAP16[f[p]/2+4]=n.pElement[p].pStruct.positionCircle.x,Y.HEAP16[f[p]/2+5]=n.pElement[p].pStruct.positionCircle.y,Y.HEAPU8[f[p]+12]=n.pElement[p].pStruct.chLineA,Y.HEAPU8[f[p]+13]=n.pElement[p].pStruct.chLineR,Y.HEAPU8[f[p]+14]=n.pElement[p].pStruct.chLineG,Y.HEAPU8[f[p]+15]=n.pElement[p].pStruct.chLineB,Y.HEAPU8[f[p]+16]=n.pElement[p].pStruct.chRegA,Y.HEAPU8[f[p]+17]=n.pElement[p].pStruct.chRegR,Y.HEAPU8[f[p]+18]=n.pElement[p].pStruct.chRegG,Y.HEAPU8[f[p]+19]=n.pElement[p].pStruct.chRegB;else if(oe==n.pElement[p].nStructType){if(Y.HEAPU8[f[p]+0]=n.pElement[p].pStruct.chType,Y.HEAPU8[f[p]+1]=n.pElement[p].pStruct.chCount,Y.HEAPU8[f[p]+2]=n.pElement[p].pStruct.chWidth,Y.HEAPU8[f[p]+3]=n.pElement[p].pStruct.chStyle,Y.HEAPU8[f[p]+4]=n.pElement[p].pStruct.chLineA,Y.HEAPU8[f[p]+5]=n.pElement[p].pStruct.chLineR,Y.HEAPU8[f[p]+6]=n.pElement[p].pStruct.chLineG,Y.HEAPU8[f[p]+7]=n.pElement[p].pStruct.chLineB,n.pElement[p].pStruct.chCount>0){s[p]=Y._malloc(4*n.pElement[p].pStruct.chCount);for(var d=0;d<n.pElement[p].pStruct.chCount;d++)Y.HEAPU16[s[p]/2+2*d]=n.pElement[p].pStruct.pPoints[d].x,Y.HEAPU16[s[p]/2+2*d+1]=n.pElement[p].pStruct.pPoints[d].y;Y.HEAPU32[f[p]/4+2]=s[p]}}else if(ae==n.pElement[p].nStructType){if(Y.HEAPU8[f[p]+0]=n.pElement[p].pStruct.chType,Y.HEAPU8[f[p]+1]=n.pElement[p].pStruct.chCount,Y.HEAPU8[f[p]+2]=n.pElement[p].pStruct.chWidth,Y.HEAPU8[f[p]+3]=n.pElement[p].pStruct.chStyle,Y.HEAPU8[f[p]+4]=n.pElement[p].pStruct.chLineA,Y.HEAPU8[f[p]+5]=n.pElement[p].pStruct.chLineR,Y.HEAPU8[f[p]+6]=n.pElement[p].pStruct.chLineG,Y.HEAPU8[f[p]+7]=n.pElement[p].pStruct.chLineB,Y.HEAPU8[f[p]+8]=n.pElement[p].pStruct.chRegA,Y.HEAPU8[f[p]+9]=n.pElement[p].pStruct.chRegR,Y.HEAPU8[f[p]+10]=n.pElement[p].pStruct.chRegG,Y.HEAPU8[f[p]+11]=n.pElement[p].pStruct.chRegB,n.pElement[p].pStruct.chCount>0){s[p]=Y._malloc(4*n.pElement[p].pStruct.chCount);for(d=0;d<n.pElement[p].pStruct.chCount;d++)Y.HEAPU16[s[p]/2+2*d]=n.pElement[p].pStruct.pPoints[d].x,Y.HEAPU16[s[p]/2+2*d+1]=n.pElement[p].pStruct.pPoints[d].y;Y.HEAPU32[f[p]/4+3]=s[p]}}else ie==n.pElement[p].nStructType&&(Y.HEAPU8[f[p]+0]=n.pElement[p].pStruct.chType,Y.HEAPU8[f[p]+1]=n.pElement[p].pStruct.chCharset,Y.HEAPU16[f[p]/2+2]=n.pElement[p].pStruct.stringPos.x,Y.HEAPU16[f[p]/2+3]=n.pElement[p].pStruct.stringPos.y,Y.HEAPU8[f[p]+8]=n.pElement[p].pStruct.chLineA,Y.HEAPU8[f[p]+9]=n.pElement[p].pStruct.chLineR,Y.HEAPU8[f[p]+10]=n.pElement[p].pStruct.chLineG,Y.HEAPU8[f[p]+11]=n.pElement[p].pStruct.chLineB,Y.HEAPU8[f[p]+12]=n.pElement[p].pStruct.chFontSize,Y.HEAPU8[f[p]+13]=n.pElement[p].pStruct.chFontAlign,Y.HEAPU16[f[p]/2+6]=n.pElement[p].pStruct.wTxtLen,c[p]=Y._malloc(n.pElement[p].pStruct.wTxtLen),Y.writeArrayToMemory(n.pElement[p].pStruct.stringDataArray,c[p]),Y.HEAPU32[f[p]/4+4]=c[p]);Y.HEAP32[u/4+2]=f[p],u+=12}if(Y.HEAP32[i/4+2]=l,Y.HEAP16[i/2+6]=n.wInfoLen,n.wInfoLen>0){var m=Y._malloc(n.wInfoLen);Y.writeArrayToMemory(n.pInfo,m),Y.HEAP32[i/4+4]=m}a=Y._DRAW_InputTrackDataEx2(e,t,i,r,o);for(p=0;p<n.chCount;p++)re==n.pElement[p].nStructType||(oe==n.pElement[p].nStructType||ae==n.pElement[p].nStructType?n.pElement[p].pStruct.chCount>0&&Y._free(s[p]):ie==n.pElement[p].nStructType&&Y._free(c[p])),Y._free(f[p]);Y._free(l),n.wInfoLen>0&&Y._free(InfoPtr)}else Y.writeArrayToMemory(n,i),a=Y._DRAW_InputTrackDataEx2(e,t,i,r,o);Y._free(i)}(P,t,e,n,r)}}},SetLifeCount:function(e){y&&function(e,t){Y._DRAW_SetLifeCount(e,2,t)}(P,e)},DrawDrawIVS:function(e){y&&le(P,e)},SetPanoVRMode:function(e,t,n,r){return 0===e?q(u,1,!1):(T=!0,M=!1,D={PanoARMode:e,ImageStride:n,ImageWidth:n,ImageHeight:r,PanoARCustomParams:t}),!0},GetModelRotate:function(){return function(e){var t=N._malloc(4),n=N._malloc(4),r=N._malloc(4);N._RENDER_3DGetModelRotate(e,t,n,r);var o=N.HEAPF32[t/4],a=N.HEAPF32[n/4],i=N.HEAPF32[r/4];return N._free(t),N._free(n),N._free(r),{x:o,y:a,z:i}}(u)},SetModelRotate:function(e,t,n){return function(e,t,n,r){return N._RENDER_3DSetModelRotate(e,t,n,r)}(u,e,t,n)},SetStereoPerspectiveFovy:function(e){return function(e,t){return N._RENDER_3DSetPerspectiveFovy(e,t)}(u,e)},GetVRCoord2DTrans:function(e,t){return function(e,t,n){var r=N._malloc(4),o=N._malloc(4);N._RENDER_GetPanoARCoord2DTrans(e,t,n,r,o);var a=N.HEAPF32[r/4],i=N.HEAPF32[o/4];return N._free(r),N._free(o),{x:a,y:i}}(u,e,t)},GetVRCoord3DTrans:function(e,t){return function(e,t,n){var r=N._malloc(4),o=N._malloc(4);N._RENDER_GetPanoARCoord3DTrans(e,t,n,r,o);var a=N.HEAP32[r/4],i=N.HEAP32[o/4];return N._free(r),N._free(o),{x:a,y:i}}(u,e,t)},ResetPlayState:function(){w=!1},GetCurrentFrameID:function(){return A},SetPrintLogLevel:function(e){E||function(e){null==N&&null!=window.REModule&&(N=window.REModule),N._RENDER_SetPrintLogLevel(e)}(e),function(e){null==Y&&null!=window.IVSModule&&(Y=window.IVSModule),Y._DRAW_SetPrintLogLevel(e)}(e)},SetPlayMethod:function(e,t,n){R=e,I=t,U=n}},new k};var ce=function(){var e=null,t=null,n=null,r=0,o=0,a=!1,i=0,l=0,u=null,s=!1,c=new Float32Array(8e4),f=0,p=null,d=!1,m=0,g=0,h=null;function S(t,r){if(d){var a=r-i;(a>200||a<0)&&(o=0,f=0,s=!0,null!==p&&p.stop()),o-e.currentTime<0&&(o=0),i=r;for(var l=new Int16Array(t.buffer,t.byteOffset,t.byteLength/Int16Array.BYTES_PER_ELEMENT),g=new Float32Array(l.length),S=0;S<l.length;S++)g[S]=l[S]/Math.pow(2,15);if(h&&h.InputPlayData(g),c=function(e,t,n){var r=e;n+t.length>=r.length&&(r=new Float32Array(r.length+8e4)).set(r,0);return r.set(t,n),r}(c,g,f),f+=g.length,!s){var y=0;if(f/g.length>1&&(null!==u&&(y=u*m),y>=f||null===u))return void(f=0);var v=null;(v=e.createBuffer(1,f-y,m)).getChannelData(0).set(c.subarray(y,f)),f=0,p=null,(p=e.createBufferSource()).buffer=v,p.connect(n),o||(o=e.currentTime+.01),p.start(o),o+=v.duration}}else o=0}function y(){}return y.prototype={audioInit:function(r){if(o=0,null!==e);else try{return window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(e=new AudioContext).onstatechange=function(){e&&"running"===e.state&&(a=!0)},t=e.createGain(),(n=e.createBiquadFilter()).connect(t),n.type="lowpass",n.frequency.value=1500,n.gain.value=40,t.connect(e.destination),this.setVolume(r),!0}catch(e){return!1}},play:function(){this.setVolume(r)},stop:function(){r=0,t.gain.value=0,o=0,e=null},bufferAudio:function(t,n){var i=o-e.currentTime;(h&&i>.25||i>10)&&(this.terminate(),this.audioInit(r)),a&&S(t,0)},setSoundState:function(e){d=e},setVolume:function(e){r=e;var n=e/1;n<=0?(t.gain.value=0,o=0):t.gain.value=n>=1?1:n},getVolume:function(){return r},Mute:function(e){if(e)t.gain.value=0,o=0;else{var n=r/1;n<=0?(t.gain.value=0,o=0):t.gain.value=n>=1?1:n}},terminate:function(){"closed"!==e.state&&(f=0,o=0,a=!1,p=null,n=null,t=null,e.close(),e=null)},setBufferingFlag:function(e,t){"init"===t?l=e:s&&(0===e||null==e?u=null:(u=e-l,l=0),s=!1)},getBufferingFlag:function(){return s},setInitVideoTimeStamp:function(e){l=e},getInitVideoTimeStamp:function(){return l},setSampleBits:function(e,t){m=e,g=t,h&&h.SetPlayParam(m,g)},getAudioBufTime:function(){if(void 0!==e){var t=o-e.currentTime;return t>0?t*=1e3:t=0,t}return 0},setAudioProcesser:function(e){(h=e)&&h.SetPlayParam(m,g)}},new y};var fe=function(e){e=e;var t=null,n="",r=null,o=null,a=null,i=null,l=null,u=null,s=null,c=null,f=1,p={timestamp:0,timestamp_usec:0,timezone:0},d={timestamp:0,timestamp_usec:0,timezone:0},m=null,g=!1,h=null,S=null,y=null,v=!1,_=!0,P=0,E=!1,A=[],b=.5,w=null,C=null,T=null,M=0,D=0,R=!1,I=null,U="png",k=1,H=O(),L=null,F=0,B=0,G=0,x=null,V=!1,N=!1,W=[],z={},Y=25,q=.5,j=!1,K=!1;function X(){}function J(){Q(),K=!0}function Z(){var e=0;if(null!==h)for(e=0;e<h.length;e++)T.updating||T.removeEventListener(h[e].type,h[e].function);if(null!==y)for(e=0;e<y.length;e++)C.removeEventListener(y[e].type,y[e].function);if(null!==S)for(e=0;e<S.length;e++)w.removeEventListener(S[e].type,S[e].function)}function Q(){if(null===C||"ended"===C.readyState)return function(e){(y=[]).push({type:"sourceopen",function:J}),y.push({type:"error",function:ae});for(var t=0;t<y.length;t++)e.addEventListener(y[t].type,y[t].function)}(C=new MediaSource),w.src=window.URL.createObjectURL(C),e.log("videoMediaSource::appendInitSegment new MediaSource()"),!1;if(e.log("videoMediaSource::appendInitSegment start"),0===C.sourceBuffers.length){C.duration=0;var r='video/mp4; codecs="'+n+'"';if(0==n.length)return!1;if(!MediaSource.isTypeSupported(r))return e.log("not support"+r),x&&x({errorCode:101}),s&&s("InitError"),!1;!function(e){(h=[]).push({type:"error",function:ie}),h.push({type:"updateend",function:re}),h.push({type:"update",function:oe});for(var t=0;t<h.length;t++)e.updating||e.addEventListener(h[t].type,h[t].function)}(T=C.addSourceBuffer(r))}var o=t();return null===o?(C.endOfStream("network"),!1):(T.updating?A.push(o):T.appendBuffer(o),e.log("videoMediaSource::appendInitSegment end, codecInfo = "+n),!0)}function $(){w.paused&&(a(),v||V||w.play())}function ee(){w.paused||_||(e.log("pause"),w.pause())}function te(){W.length&&function(t){if(!j&&K&&(j=Q()),null!==T&&"closed"!==C.readyState&&"ended"!==C.readyState)try{if(A.length>0)return e.log("segmentWaitDecode.length: "+A.length),void A.push(t);T.updating?(e.log("updating.........."),A.push(t)):(T.appendBuffer(t),V&&(z.buffer=t))}catch(t){e.log("videoMediaSource::appendNextMediaSegment error >> initVideo"),A.length=0,x&&x({errorCode:102})}}(W.shift())}function ne(){if(null!==C)try{if(T&&T.buffered.length>0){if(function(){var e=1*T.buffered.start(T.buffered.length-1),t=1*T.buffered.end(T.buffered.length-1);t-e>60&&T.remove(e,t-10)}(),N&&!V||w.duration>q&&(w.currentTime=(w.duration-q).toFixed(3),q+=Y<10?.5:.1),w&&w.duration-w.currentTime>8){if(x&&x({errorCode:103}),T.updating)return;var t=w.duration-.1;w.currentTime=t.toFixed(3)}if(E&&!g){var n=1*T.buffered.start(T.buffered.length-1),r=1*T.buffered.end(T.buffered.length-1);if((0===w.currentTime?r-n:r-w.currentTime)>=q+.2){if(e.log("跳秒"),T.updating)return;var o=r-.1;w.currentTime=o.toFixed(3)}}}}catch(t){e.error("sourceBuffer has been removed")}}function re(){A.length>0&&T&&!T.updating&&(T.appendBuffer(A[0]),A.shift())}function oe(){}function ae(){console.log("videoMediaSource::onSourceError"),s&&s("SourceError")}function ie(){console.log("videoMediaSource::onSourceBufferErrormsg"),s&&s("SourceBufferError")}function le(){console.log("videoMediaSource::onError"),ee(),x&&x({errorCode:104}),s&&s("Error")}function ue(){if(v=!0,_=!1,N=!0,!R){R=!0;var e={decodeMode:"video"};e.width=w.videoWidth,e.height=w.videoHeight,1==r?e.encodeMode="H264":2==r&&(e.encodeMode="H265"),l&&l(e)}}function se(){v=!1,_=!0,e.log("硬解码暂停播放")}function ce(){var e=parseInt(C.duration,10),t=parseInt(w.currentTime,10),n={timestamp:p.timestamp-f*(e-t+(1!==f?1:0)),timestamp_usec:0,timezone:p.timezone};0===t||isNaN(e)||!g&&Math.abs(e-t)>4&&1===f||w.paused||(null===m?(m=n,i(0,"currentTime")):(m.timestamp<=n.timestamp&&f>=1||m.timestamp>n.timestamp&&f<1)&&(m=n,++P>4&&i(n.timestamp,"currentTime")))}function fe(){null!=T&&($(),ne())}function pe(){a()}function de(){$()}function me(){if(e.log("需要缓冲下一帧"),E=!1,0==D)M=Date.now(),D++;else{D++;var t=Date.now()-M;e.log("diffTime: "+t+"  Count: "+D),D>=5&&t<6e4&&b<=1&&(b+=.1,D=0,M=0,e.log("delay + 0.1 = "+b))}}function ge(){e.log("Can play !")}function he(){e.log("Can play without waiting"),E=!0}function Se(){e.log("loadedmetadata")}function ye(e,t){var n=document.createElement("canvas");n.width=w.videoWidth,n.height=w.videoHeight;var r=n.getContext("2d");r.drawImage(w,0,0,n.width,n.height);for(var o=0;o<e.length;o++)e[o]&&r.drawImage(e[o],0,0,n.width,n.height);for(var a=n.toDataURL(),i=atob(a.substring("data:image/jpg;base64,".length)),l=new Uint8Array(i.length),s=0,c=i.length;s<c;++s)l[s]=i.charCodeAt(s);u&&u(l);var f=new Blob([l.buffer],{type:"image/jpg"});Ie(f,t),f=null}X.prototype={init:function(t){c=O(),e.log("videoMediaSource::init browserType = "+c),(w=t).autoplay="safari"!==c,w.controls=!1,w.preload="auto",function(e){(S=[]).push({type:"durationchange",function:fe}),S.push({type:"playing",function:ue}),S.push({type:"error",function:le}),S.push({type:"pause",function:se}),S.push({type:"timeupdate",function:ce}),S.push({type:"resize",function:pe}),S.push({type:"seeked",function:de}),S.push({type:"waiting",function:me}),S.push({type:"canplaythrough",function:he}),S.push({type:"canplay",function:ge}),S.push({type:"loadedmetadata",function:Se});for(var t=0;t<S.length;t++)e.addEventListener(S[t].type,S[t].function)}(w),Q()},setInitSegmentFunc:function(e){t=e},getVideoElement:function(){return w},setCodecInfo:function(e){n=e},setMediaSegment:function(e){W.push(e),V||te()},capturePic:function(e,t){I&&clearInterval(I),E||"edge"===H?ye(e,t):I=setInterval((function(){E&&(ye(e,t),clearInterval(I))}),200)},getCapture:function(e,t,n){I&&clearInterval(I),k=n||1,U="png","jpg"!==t&&"jpeg"!==t||(U="jpeg");var r=document.createElement("canvas"),o=null;return r.width=w.videoWidth,r.height=w.videoHeight,(E||"edge"===H||E)&&(r.getContext("2d").drawImage(w,0,0,r.width,r.height),o=r.toDataURL("image/"+U,k)),o},setInitSegment:function(){Q()},ResetInitSegmentFlag:function(){j=!1},setTimeStamp:function(e,t){o=e},setVideoSizeCallback:function(e){a=e},setAudioStartCallback:function(e){i=e},setMseErrorCallback:function(e){s=e},getPlaybackTimeStamp:function(){return o},setPlaySpeed:function(e){f!=e&&(f=e,w.playbackRate=e)},setvideoTimeStamp:function(e){var t=Math.abs(p.timestamp-e.timestamp)>3;d.timestamp,!0===t&&(P=0,i((d=e).timestamp,"init"),0!==p.timestamp&&g&&(w.currentTime=C.duration-.1),m=null),p=e},pause:function(){V=!0,ee()},play:function(){V=!1},setPlaybackFlag:function(e){g=e},setTimeStampInit:function(){m=null,d={timestamp:0,timestamp_usec:0,timezone:0}},close:function(){Z(),ee()},setBeginDrawCallback:function(e){l=e},setCapturePicDataCallBack:function(e){u=e},setErrorCallback:function(e){x=e},terminate:function(){null!==w&&(Z(),"open"===C.readyState&&(T&&C.removeSourceBuffer(T),C.endOfStream()),T=null,C=null,w=null,I&&(clearInterval(I),I=null),L&&(clearInterval(L),L=null),G=0,B=0,F=0,j=!1,K=!1,R=!1)},getDuration:function(){return w.duration-w.currentTime},setFPS:function(e){e&&(Y=e)},setRtspOver:function(){w.duration.toFixed(4)-0==w.currentTime.toFixed(4)-0||(F=parseInt(w.currentTime),B=parseInt(w.duration),L=setInterval((function(){F===parseInt(w.currentTime)&&B===parseInt(w.duration)?G++>10&&(L&&clearInterval(L),L=null):parseInt(w.currentTime)>=parseInt(w.duration)?(L&&clearInterval(L),L=null):(F=parseInt(w.currentTime),B=parseInt(w.duration),G=0)}),150))},getVideoBufferQueueSize:function(){return W.length},playNextFrame:function(){te()},getCurFrameInfo:function(){var e;return z.src=((e=document.createElement("canvas")).width=w.videoWidth,e.height=w.videoHeight,e.getContext("2d").drawImage(w,0,0,e.width,e.height),e.toDataURL()),z},setDecodeType:function(e){r=e},ResetPlayState:function(){R=!1}};var ve,_e,Pe,Ee,Ae,be,we,Ce,Te,Me,De,Re,Ie=(ve=window,_e=ve.document,Pe=function(){return ve.URL||ve.webkitURL||ve},Ee=_e.createElementNS("http://www.w3.org/1999/xhtml","a"),Ae="download"in Ee,be=/constructor/i.test(ve.HTMLElement),we=/CriOS\/[\d]+/.test(navigator.userAgent),Ce=function(e){(ve.setImmediate||ve.setTimeout)((function(){throw e}),0)},Te=function(e){setTimeout((function(){"string"==typeof e?Pe().revokeObjectURL(e):e.remove()}),4e4)},Me=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},Re=(De=function(e,t,n){n||(e=Me(e));var r,o=this,a="application/octet-stream"===e.type,i=function(){!function(e,t,n){for(var r=(t=[].concat(t)).length;r--;){var o=e["on"+t[r]];if("function"==typeof o)try{o.call(e,n||e)}catch(e){Ce(e)}}}(o,"writestart progress write writeend".split(" "))};if(o.readyState=o.INIT,Ae)return r=Pe().createObjectURL(e),void setTimeout((function(){var e,n;Ee.href=r,Ee.download=t,e=Ee,n=new MouseEvent("click"),e.dispatchEvent(n),i(),Te(r),o.readyState=o.DONE}));!function(){if((we||a&&be)&&ve.FileReader){var t=new FileReader;return t.onloadend=function(){var e=we?t.result:t.result.replace(/^data:[^;]*;/,"data:attachment/file;");ve.open(e,"_blank")||(ve.location.href=e),e=void 0,o.readyState=o.DONE,i()},t.readAsDataURL(e),void(o.readyState=o.INIT)}r||(r=Pe().createObjectURL(e)),a?ve.location.href=r:ve.open(r,"_blank")||(ve.location.href=r),o.readyState=o.DONE,i(),Te(r)}()}).prototype,"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=Me(e)),navigator.msSaveOrOpenBlob(e,t)}:(Re.readyState=Re.INIT=0,Re.WRITING=1,Re.DONE=2,Re.error=Re.onwritestart=Re.onprogress=Re.onwrite=Re.onabort=Re.onerror=Re.onwriteend=null,function(e,t,n){return null==t||null==t?null:new De(e,t||e.name||"download",n)}));return new X};function pe(e){return(pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function de(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function me(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?de(Object(n),!0).forEach((function(t){ge(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):de(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ge(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=pe(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=pe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pe(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function he(e,t){return t=(t=t.toLowerCase())[0].toUpperCase()+t.substr(1),Object.prototype.toString.call(e)==="[object "+t+"]"}function Se(e,t,n){if(void 0===n&&(n=2),void 0===t&&(t=0),(e=e.toString()).length>=n)return e;var r=n-e.length;return new Array(r).fill(String(t)).join("")+e}function ye(e,t){return void 0!==e&&e?(t=t||new Date,e=(e=(e=(e=(e=(e=e.replace(/y/gi,Se(t.getFullYear()),0)).replace(/m/gi,Se(t.getMonth()+1),0)).replace(/d/gi,Se(t.getDate()),0)).replace(/h/gi,Se(t.getHours()),0)).replace(/i/gi,Se(t.getMinutes()),0)).replace(/s/gi,Se(t.getSeconds()),0)):""}function ve(e,t){var n=(e=e||{}).nameFormat||["ymd_his"];t=t||new Date;var r="";if(he(n,"string"))n=[n,{}];else{if(!he(n,"array"))return void function(e){throw new Error(e)}("name format must be string or array");he(n[0],"string")||(n[0]="ymd_his"),he(n[1],"object")||(n[1]={})}var o=n[0].split(/\{(?:[^{}]+)\}/),a=n[1];n[0].replace(/\{([^{}]*)\}/g,(function(e,t,n){o.shift();r+=ye(),r+=t in a?a[t]:e}));var i=o.shift();return r+=ye(i,t)}function _e(e,t){this.name=e,this.allowUpDateName=!0,this.byteLength=0,this.options=t,this.startTime=(new Date).toLocaleString()}_e.prototype.setEndTime=function(){this.endTime=(new Date).toLocaleString()},_e.prototype.updateNameByStream=function(e,t){if(this.allowUpDateName){var n=new Uint8Array(t),r=(n[19]<<24)+(n[18]<<16)+(n[17]<<8)+n[16]>>>0,o="20"+(r>>26)+"/"+(r>>22&15)+"/"+(r>>17&31)+" "+(r>>12&31)+":"+(r>>6&63)+":"+(63&r);this.name=ve(e,new Date(o)),this.allowUpDateName=!1,n=null}t=null};var Pe=new function(){var e={count:0,total:0,group:[]},t=function(){};return t.prototype.add=function(t){e.count++,e.total+=t.byteLength,e.group.push(t)},t.prototype.get=function(t){return t in e?e[t]:e},new t};var Ee,Ae=function(){var e=1048576,t=null,n=null,r=0,o=void 0,a=null,i=0,l=null,u=!1;function s(){this.onMessage=function(){},this.postMessage=function(e){this.__onMessage(e)},this.__postMessage=function(e){this.onMessage(e)}}return s.prototype.__onMessage=function(e){var t=e;switch(t.type){case"init":this.init(t.options);break;case"addBuffer":this.addBuffer(t);break;case"close":this.close()}},s.prototype.init=function(t){this.fullSize=t.fullSize||1/0,this.singleSize=t.singleSize+20*e||520*e,o="init",u=!1,l=t.recordName,this.limitOptions=Object.assign({limitBy:"fullSize"},t.limitOptions),this.nameOptions=Object.assign({namedBy:"date",nameFormat:["ymd_his",{}]},t.nameOptions)},s.prototype._malloc=function(e){t&&n&&(n=null,t=null),t=new ArrayBuffer(e),n=new DataView(t);var r=this.nameOptions,o="";if(null!=l)o=l;else switch(this.nameOptions.namedBy.toLowerCase()){case"date":o=ve(r);break;default:o=ve()}a=new _e(o)},s.prototype._initVideoMem=function(){!t&&this.singleSize&&this._malloc(this.singleSize)},s.prototype.appendVideoBuf=function(t,o,a){var l=t.byteLength,s=0;if(5==i){s=o+l;for(var c=o;c<s;c++)n.setUint8(c,t[c-o]);s>r&&(r=s)}else{s=r+l;for(c=r;c<s;c++)n.setUint8(c,t[c-r]);r=s}this.__postMessage({type:"pendding",size:r,total:this.singleSize}),s>this.singleSize-20*e&&!u&&(u=!0,this.__postMessage({type:"close"}))},s.prototype.addBuffer=function(e){if("closed"!==o){var t=e.buffer,n=e.offset;i=e.recordType,this._initVideoMem(),o="addBuffer";var a=t.length,l=0;l=5==i?n+a:r+a,Pe.get("total")+l>this.fullSize?this.close():this.appendVideoBuf(t,n)}},s.prototype.inNodePlace=function(){if("addBuffer"===o){o="download",a.updateNameByStream(this.nameOptions,t.slice(0,20)),a.byteLength=r,a.setEndTime(),Pe.add(a);var e=t.slice(0,r);if(this.reset(),this.__postMessage({type:"download",data:me(me({},a),{},{buffer:e})}),e=null,"count"===this.limitOptions.limitBy){var n=this.limitOptions.count;n&&n===Pe.get("count")&&this.close()}}},s.prototype.reset=function(){r=0,this._malloc(this.singleSize)},s.prototype.close=function(){this.inNodePlace(),"closed"!==o&&void 0!==o&&(o="closed",this.__postMessage({type:"closed",message:"record was closed"}),t=null,n=null)},new s},be=function(e){var t=e,n=[255,511,1023,2047,4095,8191,16383,32767];function r(){}function o(e){var t,r;return e<0?(e=132-e,t=127):(e+=132,t=255),(r=a(e,n))>=8?127^t:(r<<4|e>>r+3&15)^t}function a(e,t){for(var n=0,r=t.length;n<r;n++)if(e<=t[n])return n;return t.length}function i(e){var t,r,o;return e>=0?t=213:(t=85,e=-e-8),(r=a(e,n))>=8?127^t:(o=r<<4,(o|=r<2?e>>4&15:e>>r+3&15)^t)}return r.prototype={setSampleRate:function(e){e},encode:function(e){for(var n=new Int16Array(e.buffer),r=new Uint8Array(n.length),a=0;a<n.length;a++)r[a]=0==t?i(n[a]):o(n[a]);return r}},new r},we=function(e,t){var n=t,r=null,o=null,a=[36,e,0,0,0,0],i=[68,72,65,86],l=[100,104,97,118],u=245,s=0,c=null;function f(e,t,n){var r=[],o=t||4;if(!0===n)for(var a=0;a<o;a++)r[a]=e>>>8*(o-1-a)&255;else for(var i=0;i<o;i++)r[i]=e>>>8*i&255;return r}function p(){o=new be(n)}return p.prototype={setSampleRate:function(e){o.setSampleRate(e)},getRTPPacket:function(t){var p=o.encode(t),d=0;(r=new Uint8Array(a.length+40+p.length+8)).set([36,e],d),d+=2,r.set(f(40+p.length+8,4,!0),d),d+=4,r.set(i,d),d+=4,r.set([240],d),d+=1,r.set([0],d),d+=1,r.set([1],d),d+=1,r.set([0],d),d+=1,u>65535&&(u=240),r.set(f(u),d),d+=4,u++;var m=f(40+p.length+8);r.set(m,d),d+=4;var g=new Date,h=(g.getFullYear()-2e3<<26)+(g.getMonth()+1<<22)+(g.getDate()<<17)+(g.getHours()<<12)+(g.getMinutes()<<6)+g.getSeconds(),S=g.getTime(),y=null===c?0:S-c;c=S,(s+=y)>65535&&(s=65535-s),r.set(f(h),d),d+=4,r.set(f(s,2),d),d+=2,r.set([16],d),d+=1;var v=function(e,t){for(var n=0,r=t;r<e.length;r++)n+=e[r];return n}(r,6);r.set([v],d),d+=1;var _=[131,1,0==n?14:10,2];r.set(_,d),d+=4;r.set([150,1,0,0],d),d+=4;var P=function(e,t){for(var n=0,r=0;r<t;r++)n+=e[r]<<r%4*8;return n}(p,p.length);return r.set([136],d),d+=1,r.set(f(P),d),d+=4,r.set([0,0,0],d),d+=3,r.set(p,d),d+=p.length,r.set(l,d),d+=4,r.set(m,d),r},terminate:function(){o=null}},new p(e)};function Ce(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],u=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,o=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Te(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Te(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var Me=function(e){var t=e,n=null,r=null,o=null,a=8e3,i=16,l=0,u=0,s=null,c=null,f=null,p={};p.pBuffer=new Uint8Array(320),p.nByteOffset=0;var d={};d.pBuffer=new Uint8Array(320),d.nByteOffset=0;var m=null,g=null,h=null,S=null,y=null,v=!1,_=!1;function P(){f=new G(20),c=new G(20)}function E(e){var t=new ArrayBuffer(2*e.length);return function(e,t,n){for(var r=0;r<n.length;r++,t+=2){var o=Math.max(-1,Math.min(1,n[r])),a=o<0?32768*o:32767*o;e.setInt16(t,a,!0)}}(new DataView(t),0,e),t}function A(e,t,n){var r=Ce(function(e,t,n){var r=null;if(n===t)return[e,r];if(n>t)return console.error("The dstRate show be smaller than srcRate"),[e,r];for(var o=t/n,a=Math.floor(e.length/o),i=new Float32Array(a),l=0,u=0;l<i.length;){for(var s=Math.round((l+1)*o),c=0,f=0,p=u,d=e.length;p<s&&p<d;p++)c+=e[p],f++;i[l]=c/f,l++,u=s}if(Math.round(l*o)!==e.length){var m=Math.round(l*o);r=new Float32Array(e.subarray(m,e.length))}return[i,r]}(e,t,n),2);return[r[0],r[1]]}return P.prototype={SetPlayParam:function(e,t){0!=e&&0!=t&&(l=e,u=t,n&&this.StopProcess(),this.Init(),this.Config(a,i,l,u),this.StartProcess())},SetCaptureParam:function(e,t){a=e,i=t},Init:function(){var e;return e=(Ee=window.ASPLiteModule)._malloc(4),0!=Ee._Audio_Framework_Init(e)&&(Ee._free(e),e=null),null!=(n=e)},Config:function(e,t,a,i){var l=Ce(function(e,t,n,r,o){if(null==e)return[null,null];var a=Ee._malloc(96),i=Ee._malloc(668),l=new Uint8Array(Ee.HEAPU8.buffer),u=0;"ASPLite.cfg".split("").forEach((function(e,t){l[i+u+4>>0]=e.charCodeAt(0),u++}));var s=t;return t>r&&(s=r),Ee.HEAP32[i/4+129]=2,Ee.HEAP32[i/4+130]=s,Ee.HEAP32[i/4+132]=1,Ee.HEAP32[i/4+133]=16,Ee.HEAP32[i/4+138]=1,Ee.HEAP32[i/4+140]=0,Ee.HEAP32[i/4+141]=0,0!=Ee._Audio_Framework_Config(e,i)&&(Ee._free(i),i=null,Ee._free(a),a=null),[i,a]}(n,e,0,a),2);r=l[0],o=l[1]},DeInit:function(){var e,t,a;t=r,a=o,null!=(e=n)&&(Ee._Audio_Framework_DeInit(e,t,a),Ee._free(e),Ee._free(t),Ee._free(a)),n=null,r=null,o=null,v=!1},InputCaptureData:function(e,r){var o=null;null!==m?((o=new Float32Array(e.length+m.length)).set(m,0),o.set(e,m.length)):o=e;var a=Ce(A(o,r,8e3),2),i=a[0],l=a[1];m=l;new Uint8Array(i.buffer);var u=E(i),s=new Uint8Array(u);null!=n&&t?function e(t,n){if(n<=0)return;var r=320-p.nByteOffset,o=n>r?r:n,a=null;a=n>=320&&0==p.nByteOffset?t.slice(0,320):p.nByteOffset>0?t.slice(0,320-p.nByteOffset):t;if(p.pBuffer.set(a,p.nByteOffset),p.nByteOffset+=o,320==p.nByteOffset){var i=new Uint8Array(320);i.set(p.pBuffer),c.enqueue(i),p.pBuffer.fill(0),p.nByteOffset=0}h=t.slice(o),n-=o,e(h,h.length)}(s,s.length):y&&y(s)},InputPlayData:function(e){if(_){var t=null;null!==g?((t=new Float32Array(e.length+g.length)).set(g,0),t.set(e,g.length)):t=e;var n=Ce(A(t,l,8e3),2),r=n[0],o=n[1];g=o;new Uint8Array(r.buffer);var a=E(r),i=new Uint8Array(a);!function e(t,n){if(n<=0)return;var r=320-d.nByteOffset,o=n>r?r:n,a=null;a=n>=320&&0==d.nByteOffset?t.slice(0,320):d.nByteOffset>0?t.slice(0,320-d.nByteOffset):t;if(d.pBuffer.set(a,d.nByteOffset),d.nByteOffset+=o,320==d.nByteOffset){var i=new Uint8Array(320);i.set(d.pBuffer),f.enqueue(i),d.pBuffer.fill(0),d.nByteOffset=0}S=t.slice(o),n-=o,e(S,S.length)}(i,i.length)}else f.clear()},StartProcess:function(){_=!0,s=setInterval((function(){!function(){if(null!==c&&0==c.size)return;if(!v&&f.size>5){v=!0;for(var e=0;e<f.size-1;e++)f.dequeue()}var t={};if(f.size>0)t=f.dequeue();else{if(!(c.size>5))return;t.buffer=new Uint8Array(320)}var r=c.dequeue(),a={};a.data=r.buffer,a.fs=8e3,a.datalen=320,a.depth=16,a.offset=2,a.channels=1,a.buflen=320;var i={};i.data=t.buffer,i.fs=8e3,i.datalen=320,i.depth=16,i.offset=2,i.channels=1,i.buflen=320;var l={},u=new ArrayBuffer(a.datalen),s=new Uint8Array(u);l.data=s,0==function(e,t,n,r,o){if(null==e)return-1;var a=Ee._malloc(232),i=Ee._malloc(116),l=Ee._malloc(t.datalen);Ee.writeArrayToMemory(t.data,l);var u=Ee._malloc(n.datalen);Ee.writeArrayToMemory(n.data,u);var s=Ee._malloc(t.datalen);Ee.HEAP32[a/4+0]=l,Ee.HEAP32[a/4+1]=t.fs,Ee.HEAP32[a/4+2]=t.datalen,Ee.HEAP32[a/4+3]=t.depth,Ee.HEAP32[a/4+4]=t.offset,Ee.HEAP32[a/4+5]=t.channels,Ee.HEAP32[a/4+6]=t.buflen,Ee.HEAP32[a/4+29]=u,Ee.HEAP32[a/4+30]=n.fs,Ee.HEAP32[a/4+31]=n.datalen,Ee.HEAP32[a/4+32]=n.depth,Ee.HEAP32[a/4+33]=n.offset,Ee.HEAP32[a/4+34]=n.channels,Ee.HEAP32[a/4+35]=n.buflen,Ee.HEAP32[i/4+0]=s;var c=Ee._Audio_Framework_Main(e,a,i,o);return 0==c&&r.data.set(Ee.HEAPU8.subarray(s,s+t.datalen)),Ee._free(a),Ee._free(i),Ee._free(l),Ee._free(u),Ee._free(s),c}(n,a,i,l,o)&&y&&y(l.data)}()}),10)},StopProcess:function(){this.DeInit(),s&&(clearInterval(s),s=null),c.clear(),f.clear(),m=null,g=null,_=!1},SetProcessedDataCallback:function(e){y=e}},new P},De=function(e){var t=e,n=null,r=null,o=null,a=null,i=null,l=!1,u=null,s={audio:!0,video:!1},c=null;function f(){}function p(e){var t=n.getRTPPacket(e);c(t,5)}return f.prototype={initAudioOut:function(e){if(null==r)try{window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(r=new AudioContext).onstatechange=function(){}}catch(e){return}if(t.SetProcessedDataCallback(p),null!==o&&null!==a||(o=r.createGain(),(a=r.createScriptProcessor(1024,1,1)).onaudioprocess=function(e){if(null!==u){var n=e.inputBuffer.getChannelData(0);null!==c&&!0===l&&t.InputCaptureData(n,r.sampleRate)}},o.connect(a),a.connect(r.destination),i=r.sampleRate,o.gain.value=1),void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e,t,n){var r=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return r?new Promise((function(t,n){r.call(navigator,e,t,n)})):(n(),Promise.reject(new Error("getUserMedia is not implemented in this browser")))}),navigator.mediaDevices.getUserMedia)return navigator.mediaDevices.getUserMedia(s).then((function(e){u=e,r.createMediaStreamSource(e).connect(o)})).catch((function(e){})),l=!0,n||(n=new we(0,e)).setSampleRate(i),i},controlVolumnOut:function(e){var t=e/20*2;o.gain.value=t<=0?0:t>=10?10:t},stopAudioOut:function(){if(null!==u&&l)try{for(var e=u.getAudioTracks(),t=0,n=e.length;t<n;t++)e[t].stop();l=!1,u=null}catch(e){}},terminate:function(){this.stopAudioOut(),r.close(),o=null,a=null,n&&(n.terminate(),n=null)},setSendAudioTalkBufferCallback:function(e){c=e}},new f},Re=function(e){var t=0,n=[],r=null,o=null,a=window.H264EncModule,i=document.getElementById("video-capture"),l={audio:!1,video:e||{width:320,height:240,frameRate:15,facingMode:"user"}};window.cPlusH264EncCallBack=function(n,r,i){var l=new ArrayBuffer(r),g=new Uint8Array(l);if(g.set(a.HEAPU8.subarray(n,n+r)),o){var h=function(n,r){var o=n,a=new Uint8Array(u.length+44+o.length+8),i=0;a.set([36,t],i),i+=2,a.set(m(44+o.length+8,4,!0),i),i+=4,a.set(s,i),i+=4,1==r?(a.set([253],i),i+=1):(a.set([252],i),i+=1);a.set([0],i),i+=1,a.set([0],i),i+=1,a.set([0],i),i+=1,f>65535&&(f=240);a.set(m(f),i),i+=4,f++;var l=m(44+o.length+8);a.set(l,i),i+=4;var g=new Date,h=g.getFullYear()-2e3,S=g.getMonth()+1,y=g.getDate(),v=g.getHours(),_=g.getMinutes(),P=g.getSeconds(),E=(h<<26)+(S<<22)+(y<<17)+(v<<12)+(_<<6)+P,A=g.getTime(),b=null===d?0:A-d;d=A,(p+=b)>65535&&(p=65535-p);a.set(m(E),i),i+=4,a.set(m(p,2),i),i+=2,a.set([20],i),i+=1;var w=function(e,t){for(var n=0,r=t;r<e.length;r++)n+=e[r];return n}(a,6);a.set([w],i),i+=1;var C=[129,30,8,e.frameRate];a.set(C,i),i+=4;a.set([130,0,0,0],i),i+=4,a.set(m(e.height,2),i),i+=2,a.set(m(e.width,2),i),i+=2;var T=function(e,t){for(var n=0,r=0;r<t;r++)n+=e[r]<<r%4*8;return n}(o,o.length);return a.set([136],i),i+=1,a.set(m(T),i),i+=4,a.set([0,0,0],i),i+=3,a.set(o,i),i+=o.length,a.set(c,i),i+=4,a.set(l,i),a}(g,i);o(h,7)}};var u=[36,t,0,0,0,0],s=[68,72,65,86],c=[100,104,97,118],f=245,p=0,d=null;function m(e,t,n){var r=[],o=t||4;if(!0===n)for(var a=0;a<o;a++)r[a]=e>>>8*(o-1-a)&255;else for(var i=0;i<o;i++)r[i]=e>>>8*i&255;return r}function g(){var t=document.createElement("canvas");t.height=e.width,t.width=e.height;var n=Math.floor(1e3/e.frameRate),o=0,u=0,s=t.getContext("2d");!function e(){if(!i.paused&&!i.ended){var c,f,p,d,m,g=Date.now();if(0==o||g-o>=n+u){0!=o&&(u+=n-(g-o)),o=g,s.drawImage(i,0,0,t.width,t.height);var h=s.getImageData(0,0,t.width,t.height),S=new Uint8Array(h.data),y=(c=S,f=S.length,p=h.width,d=h.height,m=l.video.frameRate,null==r&&(r=a._malloc(f)),a.HEAPU8.set(c,r),a._h264Enc(r,f,p,d,m));if(0!=y)return void console.error("H264Encode initial failed, ret:"+y)}requestAnimationFrame(e)}}()}function h(){}return h.prototype={initVideoCapture:function(e,n){t=e,void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e,t,n){var r=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return r?new Promise((function(t,n){r.call(navigator,e,t,n)})):Promise.reject(new Error("getUserMedia is not implemented on this browser"))}),navigator.mediaDevices.getUserMedia?navigator.mediaDevices.getUserMedia(l).then((function(e){i.srcObject=e,i.currentTime=0,i.addEventListener("play",g)})).catch((function(e){console.error("Cannot get User Media"+e)})):console.error("Cannot open local media stream! :: navigator.mediaDevices.getUserMedia is not defined!")},stopVideoPlay:function(){if(null!=i){i.pause(),i.removeAttribute("src"),i.removeEventListener("play",g);var e=a._h264EncClose();0!=e&&console.error("H264EncodeClose failed, ret:"+e)}r&&(a._free(r),r=null)},writeDataOut:function(){var e=new Blob(n,{type:"video/webm"}),t=document.createElement("a");console.warn("initial downloadElem: "+t);var r=window.URL.createObjectURL(e);t.href=r,t.download="download.dav",document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(r)},terminate:function(){this.stopVideoPlay()},setBufferCallBack:function(e){o=e}},new h};function Ie(e){return(Ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ue(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ke(r.key),r)}}function ke(e){var t=function(e,t){if("object"!=Ie(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ie(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ie(t)?t:t+""}var He=5,Le=14,Oe=22,Fe=1,Be=2,Ge=3,xe=4;window.WebCodecsVideoFrameMap=new Map;var Ve=0,Ne=function(){return function(e,t,n){return t&&Ue(e.prototype,t),n&&Ue(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.m_VideoFrameIndex=0,this.m_WebCodecsDecoder=null,this.m_playMethod=t,this.m_imageWidth=0,this.m_imageHeight=0,this.m_nFrameRate=25,this.m_bIFrameComming=!1,this.m_WebCodecsVideoFrameScope=Ve++,this.m_SPSParser=new V(n),this.m_nVideoEncodeType=n}),[{key:"decode",value:function(e,t){if(e.nWidth!==this.m_imageWidth||e.nHeight!==this.m_imageHeight){this.close(),this.init();var n;if(null===this.m_WebCodecsDecoder)return console.log("[Error]the WebCodecsDecoder Create Failed!"),!1;if(null===(n=this.GetCodecString(t)))return console.log("[Warn]GetCodecString failed!"),!1;this.m_WebCodecsDecoder.configure({codec:n,codeWidth:e.nWidth,codeHeight:e.nHeight,hardwareAcceleration:"prefer-hardware"}),this.m_imageWidth=e.nWidth,this.m_imageHeight=e.nHeight,this.m_bIFrameComming=!1}if(!this.m_playMethod)return console.log("[Error] the  m_playMethod is null!"),!1;if(this.m_WebCodecsDecoder){if(!this.m_bIFrameComming&&0!==e.nFrameSubType)return console.log("[Error]The first frame is not I in WebCodecsDecoder!"),!1;this.m_bIFrameComming=!0,this.m_WebCodecsDecoder.decode(new EncodedVideoChunk({type:0===e.nFrameSubType?"key":"delta",timestamp:0,duration:0,data:t}))}return!0}},{key:"flush",value:function(){this.m_WebCodecsDecoder&&this.m_WebCodecsDecoder.flush()}},{key:"close",value:function(){this.m_WebCodecsDecoder&&this.m_WebCodecsDecoder.close(),this.m_WebCodecsDecoder=null}},{key:"init",value:function(){this.m_WebCodecsDecoder=new VideoDecoder({output:this.OnOutput.bind(this),error:this.OnError.bind(this)})}},{key:"OnOutput",value:function(e){this.m_VideoFrameIndex++>999&&(this.m_VideoFrameIndex=0);var t=this.m_WebCodecsVideoFrameScope+this.m_VideoFrameIndex;window.WebCodecsVideoFrameMap.set(t,e),this.m_playMethod&&this.m_playMethod.drawWebCodecs(t,0,this.m_nFrameRate,e.codedWidth,e.codedHeight)}},{key:"OnError",value:function(e){console.log("Decode Failed!")}},{key:"GetCodecString",value:function(e){for(var t=null,n=e.length,r=[],o=0;o<=n;)if(0==e[o])if(0==e[o+1])if(1==e[o+2]){if(r.push(o),o+=3,1==this.m_nVideoEncodeType){if(5==(31&e[o])||1==(31&e[o]))break}else if(2==this.m_nVideoEncodeType&&(38==(255&e[o])||2==(255&e[o])))break}else 0==e[o+2]?o++:o+=3;else o+=2;else o+=1;if(1==this.m_nVideoEncodeType){var a=!1;for(o=0;o<r.length;o++)if(t=e.subarray(r[o]+3,r[o+1]),7==(31&e[r[o]+3])){this.m_SPSParser.parse(t),a=!0;break}return a?"avc1."+this.decimalToHex(this.m_SPSParser.getSpsValue("profile_idc"))+this.decimalToHex(this.m_SPSParser.getSpsValue("profile_compatibility"))+this.decimalToHex(this.m_SPSParser.getSpsValue("level_idc")):null}if(2==this.m_nVideoEncodeType){var i=!1;for(o=0;o<r.length;o++)if(t=e.subarray(r[o]+3,r[o+1]-1),66===(255&e[r[o]+3])){var l=e.subarray(r[o]+5,r[o+1]-1);this.m_SPSParser.parse(l),i=!0;break}return i?this.m_SPSParser.getCodecInfo():null}return null}},{key:"decimalToHex",value:function(e,t){var n=Number(e).toString(16);for(t=null==t?t=2:t;n.length<t;)n="0"+n;return n}}])}(),We=function(e,t){t=t;var n=0,r={timestamp:0,timestamp_usec:0},o=null,a=null,i=null,l=!1,u=!1,s=null,c=null,f=null,p=null,d=null,m=null,g=1,h="",S=!1,y=null,v=0,_=0,P={id:1,samples:null,baseMediaDecodeTime:0},E=0,A=null,b=2,w=0,C=0,M=0,D=1,R=null,I=0,U=null,L=null,O=null,F=null,B=null,N=null,W=null,z=null,Y=null,q=null,j=null,K=null,X=null,J=null,Z=null,Q=null,$=null,ee=null,te=null,ne=null,re=0,oe=0,ae=0,ie=0,le=0,ue=0,pe=null,de=null,me=null,ge=null,he=null,Se=null,ye=null,ve=null,_e=null,Pe=null,Ee=null,be=0,we=0,Ce=null,Te=0,Ie=0,Ue=e,ke=!0,Ve=!1,We=!1,ze=!1,Ye=0,qe=0,je=0,Ke=!1,Xe=!1,Je=0,Ze=null,Qe=null,$e=null,et=null,tt=0,nt=0,rt=-1,ot=!1,at=!1,it="",lt=null,ut=null,st=null,ct=null,ft=0,pt=0,dt=!1,mt=!1,gt=0,ht=0,St=!0,yt=0,vt=0,_t=0,Pt=0,Et=45,At=0,bt=!1,wt=!1,Ct=!1,Tt="",Mt="",Dt="",Rt=!1,It=0,Ut=[{},{},{}],kt=null,Ht=0,Lt=null;function Ot(){}function Ft(){return Ce&&Ce.CleanScreen(0,0,0,0),1}function Bt(){null!=s&&(s.setDecodeType(Ie),s.setCodecInfo(h),s.setInitSegmentFunc(Vt),s.setPlaySpeed(g),s.setFPS(v))}function Gt(){null!=s&&(s.setBeginDrawCallback(L),s.setVideoSizeCallback(Nt),s.setErrorCallback(X),s.setAudioStartCallback(xt),s.setMseErrorCallback(Wt),s.setCapturePicDataCallBack(K))}function xt(e,t){}function Vt(){return y}function Nt(){null!==q&&q(!1)}function Wt(e){switch(e){case"InitError":zt();break;case"Error":case"SourceError":case"SourceBufferError":Ke=!0,++je>2&&(je=0,zt())}}function zt(){if(1==Ie?ke=!1:Ve=!1,Ue)Module._PLAY_SetSupportWebMSE(n,ke,Ve);else{var e={nType:"SetSupportWebMSE",bSupportH264MSE:ke,bSupportH265MSE:Ve};lt&&lt.postMessage(e)}}function Yt(){s&&(s.close(),s.terminate(),s=null),u=!1,y=null,c=null,f=null,S=!1,Xe=!1,w=0,P={id:1,samples:null,baseMediaDecodeTime:0},E=0,D=1,M=0,I=0,C=0,b=2}function qt(){var e=1;if(Ue)e=Module._PLAY_StopDataRecord(n);else{lt&&lt.postMessage({nType:"StopRecord"})}return(Ue||5!=tt)&&Ze&&(Ze.postMessage({type:"close"}),Ze=null),nt=0,e}function jt(e,o){if(1!=We&&(We=!0,Ft()),rt=e.nFrameID,re==ae&&oe==ie&&e.nEncodeType==Te||(0!=ae&&(Ke=!0),ae=re,ie=oe,Te=e.nEncodeType),v!=_&&(t.info("MSE FrameRate change, PlayPort:"+n+", m_nFrameRate:"+v+", m_nPreFrameRate:"+_),_=v),Ke&&(Yt(),Ke=!1),0!=Xe||0==e.nFrameSubType){if(null==c&&(c=new V(Ie)),null==f&&(f=new x(Ie)),function(e,t,n){for(var r=null,o=e.length,a=[],i=0;i<=o;)if(0==e[i])if(0==e[i+1])if(1==e[i+2]){if(a.push(i),i+=3,1==Ie){if(5==(31&e[i])||1==(31&e[i]))break}else if(2==Ie&&(38==(255&e[i])||2==(255&e[i])))break}else 0==e[i+2]?i++:i+=3;else i+=2;else i+=1;var l=0;if(1==Ie){for(i=0;i<a.length;i++)switch(r=e.subarray(a[i]+3,a[i+1]),31&e[a[i]+3]){case 1:case 5:l=a[i]-1,U=e.subarray(l,e.length);break;case 7:c.parse(r),d=r,u=!0;break;case 8:m=r}if(!S&&u){S=!0;var g={id:1,width:re,height:oe,type:"video",profileIdc:c.getSpsValue("profile_idc"),profileCompatibility:0,levelIdc:c.getSpsValue("level_idc"),sps:[d],pps:[m],timescale:1e3,fps:v};y=f.initSegment(g)}}else if(2==Ie){for(i=0;i<a.length;i++)switch(r=e.subarray(a[i]+3,a[i+1]-1),255&e[a[i]+3]){case 2:case 38:l=a[i]-1,U=e.subarray(l,e.length);break;case 64:p=r;break;case 66:var _=e.subarray(a[i]+5,a[i+1]-1);c.parse(_),d=r,u=!0;break;case 68:m=r}if(!S&&u){S=!0;var P=c.getSpsValue("general_profile_space"),E=c.getSpsValue("general_tier_flag"),A=c.getSpsValue("general_profile_idc"),b=c.getSpsValue("temporalIdNested");g={id:1,width:re,height:oe,type:"video",general_profile_flag:P<<6|E<<5|A,general_profile_compatibility_flags:c.getSpsValue("general_profile_compatibility_flags"),general_constraint_indicator_flags:c.getSpsValue("general_constraint_indicator_flags"),general_level_idc:c.getSpsValue("general_level_idc"),chroma_format_idc:c.getSpsValue("chroma_format_idc"),bitDepthLumaMinus8:c.getSpsValue("bitDepthLumaMinus8"),bitDepthChromaMinus8:c.getSpsValue("bitDepthChromaMinus8"),rate_layers_nested_length:11|(1&b)<<2,vps:[p],sps:[d],pps:[m],timescale:1e3,fps:v};y=f.initSegment(g)}}u&&(h=c.getCodecInfo(),s&&s.setCodecInfo(h))}(o,e.nFrameSubType,e.nFrameID),0==Xe){null==s&&(s=new fe(t)).init(a);var i={decodeMode:"video"};i.width=re,i.height=oe,[2,4,8].includes(e.nEncodeType)?i.encodeMode="H264":12===e.nEncodeType&&(i.encodeMode="H265"),O&&O(i),Gt(),Bt(),Xe=!0}at&&(at=!1,s.capturePic([a],it)),s&&s.setvideoTimeStamp(r),function(){if(null!=U){if(!l&&Ue){var e=Module._PLAY_GetPlaySpeed(n);s&&s.setPlaySpeed(e)}var t={duration:Math.round(1/v*1e3),size:U.length,frame_time_stamp:null,frameDuration:null};t.frameDuration=t.duration,null==P.samples&&(P.samples=new Array(b)),P.samples[w++]=t,C+=t.frameDuration,M+=t.frameDuration;var r=U.length-4;U[0]=(4278190080&r)>>>24,U[1]=(16711680&r)>>>16,U[2]=(65280&r)>>>8,U[3]=255&r;var o=new Uint8Array(U.length+E);if(0!==E&&o.set(A),o.set(U,E),E=(A=o).length,w%b==0&&0!==w){if(null!==P.samples[0].frameDuration&&(P.baseMediaDecodeTime=1===D?0:I,I=C),1==g)for(var a=P.samples.length,i=M/b,u=0;u<a;u++)P.samples[u].frameDuration=i;M=0,R=f.mediaSegment(D,P,A,P.baseMediaDecodeTime),D++,w=0,A=null,E=0,null!==s?s.setMediaSegment(R):!1===S&&Bt(),2==b&&(b=1,P.samples=null,P.samples=new Array(b))}}}(),Ce&&Ce.DrawDrawIVS(e.nFrameID)}}function Kt(e,o,a,i,l){ft>=l.nTotalStreamLength&&(pt=ft-l.nTotalStreamLength);var u,s,c=Date.UTC(l.nYear,l.nMonth,l.nDay,l.nHour,l.nMinute,l.nSecond)/1e3;if(1==l.nFrameType){if(t.log("PlayFrameData, PlayPort:"+n+", nFrameID:"+l.nFrameID+", nRemainData:"+l.nRemainData),ue=l.nRemainData,ze&&0==ue&&Y(),l.bThrowFrame)return;if(8==l.nStreamType?r.timestamp=c:r.timestamp=l.nTimeStamp/1e3,r.timestamp_usec=0,2==l.nEncodeType||4==l.nEncodeType||8==l.nEncodeType?Ie=1:12==l.nEncodeType&&(Ie=2),Ce&&Ce.setEncodeType(Ie),re=l.nWidth,oe=l.nHeight,0==re||0==oe)return;if(v=l.nFrameRate,Ye<=5&&Ye++,18==l.nFrameSubType||19==l.nFrameSubType||20==l.nFrameSubType?le=1:0==l.nFrameSubType&&(le=0),2==l.nFrameSubType&&!dt){if(dt=!0,1==Ie?ke=!1:2==Ie&&(Ve=!1),Ue)Module._PLAY_SetSupportWebMSE(n,ke,Ve);else{var f={nType:"SetSupportWebMSE",bSupportH264MSE:ke,bSupportH265MSE:Ve};lt&&lt.postMessage(f)}return}if(Ce&&Ce.SetLifeCount(3*l.nFrameRate),(1==Ie&&1==ke||2==Ie&&1==Ve)&&!le&&13!=l.nStreamType){if(u=l.nFrameID,0!=(s=l.nFrameSubType)&&18!=s&&20!=s&&-1!=rt&&u!=rt+1&&(t.info("checkFrame failed, PlayPort:"+n+", nFrameID:"+u+", m_nLastFrameID:"+rt),1))return;0!==ht?(rt=l.nFrameID,kt&&kt.decode(l,e)):jt(l,e)}else{if(!Ue&&(null==e||null==o||null==a))return;if(0!=We&&(We=!1,Yt(),Ke=!0,ae=0,ie=0),re!=ae||oe!=ie)ae=re,ie=oe,Ue||Ce.resize(re,oe),(p={decodeMode:"canvas"}).width=re,p.height=oe,[2,4,8].includes(l.nEncodeType)?p.encodeMode="H264":12===l.nEncodeType&&(p.encodeMode="H265"),O&&O(p);if(rt=l.nFrameID,Ue){var p;if(!ot)ot=!0,(p={decodeMode:"canvas"}).width=re,p.height=oe,[2,4,8].includes(l.nEncodeType)?p.encodeMode="H264":12===l.nEncodeType&&(p.encodeMode="H265"),L(p);Ce&&Ce.DrawDrawIVS(l.nFrameID)}else Ce&&Ce.draw(e,o,a,l.nFrameID,v)}var d=l.nYear,m=fn(l.nMonth),g=fn(l.nDay),h=fn(l.nHour),S=fn(l.nMinute),y=fn(l.nSecond);l.timeStamp=Date.UTC(d,m,g,h,S,y)/1e3,l.utcTimeStamp=new Date("".concat(d,"-").concat(m,"-").concat(g," ").concat(h,":").concat(S,":").concat(y)).getTime(),F(l)}else if(2==l.nFrameType){if(qe<=10&&qe++,!mt&&Ye<5&&qe<=10){if(v>5||0==v)return;if(Ye<2)return}if(l.nSamples==be&&l.nBits==we||(be=l.nSamples,we=l.nBits,Ee.setSampleBits(l.nSamples,l.nBits)),Ee&&Ee.bufferAudio(e,0),Ue){var _=Ee.getAudioBufTime();setTimeout((function(){Module._PLAY_SetInt32(n,2057,_)}))}B(l)}}function Xt(e,t,n,r,o){if(-1!=o){if(n==He)if(Ue||We)o=rt;else{var a=Ce&&Ce.GetCurrentFrameID();if(-1==a||null==a)return;o=a}if(Ue){var i=new ArrayBuffer(r),l=new Uint8Array(i);l.set(Module.HEAPU8.subarray(t,t+r));var u=new DataView(l.buffer);if(Oe==n){var s={};s.nId=u.getUint32(0,!0),s.wCustom=u.getUint16(4,!0),s.chState=u.getUint8(6,!0),s.chCount=u.getUint8(7,!0);var c=u.getUint32(8,!0),f=new ArrayBuffer(12),p=new Uint8Array(f),d=new DataView(f);s.pElement=new Array(s.chCount);for(var m=0;m<s.chCount;m++){p.set(Module.HEAPU8.subarray(c+12*m,c+12*m+12)),s.pElement[m]={},s.pElement[m].nStructType=d.getUint32(0,!0),s.pElement[m].nStructLength=d.getUint32(4,!0);var g=d.getUint32(8,!0),h=new ArrayBuffer(s.pElement[m].nStructLength),S=new Uint8Array(h),y=new DataView(h);if(S.set(Module.HEAPU8.subarray(g,g+s.pElement[m].nStructLength)),s.pElement[m].pStruct={},Fe==s.pElement[m].nStructType)s.pElement[m].pStruct.chType=y.getUint8(0,!0),s.pElement[m].pStruct.chWidth=y.getUint8(1,!0),s.pElement[m].pStruct.chStyle=y.getUint8(2,!0),s.pElement[m].pStruct.wRadius=y.getUint16(4,!0),s.pElement[m].pStruct.positionCircle={},s.pElement[m].pStruct.positionCircle.x=y.getUint16(8,!0),s.pElement[m].pStruct.positionCircle.y=y.getUint16(10,!0),s.pElement[m].pStruct.chLineA=y.getUint8(12,!0),s.pElement[m].pStruct.chLineR=y.getUint8(13,!0),s.pElement[m].pStruct.chLineG=y.getUint8(14,!0),s.pElement[m].pStruct.chLineB=y.getUint8(15,!0),s.pElement[m].pStruct.chRegA=y.getUint8(16,!0),s.pElement[m].pStruct.chRegR=y.getUint8(17,!0),s.pElement[m].pStruct.chRegG=y.getUint8(18,!0),s.pElement[m].pStruct.chRegB=y.getUint8(19,!0);else if(Be==s.pElement[m].nStructType){s.pElement[m].pStruct.chType=y.getUint8(0,!0),s.pElement[m].pStruct.chCount=y.getUint8(1,!0),s.pElement[m].pStruct.chWidth=y.getUint8(2,!0),s.pElement[m].pStruct.chStyle=y.getUint8(3,!0),s.pElement[m].pStruct.chLineA=y.getUint8(4,!0),s.pElement[m].pStruct.chLineR=y.getUint8(5,!0),s.pElement[m].pStruct.chLineG=y.getUint8(6,!0),s.pElement[m].pStruct.chLineB=y.getUint8(7,!0);var v=null,_=null,P=null,E=null;s.pElement[m].pStruct.chCount>0&&(s.pElement[m].pStruct.pPoints=new Array(s.pElement[m].pStruct.chCount),v=y.getUint32(8,!0),_=new ArrayBuffer(4),P=new Uint8Array(_),E=new DataView(_));for(var A=0;A<s.pElement[m].pStruct.chCount;A++)P.set(Module.HEAPU8.subarray(v+4*A,v+4*A+4)),s.pElement[m].pStruct.pPoints[A]={},s.pElement[m].pStruct.pPoints[A].x=E.getUint16(0,!0),s.pElement[m].pStruct.pPoints[A].y=E.getUint16(2,!0)}else if(Ge==s.pElement[m].nStructType){s.pElement[m].pStruct.chType=y.getUint8(0,!0),s.pElement[m].pStruct.chCount=y.getUint8(1,!0),s.pElement[m].pStruct.chWidth=y.getUint8(2,!0),s.pElement[m].pStruct.chStyle=y.getUint8(3,!0),s.pElement[m].pStruct.chLineA=y.getUint8(4,!0),s.pElement[m].pStruct.chLineR=y.getUint8(5,!0),s.pElement[m].pStruct.chLineG=y.getUint8(6,!0),s.pElement[m].pStruct.chLineB=y.getUint8(7,!0),s.pElement[m].pStruct.chRegA=y.getUint8(8,!0),s.pElement[m].pStruct.chRegR=y.getUint8(9,!0),s.pElement[m].pStruct.chRegG=y.getUint8(10,!0),s.pElement[m].pStruct.chRegB=y.getUint8(11,!0);v=null;var b=null,w=null,C=null;s.pElement[m].pStruct.chCount>0&&(s.pElement[m].pStruct.pPoints=new Array(s.pElement[m].pStruct.chCount),v=y.getUint32(12,!0),b=new ArrayBuffer(4),w=new Uint8Array(b),C=new DataView(b));for(A=0;A<s.pElement[m].pStruct.chCount;A++)w.set(Module.HEAPU8.subarray(v+4*A,v+4*A+4)),s.pElement[m].pStruct.pPoints[A]={},s.pElement[m].pStruct.pPoints[A].x=C.getUint16(0,!0),s.pElement[m].pStruct.pPoints[A].y=C.getUint16(2,!0)}else if(xe==s.pElement[m].nStructType){s.pElement[m].pStruct.chType=y.getUint8(0,!0),s.pElement[m].pStruct.chCharset=y.getUint8(1,!0),s.pElement[m].pStruct.stringPos={},s.pElement[m].pStruct.stringPos.x=y.getUint16(4,!0),s.pElement[m].pStruct.stringPos.y=y.getUint16(6,!0),s.pElement[m].pStruct.chLineA=y.getUint8(8,!0),s.pElement[m].pStruct.chLineR=y.getUint8(9,!0),s.pElement[m].pStruct.chLineG=y.getUint8(10,!0),s.pElement[m].pStruct.chLineB=y.getUint8(11,!0),s.pElement[m].pStruct.chFontSize=y.getUint8(12,!0),s.pElement[m].pStruct.chFontAlign=y.getUint8(13,!0),s.pElement[m].pStruct.wTxtLen=y.getUint16(14,!0);var T=y.getUint32(16,!0),M=new ArrayBuffer(s.pElement[m].pStruct.wTxtLen),D=new Uint8Array(M);new DataView(M);D.set(Module.HEAPU8.subarray(T,T+s.pElement[m].pStruct.wTxtLen)),s.pElement[m].pStruct.stringDataArray=D}}if(s.nInfoLen=u.getUint16(12,!0),s.nInfoLen>0){var R=u.getUint32(16,!0),I=new ArrayBuffer(nInfoLen),U=new Uint8Array(I);U.set(Module.HEAPU8.subarray(R,R+nInfoLen)),s.pInfo=U}Ce&&Ce.DrawIVS(s,n,r,o)}else if(Le==n){for(var k=new DataView(i),H=r/144,L=[],O=0;O<H;O++){var F={},B=144*O;F.nIndex=k.getInt32(B+0,!0),F.xPoint=k.getUint16(B+4,!0),F.yPoint=k.getUint16(B+6,!0);var G=new ArrayBuffer(64);G=i.slice(B+8),F.strName=pn(G),F.enable=k.getInt8(B+72,!0),F.titleType=k.getInt8(B+73,!0),F.titleAttribute=k.getInt8(B+74,!0),F.sharpType=k.getInt8(B+75,!0),F.polygonNum=k.getInt8(B+76,!0),F.polygon=[];for(var x=0;x<2*F.polygonNum;x+=2)F.polygon[x]={x:k.getInt8(B+79+2*x,!0),y:k.getInt8(B+79+2*(x+1),!0)};L[O]=F,G=null}j&&j(L),k=null,Ce&&Ce.DrawIVS(l,n,r,o)}else Ce&&Ce.DrawIVS(l,n,r,o)}else Ce&&Ce.DrawIVS(t,n,r,o)}}function Jt(e){var t=e.data.msgType,r=e.data.nPort,a=e.data.msgData,i=0,u=0;switch(o&&(i=parseInt(o.width),u=parseInt(o.height)),t){case"LoadSuccess":if(0<Ht){var s={nType:"setPrintLogLevel",nLogLevel:Ht};lt.postMessage(s)}s={nType:"Init",option:{bPlayback:l,bSupportMultiThread:Ue,bSupportH264MSE:ke,bSupportH265MSE:Ve,nCanvasWidth:i,nCanvasHeight:u}};if(lt.postMessage(s),wt=!0,Ct||(Ct=!0,0!=Tt.length&&0!=Mt.length&&0!=Dt.length&&GetOriginalKey(Tt,Mt,Dt)),!Rt)for(var c=0;c<3;c++)null!=Ut[c].nFrameType&&SetWebSecurityKey(It,Ut[c].nFrameType,Ut[c].strKey,Ut[c].stStreamInfo);break;case"InitSuccess":Z(n=r);break;case"VisibleDecCallBack":Q(),Kt(a.pBufY,a.pBufU,a.pBufV,a.nSize,a.stuFrameInfo);break;case"IVSDataCallBack":Xt(0,a.pBuf,a.nType,a.nLen,a.nReallen);break;case"RecordDataCallBack":!function(e,t,n,r){nt+=t;var o=r.nYear,a=r.nMonth,i=r.nDay,l=r.nHour,u=r.nMinute,s=r.nSecond,c=Date.UTC(o,a,i,l,u,s)/1e3,f=new Date("".concat(o,"-").concat(a,"-").concat(i," ").concat(l,":").concat(u,":").concat(s)).getTime();N&&N({frameType:r.nFrameType,timeStamp:f,utcTimeStamp:c,length:nt}),Ze.postMessage({type:"addBuffer",buffer:e,offset:n,recordType:tt})}(a.pRecordData,a.nLen,a.Offset,a.stuFrameInfo),a.Offset<Je?(Ze.postMessage({type:"close"}),Ze=null,Je=0):Je=a.Offset;break;case"DecryptionResultCallBack":a.bSuccess,z(a.bSuccess);break;case"CatchPicCallBack":K&&K(a.buffer);var f=new Blob([a.buffer.buffer],{type:"image/jpg"});cn(f,it);break;case"GetOriginalKeyCallBack":W(a);break;case"ARTagInfoCallback":j&&j(a.tagInfo)}}Ot.prototype={Init:function(e){o=e.canvasElem,a=e.videoElem,i=e.ivsCanvasElem,l=e.bPlayBack;var r=1;At=o&&o.height,Ve=function(){var e=k(),t=H(e),n=!1;switch(e){case T:n=t>=104;break;default:n=0}return n}();var u=navigator.platform;k();if(0==u.indexOf("iPhone")&&(ke=!1,Ve=!1),Ue){var c=Module._malloc(1);if($=new Uint8Array(Module.HEAPU8.buffer,c,1),Module._PLAY_GetFreePort($.byteOffset),n=$[0],$=null,Module._free(c),r=Module._PLAY_SetStreamOpenMode(n,l),r=Module._PLAY_OpenStream(n,0,0,10485760),r=Module._PLAY_SetCacheMode(n,1),r=Module._PLAY_SetSupportWebMSE(n,ke,Ve),o){Module._PLAY_ViewResolutionChanged(n,parseInt(o.width),parseInt(o.height),0);var f=Module.allocateUTF8(o.id);r=Module._PLAY_Play(n,f),Module._free(f)}else Module._PLAY_ViewResolutionChanged(n,0,0,0),r=Module._PLAY_Play(n,0)}else(lt=new Worker("".concat(e.strDecodeFilePath,"/VideoDecodeWorker.js"))).onmessage=Jt,Lt=new G(200);(t.log("Init, PlayPort:"+n+", canvasElem:"+e.canvasElem+", videoElem:"+e.videoElem+", ivsCanvasElem:"+e.ivsCanvasElem+", bPlayBack:"+e.bPlayBack),t.log("Init, m_bSupportMultiThread:"+Ue+", m_bSupportH264MSE:"+ke+", m_bSupportH265MSE:"+Ve),r)&&(Ue&&(ee=Module._malloc(5242880),te=new Uint8Array(Module.HEAPU8.buffer,ee,5242880)),Ce=new se(o,i,!0===l?100:25,l,Ue),0<Ht&&Ce.SetPrintLogLevel(Ht),(Ve||ke)&&a&&((s=new fe(t)).init(a),Gt()));return(Ee=new ce)&&(Ee.audioInit(1)||(Ee.stop(),Ee=null)),r},SetCacheMode:function(e){t.log("SetCacheMode, PlayPort:"+n+", nMode:"+e);var r=0;return Ue&&(r=Module._PLAY_SetCacheMode(n,e)),r},GetPlayPort:function(){return n},InputData:function(e){t.log("InputData, PlayPort:"+n+", length:"+e.length);var r=1;if(Ue)te&&(te.set(e),r=Module._PLAY_InputData(n,te.byteOffset,e.length));else{ft+=e.length;var o={nType:"InputData",pData:e};if(wt){for(;Lt.size>0;){var a={nType:"InputData",pData:Lt.dequeue().buffer};lt.postMessage(a)}lt.postMessage(o)}else Lt.enqueue(e)}return r},Pause:function(e){t.log("Pause, PlayPort:"+n+", bPause:"+e);var r=1;if(Ue)r=Module._PLAY_Pause(n,e);else{var o={nType:"Pause",bPause:e};lt&&lt.postMessage(o)}return r},SetPlaySpeed:function(e){t.log("SetPlaySpeed, PlayPort:"+n+", nSpeed:"+e);var r=1;if(Ue)r=Module._PLAY_SetPlaySpeed(n,e);else{var o={nType:"SetPlaySpeed",nSpeed:e};lt&&lt.postMessage(o)}return Ce&&Ce.setPlaySpeed(e),s&&s.setPlaySpeed(e),g=e,r},SetSecurityKey:function(e,r,o,a,i){if(t.log("SetSecurityKey, PlayPort:"+n),Ue){var l=Module._malloc(49),u=new Uint8Array(Module.HEAPU8.buffer),s=0;if(1==e)r.forEach((function(e,t){u[l+s>>0]=e,s++}));else if(2==e){var c=new Uint8Array(16);if(u[l+s>>0]=1,s++,0==i){for(var f=0;f<16;f++)c[f]=0;i=16,a=c}a.forEach((function(e,t){u[l+s>>0]=e,s++})),r.forEach((function(e,t){u[l+s]=e,s++})),o=1+o+i,c=null}else 3==e&&r.split("").forEach((function(e,t){u[l+s>>0]=e.charCodeAt(0),s++}));Module._PLAY_SetSecurityKey(n,l,o),Module._free(l)}else{var p={nType:"SetSecurityKey",nEncryptType:e,szKey:r,nKeyLen:o,szKeyId:a,nKeyIdLen:i};lt&&lt.postMessage(p)}},StartRecord:function(e,r,o){t.log("StartRecord, PlayPort:"+n+", nRecordType:"+e+", nFileSize:"+r+", strRecordName:"+o);var a,i,l=1;if(Ze=new Ae,a=o,i=parseInt(r)||500,Ze.postMessage({type:"init",options:{recordName:a,singleSize:1048576*i,nameOptions:{namedBy:"date",nameFormat:["ymd_his"]},limitOptions:{limitBy:"count",count:10}}}),Ze.onMessage=function(e){switch(e.type){case"pendding":break;case"download":t=e.data.name,n=e.data.buffer,r=new Blob([n]),(o=document.createElement("a")).href=URL.createObjectURL(r),o.download=t,o.click(),URL.revokeObjectURL(o.href),o=null,n=null;break;case"close":setTimeout((function(){qt()}))}var t,n,r,o},tt=e,Ue)l=Module._PLAY_StartDataRecord(n,0,tt);else{var u={nType:"StartRecord",nRecordType:tt};lt&&lt.postMessage(u)}return l},StopRecord:function(){return t.log("StopRecord, PlayPort:"+n),qt()},OpenIVSDraw:function(e){t.log("OpenIVSDraw, PlayPort:"+n+", nWndIndex:"+e);var r=1;if(Ue)r=Module._PLAY_RenderPrivateData(n,1,0);else{lt&&lt.postMessage({nType:"OpenIVSDraw"})}return Ce&&Ce.OpenIVS(e,i.id),r},CloseIVSDraw:function(){t.log("CloseIVSDraw, PlayPort:"+n);var e=1;if(Ue)e=Module._PLAY_RenderPrivateData(n,0,0);else{lt&&lt.postMessage({nType:"CloseIVSDraw"})}return Ce&&Ce.CloseIVS(),e},SetIvsEnable:function(e,r){t.log("SetIvsEnable, PlayPort:"+n+", nIvsType:"+e+", bEnable:"+r),Ce&&Ce.SetIvsEnable(e,r)},SetPanoAR:function(e,r){t.log("SetPanoAR, PlayPort:"+n+", PanoARType:"+e);var o=1;if(Ue&&!We){var a=null;return null!==r&&9===e&&(a=Module._malloc(12),Module.HEAPF32[a/4+0]=r.VerFieldViewAngle,Module.HEAPF32[a/4+1]=r.HoriFieldViewAngle,Module.HEAPF32[a/4+2]=r.DownPressAngle),o=Module._PLAY_SetPanoVRMode(n,e,a),ht=e,null!==a&&Module._free(a),o}return 0===ht&&0!==e?We?(Ke=!0,Ce.resize(re,oe),null===kt&&(kt=new Ne(Ce,Ie))):(Ce.terminate(),Ce.resize(re,oe)):0!==ht&&0===e&&(Ft(),Ce.terminate(),We||Ce.resize(re,oe),kt=null),Ce.SetPanoVRMode(e,r,re,oe),ht=e,o},Set3DPoint:function(e){t.log("Set3DPoint, PlayPort:"+n+", b3DPoint:"+e),Ue&&(gt=e)},OnMouseDown:function(){0===ht&&0===gt||(bt=!0)},OnMouseMove:function(e,t){if(0!==ht||0!==gt){St&&(yt=e,vt=t,0,0,St=!1);var r=e-yt,o=t-vt;if(yt=e,vt=t,bt){if(Ue&&!We){var a=Module._malloc(8),i=Module._malloc(8);Module._PLAY_GetDoubleRegion(n,0,2050,a),Module._PLAY_GetDoubleRegion(n,0,2051,i),_t=Module.HEAPF64[a/8],Pt=Module.HEAPF64[i/8],Module._free(a),Module._free(i)}else{if(!Ce)return void console.log("[Error]The m_playMethod is invalid in OnMouseMove!");_t=Ce.GetModelRotate().x,Pt=Ce.GetModelRotate().y}Math.abs(r)>=Math.abs(o)?Pt+=-2*r*Et/At:_t+=-2*o*Et/At,Ue&&!We?Module._PLAY_SetStereoRotate(n,0,_t,Pt,0):Ce.SetModelRotate(_t,Pt,0)}}},OnMouseUp:function(){0===ht&&0===gt||(bt=!1)},OnMouseWheel:function(e){if(0!==ht||0!==gt)if(e<0?Et>=10&&(Et-=2):Et<=120&&(Et+=2),Ue&&!We)Module._PLAY_SetStereoPerspectiveFovy(n,0,Et);else{if(!Ce)return void console.log("[Error]The m_playMethod is invalid in OnMouseWheel!");Ce.SetStereoPerspectiveFovy(Et)}},GetVRCoord2DTrans:function(e,t){return function(e,t){var r;if(Ue&&!We){var o=Module._malloc(4),a=Module._malloc(4);Module._PLAY_GetVRCoord2DTrans(n,0,e,t,o,a),r={x:Module.HEAPF32[o/4],y:Module.HEAPF32[a/4]},Module._free(o),Module._free(a)}else{if(!Ce)return void console.log("[Error]The m_playMethod is invalid in GetVRCoord2DTrans!");r=Ce.GetVRCoord2DTrans(e,t)}return[(r.x+1)/2*i.width,(1-r.y)/2*i.height]}(e,t)},GetVRCoord3DTrans:function(e,t){return function(e,t){var r;if(Ue&&!We){var o=Module._malloc(4),a=Module._malloc(4);Module._PLAY_GetVRCoord3DTrans(n,0,e,t,o,a),r={x:Module.HEAP32[OutXPtr/4],y:Module.HEAP32[OutYPtr/4]},Module._free(OutXPtr),Module._free(OutYPtr)}else{if(!Ce)return void console.log("[Error]The m_playMethod is invalid in GetVRCoord3DTrans!");r=Ce.GetVRCoord3DTrans(e,t)}return[r.x,r.y]}(e,t)},SetSoundState:function(e){t.log("SetSoundState, PlayPort:"+n+", bPlay:"+e),Ee&&Ee.setSoundState(e)},SetVolume:function(e){t.log("SetVolume, PlayPort:"+n+", nVolume:"+e),Ee&&Ee.setVolume(e)},StartTalk:function(e){if(t.log("StartTalk, PlayPort:"+n+", nEncodeType:"+e),!ut){var r=void 0!==window.ASPLiteModule;st=new Me(r),(ut=new De(st)).setSendAudioTalkBufferCallback(J),ut.initAudioOut(e),Ee&&r&&Ee.setAudioProcesser(st)}},StopTalk:function(){t.log("StopTalk, PlayPort:"+n),ut&&(ut.terminate(),ut=null),Ee&&Ee.setAudioProcesser(null),st&&(st.StopProcess(),st=null)},StartVideoCapture:function(e,r,o){t.log("StartVideoCapture, PlayPort:"+n+", nEncodeType:"+r),ct||((ct=new Re(o)).setBufferCallBack(J),ct.initVideoCapture(0,0))},StopVideoCapture:function(){t.log("StopVideoCapture, PlayPort:"+n),ct&&(ct.terminate(),ct=null)},GetSourceBufferRemain:function(){var e=0;return e=Ue?Module._PLAY_GetSourceBufferRemain(n):pt,t.log("GetSourceBufferRemain, PlayPort:"+n+", nRemain:"+e),e},SetStreamOver:function(e){t.log("SetStreamOver, PlayPort:"+n+", bOver:"+e),(ze=e)&&0==ue&&Y()},ResetBuffer:function(){t.log("ResetBuffer, PlayPort:"+n),Ue&&(Module._PLAY_ResetBuffer(n,0),Module._PLAY_ResetBuffer(n,1),Module._PLAY_ResetBuffer(n,2))},capturePic:function(e){if(t.log("capturePic, PlayPort:"+n+", strPictureName:"+e),it=e,We)at=!0;else if(Ue){var r=re*oe*3/2,o=Module._malloc(r),a=new Uint8Array(Module.HEAPU8.buffer,o,r),i=Module._malloc(4),l=new Uint8Array(Module.HEAPU8.buffer,i,4);Module._PLAY_GetPicJPEG(n,a.byteOffset,r,l.byteOffset,100);var u=(l[3]<<24)+(l[2]<<16)+(l[1]<<8)+l[0],s=new ArrayBuffer(u),c=new Uint8Array(s);c.set(Module.HEAPU8.subarray(a.byteOffset,a.byteOffset+u)),K&&K(c);var f=new Blob([c.buffer],{type:"image/jpg"});cn(f,it),f=null,Module._free(o),Module._free(i),a=null,l=null,s=null,c=null}else{lt&&lt.postMessage({nType:"CatchPic"})}},OpenPlayGroup:function(){var e=null;return Ue&&(e=Module._PLAY_OpenPlayGroup()),t.log("OpenPlayGroup, pGroupHandle:"+e),e},AddToPlayGroup:function(e,n){t.log("AddToPlayGroup, pGroupHandle:"+e+", nPort:"+n);var r=0;return Ue&&(r=Module._PLAY_AddToPlayGroup(e,n)),r},DelFromPlayGroup:function(e,n){t.log("DelFromPlayGroup, pGroupHandle:"+e+", nPort:"+n);var r=0;return Ue&&(r=Module._PLAY_DelFromPlayGroup(e,n)),r},ClosePlayGroup:function(e){t.log("ClosePlayGroup, pGroupHandle:"+e);var n=0;return Ue&&(n=Module._PLAY_ClosePlayGroup(e)),n},PausePlayGroup:function(e,n){t.log("PausePlayGroup, pGroupHandle:"+e+", bPause:"+n);var r=0;return Ue&&(r=Module._PLAY_PausePlayGroup(e,n)),r},SetPlayGroupSpeed:function(e,n){t.log("SetPlayGroupSpeed, pGroupHandle:"+e+", fSpeed:"+n);var r=0;return Ue&&(r=Module._PLAY_SetPlayGroupSpeed(e,n)),r},SetAudioTalkFlag:function(e){t.log("SetAudioTalkFlag, PlayPort:"+n+", bAudioTalk:"+e),mt=e},SetDecodeMode:function(e,r){if(t.log("SetDecodeMode, PlayPort:"+n+", nH264DecodeMode:"+e+", nH265DecodeMode:"+r),ke=e,Ve=r,Ue)Module._PLAY_SetSupportWebMSE(n,e,r);else{var o={nType:"SetSupportWebMSE",bSupportH264MSE:e,bSupportH265MSE:r};lt&&lt.postMessage(o)}},GetOriginalKey:function(e,r,o){t.log("GetOriginalKey, PlayPort:"+n);var a=1;if(Ue){var i=Module.intArrayFromString(e).concat(0),l=Module._malloc(i.length);Module.HEAPU8.set(i,l);var u=Module.intArrayFromString(r).concat(0),s=Module._malloc(u.length);Module.HEAPU8.set(u,s);var c=null;if(null!==o){var f=Module.intArrayFromString(o).concat(0);c=Module._malloc(f.length),Module.HEAPU8.set(f,c)}var p=Module._malloc(256),d=Module._malloc(4);a=Module._PLAY_GetOriginalKey(n,l,s,c,p,d);var m=Module.HEAP32[d>>2],g="";if(1==a&&m<=256){var h=new ArrayBuffer(m);new Uint8Array(h).set(Module.HEAPU8.subarray(p,p+m)),g=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"utf-8",n=new TextDecoder(t);return n.decode(e)}(h)}if(Module._free(l),Module._free(s),Module._free(c),Module._free(p),Module._free(d),null===o)return g;W({nRet:a,outKey:g})}else if(wt){Ct=!0;var S={nType:"GetOriginalKeyCallBack",playToken:e,playTokenKey:r,deviceID:o};lt&&lt.postMessage(S)}else Tt=e,Mt=r,Dt=o;return a},SetWebSecurityKey:function(e,r,o,a){if(t.log("SetWebSecurityKey, PlayPort:"+n),!Ue)if(wt){Rt=!0;var i={nType:"SetWebSecurityKey",nDecryptType:e,nFrameType:r,strKey:o,stStreamInfo:a};lt&&lt.postMessage(i)}else It=e,1==r?(Ut[0].nFrameType=r,Ut[0].strKey=o,Ut[0].stStreamInfo=a):21==r?(Ut[1].nFrameType=2,Ut[1].strKey=o,Ut[1].stStreamInfo=a):22==r&&(Ut[2].nFrameType=2,Ut[2].strKey=o,Ut[2].stStreamInfo=a)},ResetPlayState:function(){t.log("ResetPlayState, PlayPort:"+n),Ce&&Ce.ResetPlayState(),s&&s.ResetPlayState()},Stop:function(){return function e(){var r=1;if(Ue){if(r=Module._PLAY_GetThreadRunningState(n),t.log("GetThreadRunningState, PlayPort:"+n+", nRet:"+r),0==r)return setTimeout((function(){e()}),10),1;if(t.log("Stop, PlayPort:"+n),0==(r=Module._PLAY_Stop(n)))return r;r=Module._PLAY_CloseStream(n),te=null,Module._free(ee)}else{lt&&(lt.postMessage({nType:"Stop"}),lt.terminate(),lt=null),wt=!1,null!==Lt&&(Lt.clear(),Lt=null)}Yt(),pe=null,de=null,me=null,he=null,Se=null,ye=null,ve=null,_e=null,Pe=null,Qe=null,$e=null,et=null,Je=0,Ee&&(Ee.stop(),Ee.setAudioProcesser(null),Ee=null);ut&&(ut.terminate(),ut=null);ct&&(ct.terminate(),ct=null);st&&(st.StopProcess(),st=null);Ce&&(Ce.stopRendering(),Ce.CleanScreen(0,0,0,0),Ce.terminate(),Ce=null);return ae=0,ie=0,Te=0,y=null,h=null,Ke=!1,S=!1,je=0,Ye=0,qe=0,Xe=!1,rt=-1,ot=!1,We=!1,mt=!1,ft=0,pt=0,dt=!1,r}()},FrameDataCallBack:function(e,t,n,o,a,i){var l={};de||(pe=new ArrayBuffer(292),de=new Uint8Array(pe),me=new DataView(pe)),de.set(Module.HEAPU8.subarray(i,i+292)),l.nFrameType=me.getInt32(0,!0),l.nFrameID=me.getInt32(4,!0),l.nFrameSubType=me.getInt32(56,!0),l.nYear=me.getUint16(40,!0),l.nMonth=me.getUint16(42,!0),l.nDay=me.getUint16(46,!0),l.nHour=me.getUint16(48,!0),l.nMinute=me.getUint16(50,!0),l.nSecond=me.getUint16(52,!0);var u=Date.UTC(l.nYear,l.nMonth,l.nDay,l.nHour,l.nMinute,l.nSecond)/1e3;if(1==l.nFrameType)if(l.nRemainData=me.getInt32(36,!0),l.bThrowFrame=me.getUint8(120,!0),0==l.bThrowFrame){if(l.nEncodeType=me.getInt32(108,!0),l.nStreamType=me.getInt32(112,!0),l.nTimeStamp=me.getUint32(8,!0),8==l.nStreamType?r.timestamp=u:r.timestamp=l.nTimeStamp/1e3,r.timestamp_usec=0,2==l.nEncodeType||4==l.nEncodeType||8==l.nEncodeType?Ie=1:12==l.nEncodeType&&(Ie=2),l.nWidth=me.getInt32(12,!0),l.nHeight=me.getInt32(16,!0),0==l.nWidth||0==l.nHeight)return;if(l.nFrameRate=me.getInt32(20,!0),l.nStride=me.getInt32(116,!0),18==l.nFrameSubType||19==l.nFrameSubType||20==l.nFrameSubType?le=1:0==l.nFrameSubType&&(le=0),(1==Ie&&1==ke||2==Ie&&1==Ve)&&!le&&13!=l.nStreamType)ge=new ArrayBuffer(a),(ne=new Uint8Array(ge)).set(Module.HEAPU8.subarray(t,t+a)),Kt(ne,null,null,a,l);else{if(0==t||0==n||0==o)return;if(!Ue){l.nWidth==ae&&l.nHeight==ie&&null!=ve||(he=null,Se=null,ye=null,ve=null,_e=null,Pe=null,he=new ArrayBuffer(l.nWidth*l.nHeight),ve=new Uint8Array(he),Se=new ArrayBuffer(l.nWidth*l.nHeight/4),_e=new Uint8Array(Se),ye=new ArrayBuffer(l.nWidth*l.nHeight/4),Pe=new Uint8Array(ye));var s=0;for(s=0;s<l.nHeight;s++)ve.set(Module.HEAPU8.subarray(t+s*l.nStride,t+s*l.nStride+l.nWidth),s*l.nWidth);for(s=0;s<l.nHeight/2;s++)_e.set(Module.HEAPU8.subarray(n+s*l.nStride/2,n+s*l.nStride/2+l.nWidth/2),s*l.nWidth/2);for(s=0;s<l.nHeight/2;s++)Pe.set(Module.HEAPU8.subarray(o+s*l.nStride/2,o+s*l.nStride/2+l.nWidth/2),s*l.nWidth/2)}Kt(ve,_e,Pe,a,l)}}else Kt(null,null,null,0,l);else if(2==l.nFrameType){if(l.nTotalChannel=me.getInt32(68,!0),l.nCurChannel=me.getInt32(72,!0),l.nCurChannel>0)return;l.nBits=me.getInt32(28,!0),l.nSamples=me.getInt32(32,!0),l.nAudioChnNum=me.getInt32(24,!0);var c=new ArrayBuffer(a),f=new Uint8Array(c);f.set(Module.HEAPU8.subarray(t,t+a)),Kt(f,null,null,a,l)}ge=null,ne=null},DecryptionResultCallBack:function(e,t,n){n,z(n)},RecordDataCallBack:function(e,n,r,o,a){nt+=r,$e||(Qe=new ArrayBuffer(292),$e=new Uint8Array(Qe),et=new DataView(Qe)),$e.set(Module.HEAPU8.subarray(a,a+292));var i=et.getInt32(0,!0),l=et.getInt32(4,!0);et.getInt32(56,!0);if(1==i){t.log("RecordDataCallBack, nPort:"+e+", nFrameID:"+l);et.getInt32(76,!0),et.getInt32(80,!0),et.getUint32(8,!0);var u=et.getUint16(40,!0),s=fn(et.getUint16(42,!0)),c=fn(et.getUint16(46,!0)),f=fn(et.getUint16(48,!0)),p=fn(et.getUint16(50,!0)),d=fn(et.getUint16(52,!0)),m=Date.UTC(u,s,c,f,p,d)/1e3,g=new Date("".concat(u,"-").concat(s,"-").concat(c," ").concat(f,":").concat(p,":").concat(d)).getTime()}N&&N({frameType:i,timeStamp:g,utcTimeStamp:m,length:nt});var h=new ArrayBuffer(r),S=new Uint8Array(h);S.set(Module.HEAPU8.subarray(n,n+r)),Ue&&Ze.postMessage({type:"addBuffer",buffer:S,offset:o,recordType:tt}),h=null,S=null},IVSDataCallBack:function(e,n,r,o,a){t.log("IVSDataCallBack, nPort:"+e+", nType:"+r+", nLen:"+o+", nFrameID:"+a),Xt(e,n,r,o,a)},setCallback:function(e,n){switch(t.log("setCallback, type:"+e+", func:"+n),e){case"GetPlayPort":Z=n;break;case"PlayStart":Ce.setBeginDrawCallback(n),s&&s.setBeginDrawCallback(n),L=n;break;case"DecodeStart":O=n;break;case"VideoFrameInfo":F=n;break;case"AudioFrameInfo":B=n;break;case"RecordTimeStamp":N=n;break;case"GetOriginalKey":W=n;break;case"DecryptionResult":z=n;break;case"Error":X=n;break;case"loadingBar":q=n;break;case"audioTalk":case"videoCapture":J=n;break;case"StreamPlayOver":Y=n;break;case"ARTagInfo":j=n;break;case"CapturePicDataCallBack":K=n,s&&s.setCapturePicDataCallBack(n)}},GetCurrentPlayTime:function(){return r.timestamp},SetSTFrameCallback:function(e){Q=e},setPrintLogLevel:function(e){if(Ht=e,Ue)Module._PLAY_SetPrintLogLevel(e);else{var t={nType:"setPrintLogLevel",nLogLevel:e};lt&&lt.postMessage(t)}Ce&&Ce.SetPrintLogLevel(e)},SetPlayMethod:function(e,t,n){Ce&&Ce.SetPlayMethod(e,t,n)}};var Zt,Qt,$t,en,tn,nn,rn,on,an,ln,un,sn,cn=(Zt=window,Qt=Zt.document,$t=function(){return Zt.URL||Zt.webkitURL||Zt},en=Qt.createElementNS("http://www.w3.org/1999/xhtml","a"),tn="download"in en,nn=/constructor/i.test(Zt.HTMLElement),rn=/CriOS\/[\d]+/.test(navigator.userAgent),on=function(e){(Zt.setImmediate||Zt.setTimeout)((function(){throw e}),0)},an=function(e){setTimeout((function(){"string"==typeof e?$t().revokeObjectURL(e):e.remove()}),4e4)},ln=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},sn=(un=function(e,t,n){n||(e=ln(e));var r,o=this,a="application/octet-stream"===e.type,i=function(){!function(e,t,n){for(var r=(t=[].concat(t)).length;r--;){var o=e["on"+t[r]];if("function"==typeof o)try{o.call(e,n||e)}catch(e){on(e)}}}(o,"writestart progress write writeend".split(" "))};if(o.readyState=o.INIT,tn)return r=$t().createObjectURL(e),void setTimeout((function(){en.href=r,en.download=t,en.dispatchEvent(new MouseEvent("click")),i(),an(r),o.readyState=o.DONE}));!function(){if((rn||a&&nn)&&Zt.FileReader){var t=new FileReader;return t.onloadend=function(){var e=rn?t.result:t.result.replace(/^data:[^;]*;/,"data:attachment/file;");Zt.open(e,"_blank")||(Zt.location.href=e),e=void 0,o.readyState=o.DONE,i()},t.readAsDataURL(e),void(o.readyState=o.INIT)}r||(r=$t().createObjectURL(e)),a?Zt.location.href=r:Zt.open(r,"_blank")||(Zt.location.href=r),o.readyState=o.DONE,i(),an(r)}()}).prototype,"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=ln(e)),navigator.msSaveOrOpenBlob(e,t)}:(sn.readyState=sn.INIT=0,sn.WRITING=1,sn.DONE=2,sn.error=sn.onwritestart=sn.onprogress=sn.onwrite=sn.onabort=sn.onerror=sn.onwriteend=null,function(e,t,n){return null==t||null==t?null:new un(e,t||e.name||"download",n)}));function fn(e){return e<10?"0".concat(e):e}function pn(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"utf-8",n=new TextDecoder(t),r=new Uint8Array(e),o=0;o<r.length&&0!==r[o];)o++;return n.decode(e.slice(0,o))}return new Ot};t.default=function(e){var t=new L,n=null,r=null,o=!1,a=!1,i=null,l=null,u=!0,s=!1,c=!1,f=0,p=!1,d=1,m=e,g=0,h=!1,S="",y=0,v="",P="",E=null,A=null,b=0,C=!1,T=1,M=0,D=!1,R=!1,I=!1,U=0,k={Disconnect:function(){},GetPlayPort:function(){},PlayStart:function(){},DecodeStart:function(){},VideoFrameInfo:function(){},AudioFrameInfo:function(){},RecordTimeStamp:function(){},GetOriginalKey:function(){},DecryptionResult:function(){},PlayBackStreamRange:function(){},StreamPlayOver:function(){},StreamRedirect:function(){},ARTagInfo:function(){},CapturePicDataCallBack:function(){},Error:function(){}};function H(){}function O(e,a){if(function(){null!==E&&(E.onerror=null);null!==E&&E.readyState===E.OPEN&&(E.close(),E=null)}(),r.SetAudioTalkFlag(e.bTalkService),r.ResetPlayState(),null!=e.bBroadcast&&(R=e.bBroadcast),null!=a&&(I=a),m||I)null==i&&(i=new w);else{v=e.strUserName,P=e.strPassWord,S=e.strDeviceID;var s=e.strRtspvUrl.indexOf("&encrypt=");for(var c in-1!=s&&(y=e.strRtspvUrl.slice(s+9,s+10)),null==n&&(n=new _(t)),k)n&&n.setCallback(c,k[c])}var f=e.strRtspvUri;-1!=e.strRtspvUrl.indexOf("rtsp://")?(u=!0,-1!=e.strRtspvUri.indexOf("?")?e.strRtspvUri+="&rtspoverwebsocket":e.strRtspvUri+="/rtspoverwebsocket"):(u=!1,-1!=e.strRtspvUri.indexOf("?")?e.strRtspvUri+="&httpprivateoverwebsocket":e.strRtspvUri+="/httpprivateoverwebsocket");var p=e.nShortTimeout||3,d=e.nRtspResponseTimeout||8;if(m||I){if(!u&&-1==e.strRtspvUrl.indexOf("http://")){s=f.indexOf("://");f=f.slice(s),e.strRtspvUrl="http"+f+e.strRtspvUrl}e.strSourceId.length>0&&(e.strRtspvUrl+="?sourceId=",e.strRtspvUrl+=e.strSourceId),l=i.StartStream(e,u),i.SetMsgWaitTimeout(p),R||function(e){g=setTimeout((function(){g&&(clearTimeout(g),g=0),k.Error({errorCode:"409",description:"Rtsp Not Response"})}),1e3*e)}(d)}else{var h={bTalkService:e.bTalkService,bBroadcast:R,nRange:e.nRange,bPlayBack:o,bRtspFlag:u,nShortTimeout:p,nRtspResponseTimeout:d};n.connect(e.strRtspvUri,e.strRtspvUrl,e.strSourceId,h),n.setUserInfo(e.strUserName,e.strPassWord),n.setRtpDataCallback(V),n.setStreamFinishCallback(W)}return r&&r.SetStreamOver(!1),l}function F(){if(m||I?l&&(g&&(clearTimeout(g),g=0),i.StopStream(),i=null,l=null):(G("TEARDOWN"),n&&n.disconnect(),n=null),a)return a=!1,r&&r.StopRecord()}function B(e){e!=c&&(c=e,m||I?l&&i.PauseStream(e):G(e?"PAUSE":"PLAY"))}function G(e,t,r){var o;o="video"===t?{command:e,range:r||0}:{command:e,data:t},n&&n.controlPlayer(o)}function x(){s||(setTimeout((function(){r&&(f=r.GetSourceBufferRemain())}),1),o&&(f>7340032?B(!0):f<2097152&&B(!1)))}function V(e){h||(h=!0,function(){if(!m&&!I){var e=n&&n.GetSSRC(),t=n&&n.GetSdpInfo(),o={};if(o.sdpInfo=t,o.strUserName=v,o.strPassWord=P,0==y)return;1==y&&0==e.videoSSRC||(o.nSsrc=e.videoSSRC,r.SetWebSecurityKey(y,1,S,o)),1==y&&(0!=e.audio1SSRC&&(o.nSsrc=e.audio1SSRC,r.SetWebSecurityKey(y,21,S,o)),0!=e.audio2SSRC&&(o.nSsrc=e.audio2SSRC,r.SetWebSecurityKey(y,22,S,o)))}}()),!o&&f>7340032||r&&r.InputData(e)}function N(){x()}function W(){r&&r.SetStreamOver(!0)}function z(e,t){m||I?l&&i.PutStream(e,t):n&&n.sendRtpData(e)}function Y(e){if(!C){var t=r.GetOriginalKey(e.data,A,null);if(t.length>0&&-1!==t.indexOf("data")){b&&(clearTimeout(b),b=0),C=!0;var n=t.indexOf('"deviceIp":'),o=t.indexOf('",',n),a=t.slice(n+12,o);n=t.indexOf('"devicePort":'),o=t.indexOf('",',n);var i=t.slice(n+14,o);n=t.indexOf('"userName":'),o=t.indexOf('"}',n);var l=t.slice(n+12,o);n=t.indexOf('"devicePassword":'),o=t.indexOf('",',n);var u=t.slice(n+18,o);n=t.indexOf('"id":'),o=t.indexOf(",",n);var s=t.slice(n+5,o),c={strRtspvUri:"wss://"+a+":"+i,strRtspvUrl:"rtsp://"+l+":"+u+"@"+a+":"+i+"/cam/realmonitor?channel="+T+"&subtype="+M+"&proto=Private3",strSourceId:"",strUserName:l||"admin",strPassWord:u||"admin123",strDeviceID:s,bTalkService:D,nRange:0,nShortTimeout:3,nRtspResponseTimeout:8};setTimeout((function(){var e=O(c);k.GetStreamClinetHandle(e)}),1)}}}return H.prototype={Init:function(e,n){return function(e,n){null!=n&&(T=n.nChannel,M=n.nSubType,D=n.bTalkService);o=e.bPlayBack,null==r&&(r=new We(m,t),0<U&&r.setPrintLogLevel(U));var a=r.Init(e);if(a>0){var i=r.GetPlayPort();m&&k.GetPlayPort(i)}for(var l in k)r.setCallback(l,k[l]);return r.SetSTFrameCallback(N),a}(e,n)},SetCacheMode:function(e){return function(e){return r&&r.SetCacheMode(e)}(e)},StartPullStream:function(e,t){return O(e,t)},StopPullStream:function(){F()},Pause:function(e){!function(e){s=e,B(e),r.Pause(e)}(e)},Stop:function(){r.CloseIVSDraw(),r.Stop(),r=null,h=!1,s=!1},SetSecurityKey:function(e,t,n,o,a){!function(e,t,n,o,a){r.SetSecurityKey(e,t,n,o,a)}(e,t,n,o,a)},SetSpeed:function(e){d=e,function(e){m||I?l&&i.PlayControl(-1,-1,e):G("SCALE",e);r.SetPlaySpeed(e)}(e)},SetSoundState:function(e){!function(e){r.SetSoundState(e)}(e)},SetVolume:function(e){!function(e){r.SetVolume(e)}(e)},StartRecord:function(e,t,n){return function(e,t,n){return a=!0,r.StartRecord(e,t,n)}(e,t,n)},StopRecord:function(){return a=!1,r.StopRecord()},OpenIVS:function(e){return function(e){return r.OpenIVSDraw(e)}(e)},CloseIVS:function(){return r.CloseIVSDraw()},SetIvsEnable:function(e,t){return function(e,t){return r.SetIvsEnable(e,t)}(e,t)},SetPanoAR:function(e,t){return function(e,t){return r.SetPanoAR(e,t)}(e,t)},Set3DPoint:function(e){return function(e){return r.Set3DPoint(e)}(e)},OnMouseDown:function(){return r.OnMouseDown()},OnMouseMove:function(e,t){return function(e,t){return r.OnMouseMove(e,t)}(e,t)},OnMouseUp:function(){return r.OnMouseUp()},OnMouseWheel:function(e){return function(e){return r.OnMouseWheel(e)}(e)},GetVRCoord2DTrans:function(e,t){return function(e,t){return r.GetVRCoord2DTrans(e,t)}(e,t)},GetVRCoord3DTrans:function(e,t){return function(e,t){return r.GetVRCoord3DTrans(e,t)}(e,t)},CapturePic:function(e){!function(e){r.capturePic(e)}(e)},StartTalk:function(e){!function(e){r.setCallback("audioTalk",z),r.StartTalk(e)}(e)},StopTalk:function(){r.StopTalk()},StartVideoCapture:function(e,t,n){!function(e,t,n){r.setCallback("audioTalk",z),r.StartVideoCapture(e,t,n)}(e,t,n)},StopVideoCapture:function(){r.StopVideoCapture()},SetSeekTime:function(e){!function(e){m||I?l&&i.PlayControl(e,-1,d):G("PLAY_SEEK",e);r.ResetBuffer()}(e)},OpenPlayGroup:function(){return r.OpenPlayGroup()},AddToPlayGroup:function(e,t){return function(e,t){return r.AddToPlayGroup(e,t)}(e,t)},DelFromPlayGroup:function(e,t){return function(e,t){return r.DelFromPlayGroup(e,t)}(e,t)},ClosePlayGroup:function(e){return function(e){return r.ClosePlayGroup(e)}(e)},PausePlayGroup:function(e,t){return function(e,t){return B(t),r.PausePlayGroup(e,t)}(e,t)},SetPlayGroupSpeed:function(e,t){return d=t,function(e,t){m||I?l&&i.PlayControl(-1,-1,t):G("SCALE",t);return r.SetPlayGroupSpeed(e,t)}(e,t)},GetSourceBufferRemain:function(){return f=r.GetSourceBufferRemain()},SetDecodeMode:function(e,t){!function(e,t){r.SetDecodeMode(e,t)}(e,t)},GetOriginalKey:function(e){!function(e){r.GetOriginalKey(e.strPlayToken,e.strPlayTokenKey,e.strDeviceID)}(e)},InputData:function(e){return function(e){return r&&r.InputData(e)}(e)},UserVerify:function(e,t,n,r,o){!function(e,t,n,r,o){A=o,(E=new WebSocket("wss://"+e+":"+t+"/ar/device/detail")).binaryType="arraybuffer",E.addEventListener("message",Y,!1),E.onopen=function(){var e='{\n    "id":'+n+',\n    "token":"'+r+'"\n}';E.send(e),C=!1,function(){b&&(clearTimeout(b),b=0);b=setTimeout((function(){k.Error({errorCode:"408",description:"Device verify failed"})}),5e3)}()},E.onerror=function(e){k.Error({errorCode:205,description:"WebSocket Verify Error"})}}(e,t,n,r,o)},SetCallBack:function(e,t){k[e]=t},SetPrintLogLevel:function(e){!function(e){U=e,t.setPrintLogLevel(e),r&&r.setPrintLogLevel(e)}(e)},SetPlayMethod:function(e,t,n){!function(e,t,n){r&&r.SetPlayMethod(e,t,n)}(e,t,n)},InputDataEx:function(e,t){if(g&&(clearTimeout(g),g=0),o&&!p){p=!0;var n=i.GetPlayInfo();k.PlayBackStreamRange(n)}var a=new ArrayBuffer(t),l=new Uint8Array(a);return l.set(SCModule.HEAPU8.subarray(e,e+t)),r&&r.InputData(l)},SetFrameData:function(e,t,n,o,a,i){x(),r.FrameDataCallBack(e,t,n,o,a,i)},SetDecryptionResult:function(e,t,n){r.DecryptionResultCallBack(e,t,n)},SetRecordData:function(e,t,n,o,a){r.RecordDataCallBack(e,t,n,o,a)},SetIVSDrawData:function(e,t,n,o,a){r.IVSDataCallBack(e,t,n,o,a)},StreamRedirectCallback:function(e){!function(e){F();var t=new ArrayBuffer(1e3),n=new Uint8Array(t);n.set(SCModule.HEAPU8.subarray(e,e+1e3));var r=function(e){for(var t="",n=0;n<e.length;n++){var r=String.fromCharCode(e[n]);if(t+=r,"\0"==r)break}return t}(n);k.StreamRedirect(r)}(e)},StreamDisconnectCallback:function(){k.Disconnect()},StreamFinishCallback:function(){W()},StreamFailedCallback:function(e){285868036!=e&&6500424!=e||(g&&(clearTimeout(g),g=0),k.Error({errorCode:"408",description:"Short Request Timeout"}))}},new H}}]).default}));