var __defProp=Object.defineProperty,__defNormalProp=(e,t,s)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,__publicField=(e,t,s)=>(__defNormalProp(e,"symbol"!=typeof t?t+"":t,s),s);!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).WSPlayer={})}(this,(function(e){"use strict";const t={websocketPorts:{realmonitor:"realmonitor-websocket",playback:"playback-websocket",realmonitor_ws:"9100",playback_ws:"9320",realmonitor_wss:"9102",playback_wss:"9322"},errorVideoInfo:{101:"当前浏览器不支持硬解码",102:"硬解码播放失败",103:"硬解码播放延时超过8秒",104:"硬解码失败",201:"当前音频无法播放",202:"websocket连接错误",203:"文件播放完成",401:"该用户无操作权限",404:"请求超时或未找到",405:"播放超时",406:"视频流类型解析失败，请检查通道配置",407:"请求超时",408:"请求超时或码流不支持",409:"请求超时或码流不支持",410:"视频流异常断开",411:"录像播放完成",457:"时间设置错误",503:"WS连接地址错误，非当前连接的ICC服务器返回",504:"对讲失败",701:"Chrome版本低，请升级到最新的Chrome版本",702:"Firefox版本低，请升级到最新的Firefox版本",703:"Edge版本低，请升级到最新的Edge版本",defaultErrorMsg:"播放失败，请检查配置"},errorInfo:{101:"所选通道离线，无法播放",102:"登录权限过期，查询实时预览rtsp失败",103:"操作失败，请稍后重试",104:"操作失败，请稍后重试",105:"通道休眠，正在唤醒中，请稍后再试",106:"请传入rtsp地址和websocket地址",107:"当前未传records录像文件, 将无法拖动进度条",108:"请求超时，请稍后重试",201:"所选通道未查询到录像文件",202:"查询录像文件列表失败",203:"查询录像rtsp失败",204:"{0}倍速无法播放音频",205:"通道休眠，正在唤醒中，请稍后再试",206:"当前倍速不支持",301:"正在对讲，无法关闭音频",302:"其他设备对讲中，无法开启音频",303:"其他设备对讲中，无法开启对讲",304:"查询对讲rtsp失败",305:"http协议不支持对讲",306:"设备对讲失败",307:"不支持PCM音频格式对讲",308:"不支持ARM音频格式对讲",309:"不支持G711u音频格式对讲",310:"不支持G726音频格式对讲",311:"不支持AAC音频格式对讲",312:"不支持G722音频格式对讲",501:"解码库未初始化完成，请稍后播放！",502:"解码库未初始化完成，请稍后对讲！",503:"请检查创建播放器时，播放器容器是否存在",601:"所操作播放器不存在",602:"所选播放器正在本地录像中，不可重复本地录像",603:"所选播放器未播放录像，不可本地录像",604:"所选播放器未开始本地录像，不可操作关闭本地录像",605:"时间跳转播放传参错误",606:"设置自适应拉伸传参错误",607:"实时预览不支持倍速播放",608:"需传入正确的{0}方法：{1}",610:"操作自定义窗口布局失败：{0}",611:"实时预览不支持播放方法",612:"实时预览不支持暂停方法",613:"实时预览不支持跳转播放",614:"当前窗口未播放实时预览画面, 无法进行对讲",701:"云台被用户{0}锁定，无法操作",702:"控制云台三维定位失败{0}",703:"控制云台{0}{1}失败{2}",704:"控制云台方向失败{0}"},windowDefaultCustomDivision:{2:[{lStep:0,tStep:0,wStep:50,hStep:100},{lStep:50,tStep:0,wStep:50,hStep:100}],3:[{wStep:50,hStep:100,tStep:0,lStep:0},{wStep:50,hStep:50,tStep:0,lStep:50},{wStep:50,hStep:50,tStep:50,lStep:50}],6:[{wStep:66,hStep:66,tStep:0,lStep:0,selectIndex:0},{wStep:34,hStep:33,tStep:0,lStep:66,selectIndex:1},{wStep:34,hStep:33,tStep:33,lStep:66,selectIndex:2},{wStep:33,hStep:34,tStep:66,lStep:0,selectIndex:3},{wStep:33,hStep:34,tStep:66,lStep:33,selectIndex:4},{wStep:34,hStep:34,tStep:66,lStep:66,selectIndex:5}],8:[{wStep:75,hStep:75,tStep:0,lStep:0,selectIndex:0},{wStep:25,hStep:25,tStep:0,lStep:75,selectIndex:1},{wStep:25,hStep:25,tStep:25,lStep:75,selectIndex:2},{wStep:25,hStep:25,tStep:50,lStep:75,selectIndex:3},{wStep:25,hStep:25,tStep:75,lStep:0,selectIndex:4},{wStep:25,hStep:25,tStep:75,lStep:25,selectIndex:5},{wStep:25,hStep:25,tStep:75,lStep:50,selectIndex:6},{wStep:25,hStep:25,tStep:75,lStep:75,selectIndex:7}]},iconConfig:[{key:"drawTriangle",label:"wsPlayer.drawTriangle",buttonParentClass:"ws-draw-triangle",buttonIconClass:"opt-icon draw-triangle-icon off"},{key:"talkIcon",label:"wsPlayer.talk",buttonParentClass:"ws-talk",buttonIconClass:"opt-icon talk-icon off"},{key:"localRecordIcon",label:"wsPlayer.recording",buttonParentClass:"ws-record",buttonIconClass:"opt-icon record-icon"},{key:"audioIcon",label:"wsPlayer.sound",buttonParentClass:"ws-audio",buttonIconClass:"opt-icon audio-icon off"},{key:"snapshotIcon",label:"wsPlayer.capture",buttonParentClass:"ws-capture",buttonIconClass:"opt-icon capture-icon"}],_translate(e,t,s){Object.keys(this[e]).forEach((i=>{let r=t.$t(`${s}${i}`);r&&(this[e][i]=r)}))},updateLocale(e){this._translate("errorInfo",e,"wsPlayer.error."),this._translate("errorVideoInfo",e,"wsPlayer.play.error."),this.iconConfig.forEach((t=>{t.label=e.$t(t.label)}))}},s={Opera:"Opera",Chrome:"Chrome",Firefox:"Firefox",Edge:"Edge",Edg:"Edg",IE:"IE",Safari:"Safari"};const i={checkBrowser:function(){const e=function(){const{userAgent:e}=navigator;return e.includes("Edge")?s.Edge:e.includes("Edg")?s.Edg:e.includes("Firefox")?s.Firefox:e.includes("Chrome")?s.Chrome:e.includes("Safari")?s.Safari:e.includes("compatible")&&e.includes("MSIE")&&e.includes("Opera")?s.IE:e.includes("Opera")?s.Opera:""}(),t=navigator.userAgent.includes("x64")||navigator.userAgent.includes("x86_64")?64:32,i=function(e){const{userAgent:t}=navigator;return t.split(e)[1].split(".")[0].slice(1)}(e);let r=!1,a=0;switch(e){case s.Chrome:r=i>=91&&64===t,a=701;break;case s.Firefox:r=i>=97,a=702;break;case s.Edge:case s.Edg:r=i>=91,a=703;break;default:r=0}return{isVersionCompliance:r,browserType:e,errorCode:a}},validFunction:function(e){return"[object Function]"===toString.call(e)},mergeObject:function e(){let t={};for(let i=0;i<arguments.length;i++){let r=arguments[i];for(let i in r){let a=r[i];"localeI18n"===i?t[i]=a:(s=a,"[object Object]"===toString.call(s)?t[i]=e(a):t[i]=a)}}var s;return t},getDateFormatByUnderline:function(){return function(){let e=new Date;return[e.getFullYear(),e.getMonth()+1,e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}().join("_")},throttle:function(e,t){let s;return function(){s||(e.apply(this,arguments),s=setTimeout((()=>{s=0}),t))}},debounce:function(e,t){let s;return function(){s&&clearTimeout(s),s=setTimeout((()=>{e.apply(this,arguments),s=0}),t)}}};var r=globalThis&&globalThis.__assign||function(){return r=Object.assign||function(e){for(var t,s=1,i=arguments.length;s<i;s++)for(var r in t=arguments[s])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},a={lines:12,length:7,width:5,radius:10,scale:1,corners:1,color:"#000",fadeColor:"transparent",animation:"spinner-line-fade-default",rotate:0,direction:1,speed:1,zIndex:2e9,className:"spinner",top:"50%",left:"50%",shadow:"0 0 1px transparent",position:"absolute"},l=function(){function e(e){void 0===e&&(e={}),this.opts=r(r({},a),e)}return e.prototype.spin=function(e){return this.stop(),this.el=document.createElement("div"),this.el.className=this.opts.className,this.el.setAttribute("role","progressbar"),n(this.el,{position:this.opts.position,width:0,zIndex:this.opts.zIndex,left:this.opts.left,top:this.opts.top,transform:"scale("+this.opts.scale+")"}),e&&e.insertBefore(this.el,e.firstChild||null),function(e,t){var s=Math.round(t.corners*t.width*500)/1e3+"px",i="none";!0===t.shadow?i="0 2px 4px #000":"string"==typeof t.shadow&&(i=t.shadow);for(var r=function(e){for(var t=/^\s*([a-zA-Z]+\s+)?(-?\d+(\.\d+)?)([a-zA-Z]*)\s+(-?\d+(\.\d+)?)([a-zA-Z]*)(.*)$/,s=[],i=0,r=e.split(",");i<r.length;i++){var a=r[i].match(t);if(null!==a){var l=+a[2],n=+a[5],o=a[4],c=a[7];0!==l||o||(o=c),0!==n||c||(c=o),o===c&&s.push({prefix:a[1]||"",x:l,y:n,xUnits:o,yUnits:c,end:a[8]})}}return s}(i),a=0;a<t.lines;a++){var l=~~(360/t.lines*a+t.rotate),d=n(document.createElement("div"),{position:"absolute",top:-t.width/2+"px",width:t.length+t.width+"px",height:t.width+"px",background:o(t.fadeColor,a),borderRadius:s,transformOrigin:"left",transform:"rotate("+l+"deg) translateX("+t.radius+"px)"}),h=a*t.direction/t.lines/t.speed;h-=1/t.speed;var p=n(document.createElement("div"),{width:"100%",height:"100%",background:o(t.color,a),borderRadius:s,boxShadow:c(r,l),animation:1/t.speed+"s linear "+h+"s infinite "+t.animation});d.appendChild(p),e.appendChild(d)}}(this.el,this.opts),this},e.prototype.stop=function(){return this.el&&("undefined"!=typeof requestAnimationFrame?cancelAnimationFrame(this.animateId):clearTimeout(this.animateId),this.el.parentNode&&this.el.parentNode.removeChild(this.el),this.el=void 0),this},e}();function n(e,t){for(var s in t)e.style[s]=t[s];return e}function o(e,t){return"string"==typeof e?e:e[t%e.length]}function c(e,t){for(var s=[],i=0,r=e;i<r.length;i++){var a=r[i],l=d(a.x,a.y,t);s.push(a.prefix+l[0]+a.xUnits+" "+l[1]+a.yUnits+a.end)}return s.join(", ")}function d(e,t,s){var i=s*Math.PI/180,r=Math.sin(i),a=Math.cos(i);return[Math.round(1e3*(e*a+t*r))/1e3,Math.round(1e3*(-e*r+t*a))/1e3]}class h{constructor(e){this.$el=null,this.canvasElem=null,this.videoElem=null,this.wrapperDomId=e.wrapperDomId,this.i18n=e.locale,this.domId=e.wrapperDomId+"-"+e.index,this.wsPlayer=e.wsPlayer,this.index=e.index,this.initIndex=e.index,this.firstTime=0,this.isAudioPlay=!1,this.speed=1}initDom(){let e=this.getTemplate(),t=WSPlayerJQ.$(e);this.wsPlayer.$wrapper.append(t[0]),this.$el=WSPlayerJQ.$("#"+this.domId),this.canvasElem=document.getElementById(this.canvasId)||{},this.ivsCanvasElem=document.getElementById(this.ivsCanvasId)||{},this.pztCanvasElem=document.getElementById(this.pztCanvasId)||{},this.videoElem=document.getElementById(this.videoId),this.showIcons=this.wsPlayer.config.showIcons||{},this.showIconNum=6,this.showIcons.streamChangeSelect||this.setDomHiddenDeep(WSPlayerJQ.$(".select-container",this.$el),!1),this.showIcons.ivsIcon||this.setDomHiddenDeep(WSPlayerJQ.$(".ws-draw-triangle",this.$el),!1),this.setTalkIconShow(),this.showIcons.audioIcon||(this.setDomHiddenDeep(WSPlayerJQ.$(".ws-audio",this.$el),!1),this.showIconNum--),this.showIcons.snapshotIcon||(this.setDomHiddenDeep(WSPlayerJQ.$(".ws-capture",this.$el),!1),this.showIconNum--),this.showIcons.localRecordIcon||(this.setDomHiddenDeep(WSPlayerJQ.$(".ws-record",this.$el),!1),this.showIconNum--),this.showIcons.closeIcon||(this.setDomHiddenDeep(WSPlayerJQ.$(".ws-close",this.$el),!1),this.showIconNum--),this.showMoreIcon=!1}setTalkIconShow(){let{talkIcon:e}=this.wsPlayer.config.showIcons||{},t=location.protocol;e&&"https:"===t&&"real"===this.wsPlayer.type?WSPlayerJQ.$(".ws-talk",this.$el).css({display:""}):(this.setDomHiddenDeep(WSPlayerJQ.$(".ws-talk",this.$el),!1),this.showIconNum--)}initMouseEvent(){this.$el.click((e=>{this.wsPlayer.setSelectIndex(this.index),this.$el.siblings().removeClass("selected").addClass("unselected"),this.$el.removeClass("unselected").addClass("selected")})),this.$el.dblclick((e=>{if(1!==this.wsPlayer.options.config.maxNum){if(1!==this.wsPlayer.showNum){this.wsPlayer.beforeShowNum=this.wsPlayer.showNum;let e=WSPlayerJQ.$(`#${this.wrapperDomId} .wsplayer-item`);this.wsPlayer.resetPlayerScreen(e,this.wsPlayer.options.config.maxNum),WSPlayerJQ.$(`#${this.wrapperDomId}-${this.index}`).css({top:0,left:0,width:"100%",height:"100%",visibility:"visible"}),this.wsPlayer.showNum=1}else this.wsPlayer.setPlayerNum(this.wsPlayer.beforeShowNum,!0);this.wsPlayer.setSelectIndex(this.index),this.$el.siblings().removeClass("selected").addClass("unselected"),this.$el.removeClass("unselected").addClass("selected"),setTimeout((()=>{this.wsPlayer.__updatePlayerWindow()}),200)}})),WSPlayerJQ.$(".ws-audio",this.$el).click((e=>{if(this.wsPlayer.isTalking&&this.isTalking&&this.gbTalk)console.log("当前为国标对讲，不控制音量");else if(this.wsPlayer.isTalking)return void this.wsPlayer.sendErrorMessage(this.isTalking?"301":"302");if(this.isAudioPlay)this.closeVolume();else{if(this.playSDK.isPlayback&&1!==Number(this.speed))return void this.wsPlayer.sendErrorMessage("204",{insert:[this.speed]});this.openVolume()}})),WSPlayerJQ.$(".ws-draw-triangle",this.$el).click((e=>{e.target.getAttribute("class").includes("off")?(WSPlayerJQ.$(".ws-draw-triangle",this.$el).removeClass("off").addClass("on"),this.playSDK.OpenIVS(this.options.selectIndex),this.wsPlayer.config.ivsTypeArr.includes(1)||this.playSDK.SetIvsEnable(3,0),this.wsPlayer.config.ivsTypeArr.includes(2)||(this.playSDK.SetIvsEnable(1,0),this.playSDK.SetIvsEnable(14,0))):(WSPlayerJQ.$(".ws-draw-triangle",this.$el).removeClass("on").addClass("off"),this.playSDK.CloseIVS())}));WSPlayerJQ.$(".ws-talk",this.$el).click(i.throttle((e=>{if("http:"!==location.protocol)if(this.wsPlayer.isTalking&&!this.isTalking)this.wsPlayer.sendErrorMessage("303");else if(this.isTalking)this.stopTalk();else{if(this.options&&"url"===this.options.playType){const{selectIndex:e,channelData:t}=this.options;return void this.wsPlayer.sendMessage("notifyTalk",{selectIndex:e,channelData:{...t}})}this.resumeAudio(),this.setAuthority({channelCode:this.options.channelData?this.options.channelData.channelCode:this.options.channelId,function:"3"},(()=>{this.wsPlayer.talkIndex=this.index,this.wsPlayer.__startTalk(this.options.channelData)}),(e=>{1103===e.code&&this.wsPlayer.sendErrorMessage(401,{selectIndex:selectIndex,channelData:{...channelData},type:"talk"})}))}else this.wsPlayer.sendErrorMessage("305")}),2e3)),WSPlayerJQ.$(".ws-capture",this.$el).click((e=>{this.setAuthority({channelCode:this.options.channelData?this.options.channelData.channelCode:this.options.channelId,function:"4"},(()=>{this.picCap()}),(e=>{1103===e.code&&this.wsPlayer.sendErrorMessage(401,{type:"capture"})}))})),WSPlayerJQ.$(".ws-close",this.$el).click((e=>{this.close()})),WSPlayerJQ.$(".ws-record",this.$el).click((e=>{let t=(this.options.channelData||{}).name||this.i18n.$t("wsPlayer.recording");this.isRecording?(this.isRecording=!1,this.playSDK.StopRecord(),WSPlayerJQ.$(e.target).removeClass("recording")):"playing"===this.status&&this.setAuthority({channelCode:this.options.channelData?this.options.channelData.channelCode:this.options.channelId,function:"8"},(()=>{this.isRecording=!0,this.startRecord(`${this.i18n.$t("wsPlayer.video")}_${t}_${i.getDateFormatByUnderline()}`),WSPlayerJQ.$(e.target).addClass("recording")}),(e=>{1103===e.code&&this.wsPlayer.sendErrorMessage(401,{type:"record"})}))})),WSPlayerJQ.$(".more-icon",this.$el).click((e=>{this.showMoreIcon=!this.showMoreIcon,this.setDomVisible(WSPlayerJQ.$(".opt-icons-vertical",this.$el),this.showMoreIcon)}))}setLoading(e,t){this.spinner&&this.spinner.stop(),t&&this.setStatus(t),e&&(this.spinner=new l({color:"#ffffff"}).spin(this.$el[0]))}picCap(e){let t=e;if(!t){let e=(this.options.channelData||{}).name||"";t=`${this.i18n.$t("wsPlayer.capture")}_${e}_${i.getDateFormatByUnderline()}`}this.playSDK.CapturePic(this.wsPlayer.config.picCapCb?null:t+".png")}startRecord(e,t,s){const{downloadMp4Record:i,localRecordSize:r}=this.wsPlayer.config;let a="boolean"==typeof s?s:i;a=!localStorage.playSDKLogLevel&&a;let l=t||r;this.playSDK.StartRecord(a?5:0,l,`${e}.${a?"mp4":"dav"}`)}closeOtherAudio(){this.wsPlayer.playerList.forEach((e=>{e.isAudioPlay&&e.closeVolume()}))}openVolume(e=1){this.closeOtherAudio(),this.playSDK.SetSoundState(!0),this.playSDK.SetVolume(e>1?1:e),this.resumeAudio(),WSPlayerJQ.$(".ws-audio",this.$el).removeClass("off").addClass("on"),this.isAudioPlay=!0}closeVolume(){this.playSDK&&(this.playSDK.SetSoundState(!1),this.playSDK.SetVolume(0)),WSPlayerJQ.$(".ws-audio",this.$el).removeClass("on").addClass("off"),this.isAudioPlay=!1}setAuthority(e,t,s){this.wsPlayer.fetchChannelAuthority?this.wsPlayer.fetchChannelAuthority(e).then((e=>{e.data.result&&t()})).catch((e=>{s(e)})):t()}resumeAudio(){if(window.wsAudioPlayer)window.wsAudioPlayer.manualResume("fromTalk");else{let e=setInterval((()=>{window.wsAudioPlayer&&(window.wsAudioPlayer.manualResume("fromTalk"),clearInterval(e))}),100)}}setStatus(){}showMsgInWindow(e){this.setDomVisible(this.defaultStatus,!1),WSPlayerJQ.$(".wsplayer-error-message",this.$el).text(e),this.setDomVisible(this.error,!0)}play(){this.playSDK.SetSpeed(this.speed),this.playSDK.Pause(0),this.setStatus("playing"),WSPlayerJQ.$(".ws-record-play",this.wsPlayer.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-pause",this.wsPlayer.$el).css({display:""})}pause(){this.playSDK.Pause(1),this.setStatus("pause"),WSPlayerJQ.$(".ws-record-pause",this.wsPlayer.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-play",this.wsPlayer.$el).css({display:""})}close(e=!1,t=!1,s="closed"){if(this.currentWindowPlaying=!1,this.wsPlayer.videoClosed(this.index,e,{...this.options&&this.options.channelData||{}},this.customDomId),this.setDomVisible(WSPlayerJQ.$(".play-pause-wrapper",this.$el),!1),this.videoElem.style.display="none",this.canvasElem.style.display="none",this.customDomElem&&this.customDomElem.css("display","none"),this.isTalking&&!t&&this.stopTalk(),this.speed=1,this.index===this.wsPlayer.selectIndex&&("real"===this.wsPlayer.type?!e&&this.wsPlayer.setPtzChannel():(this.wsPlayer.setTimeLine([]),this.wsPlayer.__setPlaySpeed(),WSPlayerJQ.$(".ws-record-play",this.wsPlayer.$el).css({display:""}),WSPlayerJQ.$(".ws-record-pause",this.wsPlayer.$el).css({display:"none"}))),this.isRecording&&(this.isRecording=!1,this.playSDK.StopRecord(),WSPlayerJQ.$(".ws-record",this.$el).removeClass("recording")),this.wsPlayer.config.openIvs&&this.playSDK)try{this.playSDK.CloseIVS()}catch(i){console.debug("关闭规则线")}this.spinner&&this.spinner.stop(),WSPlayerJQ.$(".stream-info",this.$el).text(""),this.playSDK&&(this.playSDK.StopPullStream(),this.playSDK.Stop(),window.wsPlayerManager.unbindPlayer(this.playSDK.nPlayPort),window.wsPlayerManager.unbindHandlePlayer(this.playSDK.m_rtspvHandle)),e||(this.playSDK=null,this.options=null),this.setStatus(s)}setDomVisible(e,t){e&&e.css({visibility:t?"visible":"hidden"})}setDomDisplay(e,t){e&&e.css({display:t?"":"none"})}setDomHiddenDeep(e,t){e&&(t?e.removeClass("hidden-deep"):e.addClass("hidden-deep"))}setLogLevel(){this.playSDK.SetPrintLogLevel&&this.playSDK.SetPrintLogLevel(localStorage.playSDKLogLevel)}uint8ArrayToBase64(e){let t="",s=e.byteLength;for(let i=0;i<s;i++)t+=String.fromCharCode(e[i]);return window.btoa(t)}updateAdapter(e,t={}){let s=t.width/t.height,i="video"===(t.decodeMode||this.decodeMode)?this.videoElem:this.canvasElem,r=i.parentNode;t.decodeMode?(this.decodeMode=t.decodeMode,this.width=t.width,this.height=t.height):s=this.width/this.height;let a="100%",l="100%";if("selfAdaption"===e){let e=r.offsetHeight,t=r.offsetWidth,n=t/e;s>n?l=t/s+"px":s<n&&(a=e*s+"px"),"video"===this.decodeMode?(WSPlayerJQ.$(i).css({width:a,height:l,"object-fit":"contain"}),WSPlayerJQ.$(this.canvasElem).css({width:a,height:l,"object-fit":"contain"})):WSPlayerJQ.$(i).css({width:a,height:l}),WSPlayerJQ.$(this.ivsCanvasElem).css({width:a,height:l,"object-fit":"contain"}),WSPlayerJQ.$(this.pztCanvasElem).css&&WSPlayerJQ.$(this.pztCanvasElem).css({width:a,height:l,"object-fit":"contain"})}else WSPlayerJQ.$(i).css({width:a,height:l,"object-fit":"fill"}),"video"===this.decodeMode&&WSPlayerJQ.$(this.canvasElem).css({width:a,height:l,"object-fit":"fill"}),WSPlayerJQ.$(this.ivsCanvasElem).css({width:a,height:l,"object-fit":"fill"}),WSPlayerJQ.$(this.pztCanvasElem).css&&WSPlayerJQ.$(this.pztCanvasElem).css({width:a,height:l,"object-fit":"fill"});this.customDomElem&&this.customDomElem.css({width:a,height:l}),this.customDomElem&&this.wsPlayer.sendMessage("customDomInfo",{customDomId:this.customDomId,width:a,height:l,currentWindowPlaying:this.currentWindowPlaying||!1}),this.playSDK&&(this.ivsCanvasElem.width=i.offsetWidth,this.ivsCanvasElem.height=i.offsetHeight,this.pztCanvasElem.width=i.offsetWidth,this.pztCanvasElem.height=i.offsetHeight)}renderDefaultStats(){let e=WSPlayerJQ.$(".default-status",this.$el),t=e.width(),s=e.height(),i=WSPlayerJQ.$(".wsplayer-error-message",this.$el);t&&s&&(t<100||s<100?(e.css({transform:"scale(0.3)"}),i.css({transform:"scale(0.3)"})):t<200||s<200?(e.css({transform:"scale(0.5)"}),i.css({transform:"scale(0.5)"})):(e.css({transform:"scale(1)"}),i.css({transform:"scale(1)"})))}updateTopBarStyle(){let e=WSPlayerJQ.$(".top-control-bar",this.$el).width()-(this.showIcons.streamChangeSelect&&"real"===this.type?94:0)-10;if(e>28*this.showIconNum+119)this.setDomDisplay(WSPlayerJQ.$(".more-icon",this.$el),!1),this.setDomDisplay(WSPlayerJQ.$(".stream-info",this.$el),!0),t.iconConfig.forEach(((e,t)=>{this.setDomDisplay(WSPlayerJQ.$(`.opt-icons > .${e.buttonParentClass}`,this.$el),!0)}));else if(e>28*this.showIconNum)this.setDomDisplay(WSPlayerJQ.$(".more-icon",this.$el),!1),this.setDomDisplay(WSPlayerJQ.$(".stream-info",this.$el),!1),t.iconConfig.forEach(((e,t)=>{this.setDomDisplay(WSPlayerJQ.$(`.opt-icons > .${e.buttonParentClass}`,this.$el),!0)}));else{let s=Math.floor(e/28);this.setDomDisplay(WSPlayerJQ.$(".stream-info",this.$el),!1),this.setDomDisplay(WSPlayerJQ.$(".more-icon",this.$el),!0),t.iconConfig.forEach(((e,t)=>{s>t+2?(this.setDomDisplay(WSPlayerJQ.$(`.opt-icons > .${e.buttonParentClass}`,this.$el),!0),this.setDomDisplay(WSPlayerJQ.$(`.opt-icons-vertical > .${e.buttonParentClass}`,this.$el),!1)):(this.setDomDisplay(WSPlayerJQ.$(`.opt-icons > .${e.buttonParentClass}`,this.$el),!1),this.setDomDisplay(WSPlayerJQ.$(`.opt-icons-vertical > .${e.buttonParentClass}`,this.$el),!0))}))}}}const p={zhCN:{"common.close":"关闭","common.ok":"确定","common.cancel":"取消","common.reset":"重置","storagePlan.stream.main":"主码流","video.player.sub.stream":"辅码流{0}","video.player.ptz.down":"下","video.player.ptz.left":"左","video.player.ptz.lower.left":"左下","video.player.ptz.lower.right":"右下","video.player.ptz.right":"右","video.player.ptz.up":"上","video.player.ptz.upper.left":"左上","video.player.ptz.upper.right":"右上","inspec.ptz.position1":"三维定位","video.player.ptz.zoom":"变倍","video.player.ptz.focus":"聚焦","video.player.ptz.halo":"光圈","wsPlayer.proxy":"代理模式, 此模式不做自动处理","wsPlayer.recording":"录像","wsplayer.more":"更多","wsPlayer.request.realtime.preview.interface":"请求实时预览接口","wsPlayer.request.realtime.preview.interface.error.tip":"在传入的 {0} 方法上，resolve 返回的值应该为一个icc返回的标准对象","wsPlayer.request.recording.interface":"请求录像接口","wsPlayer.request.talk.interface":"请求对讲接口","wsPlayer.screen.custom.split":"自定义分屏","wsPlayer.screen.four":"四分屏","wsPlayer.screen.full":"全屏","wsPlayer.screen.nine":"九分屏","wsPlayer.screen.one":"单屏","wsPlayer.screen.selfAdaption":"自适应","wsPlayer.screen.split":"{0}分屏","wsPlayer.screen.stretching":"拉伸","wsPlayer.speed.sub":"倍速-","wsPlayer.speed.add":"倍速+","wsPlayer.drawTriangle":"智能规则线","wsPlayer.sound":"声音","wsPlayer.talk":"对讲","wsPlayer.talking":"对讲中...","wsPlayer.income.format.error":"传入格式错误，请重新传入","wsPlayer.one.click.off":"一键关闭","wsPlayer.pause":"暂停","wsPlayer.play":"播放","ws.device.awakening":"{0}通道正在唤醒中","wsPlayer.auto.recognition.failed":"自动识别失败，存在端口为非默认端口，开始通过参数判断，请注意传参！如果是证书问题请临时访问：{0}","wsPlayer.capture":"抓图","wsPlayer.video":"视频","wsPlayer.current.window.cannot.be.merged":"当前窗口无法被合并","wsPlayer.custom.player.max.support.tip":"自定义播放器只支持最大分割{0}个窗口","wsPlayer.drag.tip":"不能拖拽到当前位置，请重新拖拽","wsPlayer.play.error.101":"当前浏览器不支持硬解码","wsPlayer.play.error.102":"硬解码播放失败","wsPlayer.play.error.103":"硬解码播放延时超过8秒","wsPlayer.play.error.104":"硬解码失败","wsPlayer.play.error.201":"当前音频无法播放","wsPlayer.play.error.202":"websocket连接错误","wsPlayer.play.error.203":"文件播放完成","wsPlayer.play.error.401":"该用户无操作权限","wsPlayer.play.error.404":"请求超时或未找到","wsPlayer.play.error.405":"播放超时","wsPlayer.play.error.406":"视频流类型解析失败，请检查通道配置","wsPlayer.play.error.407":"请求超时","wsPlayer.play.error.408":"请求超时或码流不支持","wsPlayer.play.error.409":"请求超时或码流不支持","wsPlayer.play.error.410":"视频流异常断开","wsPlayer.play.error.411":"录像已播放完成","wsPlayer.play.error.457":"时间设置错误","wsPlayer.play.error.503":"WS连接地址错误，非当前连接的ICC服务器返回","wsPlayer.play.error.504":"对讲失败","wsPlayer.play.error.701":"Chrome版本低，请升级到最新的Chrome版本","wsPlayer.play.error.702":"Firefox版本低，请升级到最新的Firefox版本","wsPlayer.play.error.703":"Edge版本低，请升级到最新的Edge版本","wsPlayer.play.error.default":"播放失败，请检查配置","wsPlayer.error.101":"所选通道离线，无法播放","wsPlayer.error.102":"登录权限过期，查询实时预览rtsp失败","wsPlayer.error.103":"操作失败，请稍后重试","wsPlayer.error.104":"操作失败，请稍后重试","wsPlayer.error.105":"通道休眠，正在唤醒中，请稍后再试","wsPlayer.error.106":"请传入rtsp地址和websocket地址","wsPlayer.error.107":"当前未传records录像文件, 将无法拖动进度条","wsPlayer.error.108":"请求超时，请稍后重试","wsPlayer.error.201":"所选通道未查询到录像文件","wsPlayer.error.202":"查询录像文件列表失败","wsPlayer.error.203":"查询录像rtsp失败","wsPlayer.error.204":"{0}倍速无法播放音频","wsPlayer.error.205":"通道休眠，正在唤醒中，请稍后再试","wsPlayer.error.206":"当前倍速不支持","wsPlayer.error.301":"正在对讲，无法关闭音频","wsPlayer.error.302":"其他设备对讲中，无法开启音频","wsPlayer.error.303":"其他设备对讲中，无法开启对讲","wsPlayer.error.304":"查询对讲rtsp失败","wsPlayer.error.305":"http协议不支持对讲","wsPlayer.error.306":"设备对讲失败","wsPlayer.error.307":"不支持PCM音频格式对讲","wsPlayer.error.308":"不支持ARM音频格式对讲","wsPlayer.error.309":"不支持G711u音频格式对讲","wsPlayer.error.310":"不支持G726音频格式对讲","wsPlayer.error.311":"不支持AAC音频格式对讲","wsPlayer.error.312":"不支持G722音频格式对讲","wsPlayer.error.501":"解码库未初始化完成，请稍后播放！","wsPlayer.error.502":"解码库未初始化完成，请稍后对讲！","wsPlayer.error.503":"请检查创建播放器时，播放器容器是否存在","wsPlayer.error.601":"所操作播放器不存在","wsPlayer.error.602":"所选播放器正在本地录像中，不可重复本地录像","wsPlayer.error.603":"所选播放器未播放录像，不可本地录像","wsPlayer.error.604":"所选播放器未开始本地录像，不可操作关闭本地录像","wsPlayer.error.605":"时间跳转播放传参错误","wsPlayer.error.606":"设置自适应拉伸传参错误","wsPlayer.error.607":"实时预览不支持倍速播放","wsPlayer.error.608":"需传入正确的{0}方法：{1}","wsPlayer.error.609":"超出当前设置最大窗口布局数量","wsPlayer.error.610":"操作自定义窗口布局失败：{0}","wsPlayer.error.611":"实时预览不支持播放方法","wsPlayer.error.612":"实时预览不支持暂停方法","wsPlayer.error.613":"实时预览不支持跳转播放","wsPlayer.error.614":"当前窗口未播放实时预览画面, 无法进行对讲","wsPlayer.error.701":"云台被用户{0}锁定，无法操作","wsPlayer.error.702":"控制云台三维定位失败{0}","wsPlayer.error.703":"控制云台{0}{1}失败{2}","wsPlayer.error.704":"控制云台方向失败{0}",error2084:"您无权限进行此操作"},enUS:{"common.close":"Close","common.ok":"OK","common.cancel":"Cancel","common.reset":"Reset","storagePlan.stream.main":"Main Stream","video.player.sub.stream":"Sub Stream {0}","video.player.ptz.down":"Down","video.player.ptz.left":"Left","video.player.ptz.lower.left":"Bottom Left","video.player.ptz.lower.right":"Bottom Right","video.player.ptz.right":"Right","video.player.ptz.up":"Up","video.player.ptz.upper.left":"Top Left","video.player.ptz.upper.right":"Top Right","inspec.ptz.position1":"3D Positioning","video.player.ptz.zoom":"Zoom","video.player.ptz.focus":"Focus","video.player.ptz.halo":"Aperture","wsPlayer.proxy":"Proxy Mode, this mode does not perform automatic processing","wsPlayer.recording":"Recording","wsplayer.more":"More","wsPlayer.request.realtime.preview.interface":"Request Real-time Preview Interface","wsPlayer.request.realtime.preview.interface.error.tip":"On the input {0} method, the value resolved should be a standard object returned by icc.","wsPlayer.request.recording.interface":"Request Recording Interface","wsPlayer.request.talk.interface":"Request Intercom Interface","wsPlayer.screen.custom.split":"Custom Split Screen","wsPlayer.screen.four":"Quadrant View","wsPlayer.screen.full":"Full Screen","wsPlayer.screen.nine":"Nine-Quadrant View","wsPlayer.screen.one":"Single Screen","wsPlayer.screen.selfAdaption":"Adaptive","wsPlayer.screen.split":"{0}-Split Screen","wsPlayer.screen.stretching":"Stretch","wsPlayer.speed.sub":"Speed-","wsPlayer.speed.add":"Speed+","wsPlayer.sound":"Sound","wsPlayer.drawTriangle":"Smart Guideline Triangle","wsPlayer.talk":"Intercom","wsPlayer.talking":"Speaking...","wsPlayer.income.format.error":"Incorrect input format, please re-enter","wsPlayer.one.click.off":"One-Click Close","wsPlayer.pause":"Pause","wsPlayer.play":"Play","ws.device.awakening":"Channel {0} is awakening","wsPlayer.auto.recognition.failed":"Auto recognition failed, there is a non-default port. Beginning parameter judgment, please check the parameters!","wsPlayer.capture":"Snapshot","wsPlayer.current.window.cannot.be.merged":"The current window cannot be merged","wsPlayer.custom.player.max.support.tip":"The custom player only supports up to {0} windows","wsPlayer.drag.tip":"Cannot drag to this position, please re-drag","wsPlayer.play.error.101":"Current browser does not support hardware decoding","wsPlayer.play.error.102":"Hardware decoding playback failed","wsPlayer.play.error.103":"Hardware decoding playback delay exceeds 8 seconds","wsPlayer.play.error.104":"Hardware decoding failed","wsPlayer.play.error.201":"Current audio cannot be played","wsPlayer.play.error.202":"WebSocket connection error","wsPlayer.play.error.203":"File playback completed","wsPlayer.play.error.401":"User has no operation permission","wsPlayer.play.error.404":"Request timeout or not found","wsPlayer.play.error.405":"Playback timeout","wsPlayer.play.error.406":"Video stream type parsing failed, please check channel configuration","wsPlayer.play.error.407":"Request timeout","wsPlayer.play.error.408":"Request timeout or stream not supported","wsPlayer.play.error.409":"Request timeout or stream not supported","wsPlayer.play.error.410":"Video stream disconnected abnormally","wsPlayer.play.error.411":"Recording playback completed","wsPlayer.play.error.457":"Time setting error","wsPlayer.play.error.503":"WS connection address error, not from the current ICC server","wsPlayer.play.error.504":"Intercom failed","wsPlayer.play.error.701":"Low Chrome version, please upgrade to the latest Chrome","wsPlayer.play.error.702":"Low Firefox version, please upgrade to the latest Firefox","wsPlayer.play.error.703":"Low Edge version, please upgrade to the latest Edge","wsPlayer.play.error.default":"Playback failed, please check configuration","wsPlayer.error.101":"Selected channel is offline, unable to play","wsPlayer.error.102":"Login permissions have expired, failed to query rtsp for real-time preview","wsPlayer.error.103":"Interface not connected, please check if the project backend has not forwarded the interface.","wsPlayer.error.104":"Incorrect format returned by the interface, please pass through the data returned by the ICC service.","wsPlayer.error.105":"Channel is sleeping, waking up in progress, please try again later","wsPlayer.error.106":"Please enter the rtsp address and websocket address","wsPlayer.error.107":"No records file passed, unable to scrub the timeline","wsPlayer.error.201":"No recording file found for selected channel","wsPlayer.error.202":"Failed to retrieve recording file list","wsPlayer.error.203":"Failed to retrieve recording rtsp","wsPlayer.error.204":"Cannot play audio at {0}x speed","wsPlayer.error.205":"Channel is sleeping, waking up in progress, please try again later","wsPlayer.error.206":"Unsupported playback speed","wsPlayer.error.301":"Currently in intercom, unable to close audio","wsPlayer.error.302":"Another device is in intercom, unable to start audio","wsPlayer.error.303":"Another device is in intercom, unable to start intercom","wsPlayer.error.304":"Failed to retrieve intercom rtsp","wsPlayer.error.305":"HTTP protocol does not support intercom","wsPlayer.error.306":"Device intercom failed","wsPlayer.error.307":"PCM audio format not supported for intercom","wsPlayer.error.308":"ARM audio format not supported for intercom","wsPlayer.error.309":"G711u audio format not supported for intercom","wsPlayer.error.310":"G726 audio format not supported for intercom","wsPlayer.error.311":"AAC audio format not supported for intercom","wsPlayer.error.312":"G722 audio format not supported for intercom","wsPlayer.error.501":"Decoding library not initialized, please play later!","wsPlayer.error.502":"Decoding library not initialized, please start intercom later!","wsPlayer.error.503":"Please check if the player container exists when creating the player","wsPlayer.error.601":"The operated player does not exist","wsPlayer.error.602":"The selected player is locally recording, cannot record again","wsPlayer.error.603":"The selected player is not playing a recording, cannot start local recording","wsPlayer.error.604":"The selected player has not started local recording, cannot operate to close local recording","wsPlayer.error.605":"Incorrect time jump playback parameter","wsPlayer.error.606":"Incorrect adaptive stretching parameter","wsPlayer.error.607":"Real-time preview does not support playback speed adjustment","wsPlayer.error.608":"Incorrect {0} method: {1}","wsPlayer.error.609":"Exceeded the maximum number of window layouts set","wsPlayer.error.610":"Failed to operate custom window layout: {0}","wsPlayer.error.611":"Real-time preview does not support playback method","wsPlayer.error.612":"Real-time preview does not support pause method","wsPlayer.error.613":"Real-time preview does not support jump playback","wsPlayer.error.614":"Real-time preview is not playing in the current window, unable to perform intercom.","wsPlayer.error.701":"PTZ is locked by user {0}, cannot operate","wsPlayer.error.702":"Failed to control PTZ 3D positioning {0}","wsPlayer.error.703":"Failed to control PTZ {0}{1}{2}","wsPlayer.error.704":"Failed to control PTZ direction {0}",error2084:"You do not have permission to perform this operation"}};let y={},u=null;const m={setLocal:function(e){y=p[e]},setI18n:function(e){u=e},$t:function(e,t){if(u&&u.t)return u.t(e,t);let s=y[e];return Array.isArray(t)&&t.forEach(((e,t)=>{s=s.replace(`{${t}}`,e)})),s}};class w extends h{constructor(e){super(e),this.currentIndex=e.index,this.wrapperDomId=e.wrapperDomId,this.canvasId=`${this.domId}-livecanvas`,this.ivsCanvasId=`${this.domId}-ivs-livecanvas`,this.pztCanvasId=`${this.domId}-pzt-livecanvas`,this.videoId=`${this.domId}-liveVideo`,this.type="real",this.options={},this.initDom(),this.defaultStatus=WSPlayerJQ.$(".default-status",this.$el),this.error=WSPlayerJQ.$(".error",this.$el),this.controller=WSPlayerJQ.$(".player-control",this.$el),this.controller.dblclick((e=>{e.stopPropagation()})),this.initMouseEvent(),this.setStatus("created")}getTemplate(){return`\n        <div id="${this.domId}" style="visibility: hidden; top: 150%; left: 0; width: 0; height: 0;" class="wsplayer-item wsplayer-item-${this.index} ${0===this.index?"selected":"unselected"}">\n            <div class="ws-full-content ws-flex">\n                <canvas id="${this.canvasId}" class="kind-stream-canvas" kind-channel-id="0" width="800" height="600"></canvas>\n                <video id="${this.videoId}" class="kind-stream-canvas" kind-channel-id="0" muted style="display:none" width="800" height="600"></video>\n                <canvas id="${this.ivsCanvasId}" class="kind-stream-canvas" style="position: absolute" kind-channel-id="0" width="800" height="600"></canvas>\n                <canvas id="${this.pztCanvasId}" class="kind-stream-canvas" style="display: none; position: absolute; z-index: 2;" kind-channel-id="0" width="800" height="600"></canvas>\n            </div>\n            <div class="default-status">\n                <img src="${this.wsPlayer.prefixUrl}/WSPlayer/icon/default.png" alt="">\n            </div>\n            <div class="player-control top-control-bar">\n                <div class="stream">\n                    <div class="select-container">\n                        <div class="select-show select">\n                            <div class="code-stream">${m.$t("storagePlan.stream.main")}</div>\n                            \x3c!-- 下拉箭头 --\x3e\n                            <img src="${this.wsPlayer.prefixUrl}/WSPlayer/icon/spread.png" />\n                        </div>\n                        <div class="stream-type" style="display: none">\n                            <ul class="select-ul">\n                                \x3c!--主码流--\x3e\n                                <li title='${m.$t("storagePlan.stream.main")}' stream-type="1" class="stream-type-item">${m.$t("storagePlan.stream.main")}</li>\n                                \x3c!--辅码流1--\x3e\n                                <li title='${m.$t("video.player.sub.stream",[1])}' stream-type="2" class="stream-type-item">${m.$t("video.player.sub.stream",[1])}</li>\n                                \x3c!--辅码流2--\x3e\n                                <li title='${m.$t("video.player.sub.stream",[2])}' stream-type="3" class="stream-type-item">${m.$t("video.player.sub.stream",[2])}</li>\n                            </ul>\n                        </div>\n                    </div>\n                    <span class="stream-info"></span>\n                </div>\n                <div class="opt-icons">\n                    \x3c!--智能帧--\x3e\n                    <div class="opt-icon ws-draw-triangle draw-triangle-icon off" title='${m.$t("wsPlayer.drawTriangle")}'></div>\n                    \x3c!--对讲--\x3e\n                    <div class="opt-icon ws-talk talk-icon off" title='${m.$t("wsPlayer.talk")}'></div>\n                    \x3c!--录像--\x3e\n                    <div class="opt-icon ws-record record-icon" title='${m.$t("wsPlayer.recording")}'></div>\n                    \x3c!--声音--\x3e\n                    <div class="opt-icon ws-audio audio-icon off" title='${m.$t("wsPlayer.sound")}'></div>\n                    \x3c!--抓图--\x3e\n                    <div class="opt-icon ws-capture capture-icon" title='${m.$t("wsPlayer.capture")}'></div>\n                    \x3c!--更多--\x3e\n                    <div class="opt-icon ws-more more-icon" title='${m.$t("wsplayer.more")}'>\n                        <div class="opt-icons-vertical">\n                            \x3c!--智能帧--\x3e\n                            <div class="ws-draw-triangle" title='${m.$t("wsPlayer.drawTriangle")}'><div class="opt-icon draw-triangle-icon off"></div>${m.$t("wsPlayer.drawTriangle")}</div>\n                            \x3c!--对讲--\x3e\n                            <div class="ws-talk" title='${m.$t("wsPlayer.talk")}'><div class="opt-icon talk-icon off"></div>${m.$t("wsPlayer.talk")}</div>\n                            \x3c!--录像--\x3e\n                            <div class="ws-record" title='${m.$t("wsPlayer.recording")}'><div class="opt-icon record-icon"></div>${m.$t("wsPlayer.recording")}</div>\n                            \x3c!--声音--\x3e\n                            <div class="ws-audio" title='${m.$t("wsPlayer.sound")}'><div class="opt-icon audio-icon off"></div>${m.$t("wsPlayer.sound")}</div>\n                            \x3c!--抓图--\x3e\n                            <div class="ws-capture" title='${m.$t("wsPlayer.capture")}'><div class="opt-icon capture-icon"></div>${m.$t("wsPlayer.capture")}</div>\n                        </div>\n                    </div>\n                    \x3c!--关闭--\x3e\n                    <div class="opt-icon ws-close close-icon" title='${m.$t("common.close")}'></div>\n                </div>\n            </div>\n            \x3c!--对讲中--\x3e\n            <div class="ws-talking">${m.$t("wsPlayer.talking")}</div>\n            <div class="error">\n                <div class="wsplayer-error-message"></div>\n            </div>\n        </div>\n        `}initMouseEvent(){super.initMouseEvent();let e=this;this.hideTimer=null,this.wsPlayer.config.draggable&&(this.$el.on("mousedown",(e=>{this.wsPlayer.currentDragWindowIndex=this.currentIndex,e.preventDefault()})),this.$el.on("mouseup",(e=>{this.wsPlayer.changeDragWindow(this.currentIndex),e.preventDefault()}))),this.$el.on("mouseenter mousemove",(e=>{["created","closed"].includes(this.status)||this.setDomVisible(WSPlayerJQ.$(".player-control",WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)),!0),"playing"!==this.status&&"error"!==this.status||this.hideTimer&&clearTimeout(this.hideTimer)})),this.$el.on("mouseleave",(e=>{this.hideTimer=setTimeout((()=>{WSPlayerJQ.$(".stream-type",this.$el).hide(),this.setDomVisible(WSPlayerJQ.$(".player-control",WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)),!1),this.streamSelectShow=!1,this.setDomVisible(WSPlayerJQ.$(".opt-icons-vertical",this.$el),!1),this.showMoreIcon=!1}),300)})),this.streamSelectShow=!1,WSPlayerJQ.$(".select",this.$el).click((e=>{this.streamSelectShow?(WSPlayerJQ.$(".stream-type",this.$el).hide(),this.streamSelectShow=!1):(WSPlayerJQ.$(".stream-type",this.$el).show(),this.streamSelectShow=!0)})),WSPlayerJQ.$(".stream-type",this.$el).click((t=>{let s=t.target.getAttribute("stream-type");e.streamType!==s&&e.options&&("url"===e.playType?e.wsPlayer.sendMessage("changeStreamType",{channelData:e.options.channelData,streamType:Number(s),selectIndex:e.index}):e.wsPlayer.changeStreamType(e.options.channelData,s,e.index))}))}setStreamType(e){this.streamType=e;let t=WSPlayerJQ.$(".stream-type .select-ul",this.$el)[0].children[e-1],s=t&&WSPlayerJQ.$(t).attr("title");WSPlayerJQ.$(".code-stream",this.$el).text(s),WSPlayerJQ.$(".code-stream",this.$el).attr("title",s),t&&WSPlayerJQ.$(t).addClass("stream-type-select").siblings().removeClass("stream-type-select")}setStatus(e,s){switch(this.wsPlayer.sendMessage("statusChanged",{status:e,windowIndex:this.index}),this.status=e,this.currentWindowPlaying=!1,this.status){case"created":case"closed":this.setDomVisible(this.defaultStatus,!0),this.setDomVisible(this.error,!1),this.setDomVisible(this.controller,!1),this.videoElem.src="",WSPlayerJQ.$(".audio-icon",this.$el).removeClass("on").addClass("off");break;case"loading":case"ready":case"playing":case"pause":this.currentWindowPlaying=!0,this.setDomVisible(this.defaultStatus,!1),this.setDomVisible(this.error,!1);break;case"streamError":const{selectIndex:e,channelData:i={}}=this.options;this.wsPlayer.sendMessage("realError",{selectIndex:e,channelData:{...i}},{code:s.errorCode,msg:t.errorVideoInfo[s.errorCode]}),this.close(!1,!1,"none");case"error":this.showMsgInWindow(t.errorVideoInfo[s.errorCode]?t.errorVideoInfo[s.errorCode]:t.errorVideoInfo.defaultErrorMsg)}}init(e){if(this.wsPlayer.config.isDynamicLoadLib&&!window.m_nModuleInitialized){let t=setTimeout((()=>{this.init(e),clearTimeout(t)}),100);return}let t=(this.options||{}).channelId===e.channelId;this.options=e,this.playSDK&&(this.isAudioPlay&&WSPlayerJQ.$(".audio-icon",this.$el).removeClass("on").addClass("off"),this.close(!0,t)),this.setLoading(!0,"ready"),this.setStreamType(e.streamType),this.createPlayer(e)}startPlay(e,t){"video"===t.decodeMode?(this.videoElem.style.display="",this.canvasElem.style.display="none"):(this.videoElem.style.display="none",this.canvasElem.style.display=""),this.customDomElem&&this.customDomElem.css("display",""),this.updateAdapter(e.playerAdapter,t),this.width=t.width,this.height=t.height,WSPlayerJQ.$(".stream-info",this.$el).text(`${t.encodeMode?`${t.encodeMode}, `:""}${t.width?`${t.width}*`:""}${t.height?t.height:""}${"video"===t.decodeMode?", 硬解":", 软解"}`)}createPlayer(e){const{onlyLoadSingleLib:s,useH264MSE:i,useH265MSE:r,useNginxProxy:a,openIvs:l,ivsTypeArr:n}=this.wsPlayer.config;let o=this;this.playSDK=new window.PlaySDKInterface(!s),this.setLogLevel(),this.playSDK.SetCallBack("StreamRedirect",(function(t){let s="";if(this.wsPlayer.setWSUrl)s=this.wsPlayer.setRedirectWSUrl();else if(a){let i=s=t.split("rtsp://")[1].split("/")[0];s=e.wsURL.split("serverIp=")[0]+"serverIp="+i}else s=e.wsURL.split("://")[0]+t.split("rtsp://")[1].split("/")[0];o.playSDK.StartPullStream({strRtspvUri:s,strRtspvUrl:t,strSourceId:"",bTalkService:!1,nRange:0})})),this.playSDK.SetCallBack("GetPlayPort",(e=>{this.playSDK.nPlayPort=e,window.wsPlayerManager.bindPlayer(this.playSDK.nPlayPort,this.playSDK)})),this.playSDK.SetCallBack("PlayStart",(t=>{console.log("PlayStart",t),o.startPlay(e,t),o.talkWSUrl=e.wsURL,o.setLoading(!1,"playing"),WSPlayerJQ.$(".ws-draw-triangle",o.$el).removeClass(l?"off":"on").addClass(l?"on":"off"),l?(o.playSDK.OpenIVS(o.initIndex),n.includes(1)||o.playSDK.SetIvsEnable(3,0),n.includes(2)||(o.playSDK.SetIvsEnable(1,0),o.playSDK.SetIvsEnable(14,0))):o.playSDK.CloseIVS(),o.wsPlayer.sendMessage("realSuccess",{channelData:{...e.channelData||{}},selectIndex:e.selectIndex,customDomId:o.customDomId})})),this.playSDK.SetCallBack("DecodeStart",(e=>{})),this.playSDK.SetCallBack("Disconnect",(()=>{o.setLoading(!1,"closed"),!o.isError&&o.setStatus("streamError",{errorCode:"410",description:"Video Stream Abnormality"})})),this.playSDK.SetCallBack("VideoFrameInfo",(e=>{o.wsPlayer.sendMessage("getVideoFrameInfo",{channelData:o.options.channelData,selectIndex:o.index,audioFrameInfo:e})})),this.playSDK.SetCallBack("AudioFrameInfo",(e=>{o.wsPlayer.sendMessage("getAudioFrameInfo",{channelData:o.options.channelData,selectIndex:o.index,audioFrameInfo:e})})),this.playSDK.SetCallBack("CapturePicDataCallBack",(e=>{o.wsPlayer.sendMessage("picCap",{channelData:o.options.channelData,selectIndex:o.index,base64Img:this.uint8ArrayToBase64(e)})})),this.playSDK.SetCallBack("Error",(s=>{o.playSDK&&![101,102,103,104,204].includes(Number(s.errorCode))&&(o.isError=!0,o.setLoading(!1),console.log("Error: "+JSON.stringify(s)),o.setStatus("error",s),o.wsPlayer.sendMessage("realError",{selectIndex:e.selectIndex,channelData:{...e.channelData||{}}},{code:s.errorCode,msg:t.errorVideoInfo[s.errorCode]}))})),this.playSDK.Init({canvasElem:this.canvasElem,videoElem:this.videoElem,ivsCanvasElem:this.ivsCanvasElem,bPlayBack:0,strDecodeFilePath:`${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`},null,this.wsPlayer.config.isWebView);const{platform:c}=navigator;0==c.indexOf("iPhone")?this.playSDK.SetDecodeMode(!1,!1):this.playSDK.SetDecodeMode(i,r),this.playSDK.SetCacheMode(this.wsPlayer.config.cacheMode||1),this.playSDK.m_rtspvHandle=this.playSDK.StartPullStream({strRtspvUri:e.wsURL,strRtspvUrl:e.rtspURL,strSourceId:"",bTalkService:!1,nRange:0,nShortTimeout:this.wsPlayer.rtspResponseTimeout||8,nRtspResponseTimeout:this.wsPlayer.rtspResponseTimeout+2||10}),null!=this.playSDK.m_rtspvHandle&&window.wsPlayerManager.bindHandlePlayer(this.playSDK.m_rtspvHandle,this.playSDK)}startTalk(e){if(this.wsPlayer.config.isDynamicLoadLib&&!window.m_nModuleInitialized)return void this.wsPlayer.sendErrorMessage("502");this.wsPlayer.isTalking=!0,this.isTalking=!0,WSPlayerJQ.$(".talk-icon",this.$el).removeClass("off").addClass("on");let s=this;this.talkData=e.talkData,this.talkPlaySDK=new window.PlaySDKInterface(!this.wsPlayer.config.onlyLoadSingleLib),this.setLogLevel(),this.talkPlaySDK.SetCallBack("GetPlayPort",(e=>{this.talkPlaySDK.nPlayPort=e,window.wsPlayerManager.bindPlayer(this.talkPlaySDK.nPlayPort,this.talkPlaySDK)})),this.talkPlaySDK.SetCallBack("Error",(i=>{s.wsPlayer.sendMessage("talkError",{selectIndex:e.selectIndex,channelData:{...this.options.channelData||{}}},{code:i.errorCode,msg:t.errorVideoInfo[i.errorCode]}),s.stopTalk()})),this.talkPlaySDK.Init({canvasElem:null,videoElem:null,ivsCanvasElem:null,bPlayBack:0,strDecodeFilePath:`${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`});let i=this.talkPlaySDK.StartPullStream({strRtspvUri:this.talkWSUrl,strRtspvUrl:e.rtspURL+"&trackID=501",strSourceId:"",bTalkService:!0,nRange:0,nShortTimeout:this.wsPlayer.rtspResponseTimeout||8,nRtspResponseTimeout:this.wsPlayer.rtspResponseTimeout+2||10,bBroadcast:Boolean(e.gbDevice)});null!=i&&window.wsPlayerManager.bindHandlePlayer(i,this.talkPlaySDK),"4"===e.talkData.audioType?this.talkPlaySDK.StartTalk(1):this.talkPlaySDK.StartTalk(0),window.wsPlayerManager.bindPlayer(this.talkPlaySDK.nPlayPort,this.talkPlaySDK),WSPlayerJQ.$(".ws-talking",this.$el).css({visibility:"visible"}),this.openVolume()}talkByUrl(e){if(this.wsPlayer.config.isDynamicLoadLib&&!window.m_nModuleInitialized)return void this.wsPlayer.sendErrorMessage("502");if(!this.currentWindowPlaying)return void this.wsPlayer.sendErrorMessage("614");if(![2,4].includes(Number(e.audioType))){let t={1:307,3:308,4:309,5:310,8:311,101:312};return void this.wsPlayer.sendErrorMessage(t[e.audioType],{channelList:[e]})}this.wsPlayer.isTalking=!0,this.isTalking=!0,WSPlayerJQ.$(".talk-icon",this.$el).removeClass("off").addClass("on");let s=this;this.talkPlaySDK=new window.PlaySDKInterface(!this.wsPlayer.config.onlyLoadSingleLib),this.setLogLevel(),this.talkPlaySDK.SetCallBack("GetPlayPort",(e=>{s.talkPlaySDK.nPlayPort=e,window.wsPlayerManager.bindPlayer(s.talkPlaySDK.nPlayPort,s.talkPlaySDK)})),this.talkPlaySDK.SetCallBack("Error",(i=>{s.wsPlayer.sendMessage("talkError",{selectIndex:e.selectIndex},{code:i.errorCode,msg:t.errorVideoInfo[i.errorCode]}),s.stopTalk()})),this.talkPlaySDK.Init({canvasElem:null,videoElem:null,ivsCanvasElem:null,bPlayBack:0,strDecodeFilePath:`${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`});let i=this.talkPlaySDK.StartPullStream({strRtspvUri:e.wsURL,strRtspvUrl:e.rtspURL+"&trackID=501",strSourceId:"",bTalkService:!0,nRange:0,nShortTimeout:this.wsPlayer.rtspResponseTimeout||8,nRtspResponseTimeout:this.wsPlayer.rtspResponseTimeout+2||10,bBroadcast:Boolean(e.gbDevice)});null!=i&&window.wsPlayerManager.bindHandlePlayer(i,this.talkPlaySDK),"4"===String(e.audioType)?this.talkPlaySDK.StartTalk(1):this.talkPlaySDK.StartTalk(0),window.wsPlayerManager.bindPlayer(this.talkPlaySDK.nPlayPort,this.talkPlaySDK),WSPlayerJQ.$(".ws-talking",this.$el).css({visibility:"visible"}),this.openVolume()}stopTalk(){if(this.talkPlaySDK&&window.wsPlayerManager.unbindPlayer(this.talkPlaySDK.nPlayPort),this.talkData&&this.wsPlayer.stopTalk(this.talkData),this.isTalking&&(this.wsPlayer.isTalking=!1,this.isTalking=!1),this.talkPlaySDK&&(this.talkPlaySDK.StopTalk(),this.talkPlaySDK.StopPullStream(),this.talkPlaySDK=null),WSPlayerJQ.$(".talk-icon",this.$el).removeClass("on").addClass("off"),WSPlayerJQ.$(".ws-talking",this.$el).css({visibility:"hidden"}),this.closeVolume(),this.options&&"url"===this.options.playType){const{selectIndex:e,channelData:t={}}=this.options;this.wsPlayer.sendMessage("stopTalk",{selectIndex:e,channelData:{...t}})}}}class P extends h{constructor(e){super(e),this.currentIndex=e.index,this.wrapperDomId=e.wrapperDomId,this.speed=1,this.canvasId=`${this.domId}-recordcanvas`,this.ivsCanvasId=`${this.domId}-ivs-livecanvas`,this.videoId=`${this.domId}-recordVideo`,this.curTimestamp=0,this.type="record",this.initDom(),this.defaultStatus=WSPlayerJQ.$(".default-status",this.$el),this.error=WSPlayerJQ.$(".error",this.$el),this.controller=WSPlayerJQ.$(".player-control",this.$el),this.timeInfo=WSPlayerJQ.$(".time-info",this.$el),this.initMouseEvent(),this.setStatus("created")}getTemplate(){return`\n        <div id="${this.domId}" style="visibility: hidden; top: 150%; left: 0; width: 0; height: 0;" class="wsplayer-item wsplayer-item-${this.index} ${0===this.index?"selected":"unselected"}">\n            <canvas id="${this.canvasId}" class="kind-stream-canvas" kind-channel-id="0" width="800" height="600"></canvas>\n            <video id="${this.videoId}" class="kind-stream-canvas" kind-channel-id="0" muted style="display:none" width="800" height="600"></video>\n            <canvas id="${this.ivsCanvasId}" class="kind-stream-canvas" style="position: absolute" kind-channel-id="0" width="800" height="600"></canvas>\n            <div class="default-status">\n                <img src="${this.wsPlayer.prefixUrl}/WSPlayer/icon/default.png" alt="">\n            </div>\n            <div class="player-control top-control-bar">\n                <span class="stream-info"></span>\n                <div class="opt-icons">\n                    \x3c!--智能帧--\x3e\n                    <div class="opt-icon ws-draw-triangle draw-triangle-icon off" title='${m.$t("wsPlayer.drawTriangle")}'></div>\n                    \x3c!--录像--\x3e\n                    <div class="opt-icon ws-record record-icon" title='${m.$t("wsPlayer.recording")}'></div>\n                    \x3c!--声音--\x3e\n                    <div class="opt-icon ws-audio audio-icon off" title='${m.$t("wsPlayer.sound")}'></div>\n                    \x3c!--抓图--\x3e\n                    <div class="opt-icon ws-capture capture-icon" title='${m.$t("wsPlayer.capture")}'></div>\n                    \x3c!--更多--\x3e\n                    <div class="opt-icon ws-more more-icon" title='${m.$t("wsplayer.more")}'>\n                        <div class="opt-icons-vertical">\n                            \x3c!--智能帧--\x3e\n                            <div class="ws-draw-triangle" title='${m.$t("wsPlayer.drawTriangle")}'><div class="opt-icon draw-triangle-icon off"></div>${m.$t("wsPlayer.drawTriangle")}</div>\n                            \x3c!--录像--\x3e\n                            <div class="ws-record" title='${m.$t("wsPlayer.recording")}'><div class="opt-icon record-icon"></div>${m.$t("wsPlayer.recording")}</div>\n                            \x3c!--声音--\x3e\n                            <div class="ws-audio" title='${m.$t("wsPlayer.sound")}'><div class="opt-icon audio-icon off"></div>${m.$t("wsPlayer.sound")}</div>\n                            \x3c!--抓图--\x3e\n                            <div class="ws-capture" title='${m.$t("wsPlayer.capture")}'><div class="opt-icon capture-icon"></div>${m.$t("wsPlayer.capture")}</div>\n                        </div>\n                    </div>\n                    \x3c!--关闭--\x3e\n                    <div class="ws-close opt-icon close-icon" title='${m.$t("common.close")}'></div>\n                </div>\n            </div>\n            <div class="player-control record-control-bar">\n                <div class="wsplayer-progress-bar">\n                    <div class="progress-bar_background"></div>\n                    <div class="progress-bar_hover_light"></div>\n                    <div class="progress-bar_light"></div>\n                </div>\n                <div class="record-control-left">\n                    <div class="opt-icon play-ctrl-btn play-icon play"></div>\n                    <div class="time-info"></div>/<div class="time-long"></div>\n                </div>\n                <div class="record-control-right">\n                    <div class="opt-icon close-icon"></div>\n                </div>\n            </div>\n            <div class="error">\n                <div class="wsplayer-error-message"></div>\n            </div>\n            <div class="play-pause-wrapper">\n                <div class="play-ctrl-btn center-play-icon"></div>\n            </div>\n        </div>\n        `}initMouseEvent(){super.initMouseEvent(),this.hideTimer=null,this.wsPlayer.config.draggable&&(this.$el.on("mousedown",(e=>{this.wsPlayer.currentDragWindowIndex=this.currentIndex,e.preventDefault()})),this.$el.on("mouseup",(e=>{this.wsPlayer.changeDragWindow(this.currentIndex),e.preventDefault()}))),this.$el.on("mouseenter mousemove",(e=>{["created","closed"].includes(this.status)||this.setDomVisible(WSPlayerJQ.$(".player-control",WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)),!0),"playing"===this.status?this.hideTimer&&clearTimeout(this.hideTimer):"ready"===this.status&&this.setDomVisible(this.progressBar,!0)})),this.$el.on("mouseleave",(e=>{"pause"!==this.status&&(this.hideTimer=setTimeout((()=>{this.setDomVisible(WSPlayerJQ.$(".player-control",WSPlayerJQ.$(`#${this.wrapperDomId}-${this.currentIndex}`)),!1),this.setDomVisible(WSPlayerJQ.$(".opt-icons-vertical",this.$el),!1),this.showMoreIcon=!1}),300))})),WSPlayerJQ.$(".wsplayer-progress-bar",this.$el).on("mousemove",(e=>{WSPlayerJQ.$(".progress-bar_hover_light",this.$el).css({width:e.offsetX+"px"})})),WSPlayerJQ.$(".wsplayer-progress-bar",this.$el).on("mouseleave",(e=>{WSPlayerJQ.$(".progress-bar_hover_light",this.$el).css({width:0})})),WSPlayerJQ.$(".play-ctrl-btn",this.$el).click((e=>{"playing"===this.status?(this.pause(),WSPlayerJQ.$(".play-icon",this.$el).removeClass("play").addClass("pause")):(this.play(),WSPlayerJQ.$(".play-icon",this.$el).removeClass("pause").addClass("play"))}))}setStatus(e,s){switch(this.wsPlayer.sendMessage("statusChanged",{status:e,windowIndex:this.index}),this.status=e,this.currentWindowPlaying=!1,this.status){case"created":case"closed":this.setDomVisible(this.defaultStatus,!0),this.setDomVisible(this.error,!1),this.setDomVisible(this.controller,!1),WSPlayerJQ.$(".audio-icon",this.$el).removeClass("on").addClass("off");break;case"ready":this.setDomVisible(this.defaultStatus,!1),this.setDomVisible(this.error,!1);break;case"playing":this.wsPlayer.selectIndex===this.index&&(this.currentWindowPlaying=!0,WSPlayerJQ.$("#ws-record-time-box").css({visibility:"visible"})),this.setDomVisible(this.defaultStatus,!1),this.setDomVisible(this.error,!1),this.setDomVisible(WSPlayerJQ.$(".play-pause-wrapper",this.$el),!1);break;case"pause":this.currentWindowPlaying=!0,this.setDomVisible(this.defaultStatus,!1),this.setDomVisible(this.error,!1),this.setDomVisible(this.controller,!1),this.setDomVisible(WSPlayerJQ.$(".play-pause-wrapper",this.$el),!0);break;case"streamError":setTimeout((()=>this.close(!1,!1,"none")),0);const{selectIndex:e,channelData:i={}}=this.options;"411"===s.errorCode?this.wsPlayer.sendMessage("recordFinish",{selectIndex:e,channelData:{...i}}):this.wsPlayer.sendMessage("recordError",{selectIndex:e,channelData:{...i}},{code:s.errorCode,msg:t.errorVideoInfo[s.errorCode]});case"error":this.showMsgInWindow(t.errorVideoInfo[s.errorCode]?t.errorVideoInfo[s.errorCode]:t.errorVideoInfo.defaultErrorMsg)}}init(e){if(!this.wsPlayer.config.isDynamicLoadLib||window.m_nModuleInitialized)this.options=e,this.playSDK&&(this.isAudioPlay&&WSPlayerJQ.$(".audio-icon",this.$el).removeClass("on").addClass("off"),this.close(!0)),this.setLoading(!0),this.createPlayer(e);else{let t=setTimeout((()=>{this.init(e),clearTimeout(t)}),100)}}createPlayer(e){let s=this;const{useH264MSE:i,useH265MSE:r,onlyLoadSingleLib:a,useNginxProxy:l,ivsTypeArr:n,openIvs:o}=this.wsPlayer.config;this.playSDK=new window.PlaySDKInterface(!a),this.setLogLevel(),this.playSDK.SetCallBack("StreamRedirect",(function(t){let i="";if(this.wsPlayer.setWSUrl)i=this.wsPlayer.setRedirectWSUrl(e.wsURL,t);else if(l){let s=i=t.split("rtsp://")[1].split("/")[0];i=e.wsURL.split("serverIp=")[0]+"serverIp="+s}else i=e.wsURL.split("://")[0]+t.split("rtsp://")[1].split("/")[0];s.playSDK.StartPullStream({strRtspvUri:i,strRtspvUrl:t,strSourceId:"",bTalkService:!1,nRange:0,nShortTimeout:rtspResponseTimeout||8,nRtspResponseTimeout:rtspResponseTimeout+2||10})})),this.playSDK.SetCallBack("GetPlayPort",(e=>{this.playSDK.nPlayPort=e,window.wsPlayerManager.bindPlayer(this.playSDK.nPlayPort,this.playSDK)})),this.playSDK.SetCallBack("PlayStart",(t=>{console.log("PlayStart",t),s.setLoading(!1,"playing"),"video"===t.decodeMode?(s.videoElem.style.display="",s.canvasElem.style.display="none"):(s.videoElem.style.display="none",s.canvasElem.style.display=""),this.customDomElem&&this.customDomElem.css("display",""),s.updateAdapter(e.playerAdapter,t),WSPlayerJQ.$(".stream-info",s.$el).text(`${t.encodeMode?`${t.encodeMode}, `:""}${t.width?`${t.width}*`:""}${t.height?t.height:""}${"video"===t.decodeMode?", 硬解":", 软解"}`),s.wsPlayer.selectIndex===s.index&&(WSPlayerJQ.$(".ws-record-play",s.wsPlayer.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-pause",s.wsPlayer.$el).css({display:"block"})),WSPlayerJQ.$(".ws-draw-triangle",s.$el).removeClass(o?"off":"on").addClass(o?"on":"off"),o?(s.playSDK.OpenIVS(s.initIndex),n.includes(1)||s.playSDK.SetIvsEnable(3,0),n.includes(2)||(s.playSDK.SetIvsEnable(1,0),s.playSDK.SetIvsEnable(14,0))):s.playSDK.CloseIVS(),s.wsPlayer.sendMessage("recordSuccess",{selectIndex:e.selectIndex,channelData:{...e.channelData||{}},customDomId:s.customDomId})})),this.playSDK.SetCallBack("DecodeStart",(e=>{s.DecodeStart&&s.wsPlayer.config.playCenterRecordByTime&&(s.DecodeStart(),s.DecodeStart=null)})),this.playSDK.SetCallBack("Disconnect",(()=>{s.setLoading(!1,"closed"),!s.isError&&s.setStatus("streamError",{errorCode:"410",description:"Video Stream Abnormality"})})),this.playSDK.SetCallBack("VideoFrameInfo",(e=>{"playing"===s.status&&s.wsPlayer.__setPlayingTime(s.index,e.nYear,e.nMonth,e.nDay,e.nHour,e.nMinute,e.nSecond),s.wsPlayer.sendMessage("getVideoFrameInfo",{channelData:s.options.channelData,selectIndex:s.index,videoFrameInfo:e,timeStamp:new Date(`${e.nYear}-${e.nMonth}-${e.nDay} ${e.nHour}:${e.nMinute}:${e.nSecond}`).getTime()/1e3})})),this.playSDK.SetCallBack("AudioFrameInfo",(e=>{s.wsPlayer.sendMessage("getAudioFrameInfo",{channelData:s.options.channelData,selectIndex:s.index,audioFrameInfo:e})})),this.playSDK.SetCallBack("CapturePicDataCallBack",(e=>(s.wsPlayer.sendMessage("picCap",{channelData:s.options.channelData,selectIndex:s.index,base64Img:this.uint8ArrayToBase64(e)}),!1))),this.playSDK.SetCallBack("PlayBackStreamRange",(t=>{this.options&&"url"===e.playType&&s.wsPlayer.setTimeLine(e.records),s.wsPlayer.sendMessage("getPlayBackStreamRange",{channelData:s.options.channelData,selectIndex:s.index,recordRange:t})})),this.playSDK.SetCallBack("StreamPlayOver",(e=>{console.log("回放播放完成",e);let t="",i=s.options.ssId,r=s.options.ssIdList||[];i&&(t=r[r.indexOf(i)+1]),!s.options.playRecordByTime&&t?s.wsPlayer.playNextRecord(s.index,t):s.setStatus("streamError",{errorCode:"411",description:"Record Finished"})})),this.playSDK.SetCallBack("Error",(i=>{if(s.playSDK){if(s.isError=!0,[101,102,103,104,204,408].includes(Number(i.errorCode)))return;s.setLoading(!1),console.log("Error: "+JSON.stringify(i)),s.setStatus("error",i),s.wsPlayer.sendMessage("recordError",{selectIndex:e.selectIndex,channelData:{...e.channelData||{}}},{code:i.errorCode,msg:t.errorVideoInfo[i.errorCode]})}})),this.playSDK.Init({canvasElem:this.canvasElem,videoElem:this.videoElem,ivsCanvasElem:this.ivsCanvasElem,bPlayBack:1,strDecodeFilePath:`${this.wsPlayer.prefixUrl}/WSPlayer/singleThread`},null,this.wsPlayer.config.isWebView);const{platform:c}=navigator;0==c.indexOf("iPhone")?this.playSDK.SetDecodeMode(!1,!1):this.playSDK.SetDecodeMode(i,r),this.playSDK.m_rtspvHandle=this.playSDK.StartPullStream({strRtspvUri:e.wsURL,strRtspvUrl:e.rtspURL,strSourceId:"",bTalkService:!1,nRange:0,nShortTimeout:this.wsPlayer.rtspResponseTimeout||8,nRtspResponseTimeout:this.wsPlayer.rtspResponseTimeout+2||10}),null!=this.playSDK.m_rtspvHandle&&window.wsPlayerManager.bindHandlePlayer(this.playSDK.m_rtspvHandle,this.playSDK),this.timeLong=e.endTime-e.startTime;let d=this.timeLong%60,h=parseInt(this.timeLong/60)%60,p=parseInt(this.timeLong/3600)%60;this.timeLongStr=`${p>0?p+":":""}${h<10?"0"+h:h}:${d<10?"0"+d:d}`,WSPlayerJQ.$(".time-long",this.$el).text(this.timeLongStr),this.setStatus("ready"),window.wsPlayerManager.bindPlayer(this.playSDK.nPlayPort,this.playSDK)}playSpeed(e){this.speed=e,1!==e&&this.closeVolume(),this.playSDK&&this.playSDK.SetSpeed(e)}}class S{constructor(){__publicField(this,"cPlusMediaFrameCallBack",(function(e,t,s,i){this.handleToPlayer[e]&&this.handleToPlayer[e].InputDataEx(s,i)})),__publicField(this,"cPlusRtspMsgCallBack",(function(e,t,s){switch(t){case 4096:setTimeout((()=>{this.handleToPlayer[e].StopPullStream(),this.handleToPlayer[e].StreamFailedCallback(s),this.handleToPlayer[e].StreamDisconnectCallback()}),500);break;case 4097:break;case 4100:this.handleToPlayer[e].StreamFinishCallback();break;case 4104:this.handleToPlayer[e].StreamRedirectCallback(s)}})),__publicField(this,"cPlusRtsvMsgCallBack",(function(e,t,s){switch(t){case 16385:break;case 16386:this.handleToPlayer[e]&&this.handleToPlayer[e].StreamFinishCallback()}})),__publicField(this,"cIVSDrawDataCallBack",(function(e,t,s,i,r){this.portToPlayer[e]&&this.portToPlayer[e].SetIVSDrawData(e,t,s,i,r)})),this.wsPlayerList=[],this.portToPlayer={},this.handleToPlayer={},window.cPlusVisibleDecCallBack=this.cPlusVisibleDecCallBack.bind(this),window.cDigitalSignCallBack=this.cDigitalSignCallBack.bind(this),window.cRecordDataCallBack=this.cRecordDataCallBack.bind(this),window.cIVSDrawDataCallBack=this.cIVSDrawDataCallBack.bind(this),window.cPlusMediaFrameCallBack=this.cPlusMediaFrameCallBack.bind(this),window.cPlusRtspMsgCallBack=this.cPlusRtspMsgCallBack.bind(this),window.cPlusRtsvMsgCallBack=this.cPlusRtsvMsgCallBack.bind(this)}cPlusVisibleDecCallBack(e,t,s,i,r,a){this.portToPlayer[e]&&this.portToPlayer[e].SetFrameData(e,t,s,i,r,a)}cDigitalSignCallBack(e,t,s){this.portToPlayer[e]&&this.portToPlayer[e].SetDecryptionResult(e,t,s)}cRecordDataCallBack(e,t,s,i,r){this.portToPlayer[e]&&this.portToPlayer[e].SetRecordData(e,t,s,i,r)}bindPlayer(e,t){this.portToPlayer[e]||(this.portToPlayer[e]=t)}unbindPlayer(e){this.portToPlayer[e]=null}bindHandlePlayer(e,t){this.handleToPlayer[e]||(this.handleToPlayer[e]=t)}unbindHandlePlayer(e){this.handleToPlayer[e]=null}addWSPlayer(e){this.wsPlayerList.push()}removeWSPlayer(e){this.wsPlayerList=this.wsPlayerList.filter((t=>t===e))}}const g={clientType:"WINPC",clientMac:"30:9c:23:79:40:08",clientPushId:"",project:"PSDK",method:"MTS.Video.StartVideo",data:{optional:"/admin/API/MTS/Video/StartVideo",dataType:"3",streamType:"2",channelId:"",trackId:"",urlType:1}},v={clientType:"WINPC",clientMac:"30:9c:23:79:40:08",clientPushId:"",project:"PSDK",method:"MTS.Audio.StartTalk",data:{optional:"/admin/API/MTS/Audio/StartTalk?token=ff93dabe5d754ea8acb0a95dbe6c4a0f",source:"",deviceCode:"",talkType:"1",target:"",audioBit:16,audioType:2,broadcastChannels:"",sampleRate:8e3,talkMode:"",channelSeq:"0",enableGBParamAutoAdapt:1}},f={clientType:"WINPC",clientMac:"30:9c:23:79:40:08",clientPushId:"",project:"PSDK",method:"SS.Record.QueryRecords",data:{cardNo:"",optional:"/admin/API/SS/Record/QueryRecords",diskPath:"",startIndex:"",streamType:"0",recordType:"0",recordSource:"3",endIndex:"",startTime:"",endTime:"",channelId:""}},$={clientType:"WINPC",clientMac:"30:9c:23:79:40:08",clientPushId:"",project:"PSDK",method:"SS.Playback.StartPlaybackByTime",data:{nvrId:"",optional:"/admin/API/SS/Playback/StartPlaybackByTime",recordType:"0",recordSource:"1",streamType:"1",channelId:"",startTime:"",endTime:"",urlType:"pull"}},x={1:307,3:308,4:309,5:310,8:311,101:312};class I{constructor(e){this.realPlayer=null,this.recordPlayer=null,this.playCenterRecordByTime=e.playCenterRecordByTime,this.i18n=e.i18n,"real"===e.type?this.realPlayer=e.player:this.recordPlayer=e.player,this.playIndex=0,this.recordList=[],this.getRealRtsp=e.getRealRtsp,this.getRecords=e.getRecords,this.getRecordRtspByTime=e.getRecordRtspByTime,this.getRecordRtspByFile=e.getRecordRtspByFile,this.getTalkRtsp=e.getTalkRtsp,this.stopTalk=e.stopTalk}getCurrentRtsp(e,t){const s=this.getRTSPUrls(e),i=s.find((e=>e.includes(window.location.hostname)))||s[0],r=/\btoken=[^&]*/.test(i),a=/\?/.test(e)?"&":"?";return r?i:`${i}${a}token=${t}`}getRTSPUrls(e){return e.split("|").filter((e=>!e.includes("localhost")&&!e.includes("127.0.0.1")&&!e.startsWith("rtsp://[")))}processResponse(e,t){try{return"string"==typeof e&&e.includes("rtsp://")&&e.includes("&token=")?{url:e,innerIp:null}:e.url&&e||e.data.url&&e.data||e.data.data.url&&e.data.data}catch{return null}}getWSUrl(e){return this.getRTSPUrls(e).map((e=>e.split("//")[1].split("/")[0]))}wssRedirectParam(e){"wss"===((this.realPlayer||this.recordPlayer).protocol||("https:"===window.location.protocol?"wss":"ws"))&&(e.data.clientSupportWSSDirect=1,e.data.enableXNetFlag=1)}openSomeWindow(e){let t=this.realPlayer||this.recordPlayer;e>t.showNum&&(e<t.maxWindow?this.playNum=e>16?25:e>9?16:e>4?9:4:this.playNum=t.maxWindow,t.setPlayerNum(this.playNum))}playRealVideo(e,t="2",s,r=!1){i.validFunction(this.getRealRtsp)?(this.openSomeWindow(e.length),e.map(((i,a)=>{let l=s>-1?s:this.playIndex;e.length>1&&(l+=a),g.data.streamType=t,g.data.channelId=i.id,this.wssRedirectParam(g),this.realPlayer.setLoading(l,!i.isWaiting,"loading"),this.getRealRtsp(JSON.parse(JSON.stringify(g))).then((e=>{let s=this.processResponse(e,i);if(!s)return this.realPlayer.sendErrorMessage(104,{channelList:[i]});s.rtspURL=this.getCurrentRtsp(s.url,s.token),this.realPlayer.playReal({selectIndex:l,streamServerIp:s.innerIp,rtspURL:s.rtspURL,channelId:i.id,channelData:i,streamType:t,wsList:this.getWSUrl(s.url),wssDirect:s.wssDirect})}),(e=>{this.realPlayer.setLoading(l,!1,"closed"),e?27001007===Number(e.code)?this.realPlayer.sendErrorMessage(102,{channelList:[i],selectIndex:l,apiErrorInfo:e}):3033===Number(e.code)?this.realPlayer.sendErrorMessage(105,{channelList:[i],selectIndex:l,apiErrorInfo:e}):r?this.realPlayer.sendErrorMessage(103,{channelList:[i],selectIndex:l,apiErrorInfo:e}):this.playRealVideo([i],"1",l,!0):this.realPlayer.sendErrorMessage(108,{channelList:[i],selectIndex:l,apiErrorInfo:e})}))}))):this.realPlayer.sendErrorMessage(608,{insert:[this.i18n.$t("wsPlayer.request.realtime.preview.interface"),"getRealRtsp"]})}startTalk(e){if(!i.validFunction(this.getTalkRtsp))return void this.realPlayer.sendErrorMessage(608,{insert:[this.i18n.$t("wsPlayer.request.talk.interface"),"getTalkRtsp"]});v.data.deviceCode=e.deviceCode,v.data.audioBit=e.audioBit||16,v.data.sampleRate=e.sampleRate||8e3,[1,6,10,43].includes(e.deviceType)?(v.data.talkType="2",v.data.channelSeq=e.channelSeq):(v.data.talkType="1",v.data.channelSeq="0");let t={talkType:v.data.talkType,deviceCode:e.deviceCode,session:"",channelSeq:v.data.channelSeq,audioType:2},s=e=>{let s=this.getCurrentRtsp(e.url,e.token);this.realPlayer.playerList[this.realPlayer.talkIndex].startTalk({rtspURL:s,streamServerIp:e.innerIp,wsList:this.getWSUrl(e.url),talkData:t,gbDevice:e.gbDevice})};this.getTalkRtsp(JSON.parse(JSON.stringify(v))).then((i=>{let r=this.processResponse(i,e);return r?(t.session=r.session,t.audioType=r.audioType,"2"!==t.audioType&&"4"!==t.audioType?(this.stopTalk({data:t}),v.data.audioType=4,void this.getTalkRtsp(JSON.parse(JSON.stringify(v))).then((i=>(r=this.processResponse(i,e),r?(t.session=r.session,t.audioType=r.audioType,"4"!==i.audioType?(this.realPlayer.sendErrorMessage(x[t.audioType],{channelList:[e]}),void this.stopTalk({data:t})):void s(r)):this.realPlayer.sendErrorMessage(104,{channelList:[e]})))).catch((t=>{this.realPlayer.sendErrorMessage(304,{channelList:[e],apiErrorInfo:t})}))):void s(r)):this.realPlayer.sendErrorMessage(104,{channelList:[e]})})).catch((t=>{this.realPlayer.sendErrorMessage(304,{channelList:[e],apiErrorInfo:t})}))}getRecordList(e,t){if(!i.validFunction(this.getRecords))return void this.recordPlayer.sendErrorMessage(608,{insert:[this.i18n.$t("wsPlayer.request.recording.interface"),"getRecords"]});f.data.streamType=e.streamType||"0",f.data.recordType=e.recordType||"0",f.data.recordSource=e.recordSource,f.data.startTime=e.startTime,f.data.endTime=e.endTime;let s="number"==typeof e.windowIndex?e.windowIndex:e.channelList.length>1?0:this.playIndex;e.channelList.length>1&&this.openSomeWindow(s+e.channelList.length),e.channelList.forEach(((i,r)=>{let a=s+r;this.recordPlayer.setLoading(a,!i.isWaiting,"loading"),f.data.channelId=i.id,this.getRecords(JSON.parse(JSON.stringify(f))).then((s=>{let r=(s.records||s.data&&s.data.records||s.data&&s.data.data&&s.data.data.records||[]||[]).sort(((e,t)=>e.startTime-t.startTime));if(!r.length)return this.recordPlayer.setLoading(a,!1,"closed"),void this.recordPlayer.sendErrorMessage(201,{channelList:[i]});this.getRecordRtsp({...e,channel:i},r.map((e=>(e.isImportant="2"===e.recordType,e))),!e.isUpdateRecords,a,t)}),(t=>{this.recordPlayer.setLoading(a,!1,"closed"),t?3033===Number(t.code)?this.recordPlayer.sendErrorMessage(205,{channelList:[i],options:e,selectIndex:a,apiErrorInfo:t}):this.recordPlayer.sendErrorMessage(202,{channelList:[i],apiErrorInfo:t}):this.recordPlayer.sendErrorMessage(108,{channelList:[i],selectIndex:a,apiErrorInfo:t})}))}))}getRecordRtsp(e,t,s=!0,i,r){let a=null,l=t[0].recordSource||e.recordSource,n=e.ssId,o=[];$.data.streamType=t[0].streamType||e.streamType||"0",$.data.recordType="1",$.data.recordSource=l,$.data.startTime=e.startTime,$.data.endTime=e.endTime,$.data.channelId=e.channel.id,$.data.streamId=t[0].streamId||"",n=n||t[0].ssId,o=Array.from(new Set(t.map((e=>e.ssId)))),$.data.ssId=n,this.wssRedirectParam($),a=this.getRecordRtspByTime(JSON.parse(JSON.stringify($))),a&&a.then((a=>{let l=this.processResponse(a,e.channel);return l?(l.channelId=e.channel.id,l.rtspURL=this.getCurrentRtsp(l.url,l.token),l.wsList=this.getWSUrl(l.url),l.rtspURL?(l.channelData=e.channel,l.startTime=e.startTime,l.endTime=e.endTime,void this.recordPlay(l,i,n,o,e.isJumpPlay,r,true,t).then((()=>{let r=this.recordList[i];if(s)this.recordList[i]={...e,recordList:t,recordIndex:0,isPlaying:!0};else{let e=t[0].recordName;r.recordIndex=r.recordList.findIndex((t=>t.recordName===e)),r.isPlaying=!0}this.playIndex===i&&(s||(t=r.recordList,r.isPlaying=!0),this.setTimeLine(t))}))):(this.recordPlayer.setLoading(i,!1,"closed"),this.recordPlayer.sendErrorMessage(201,{channelList:[e.channel]}),void console.warn("所选通道未查询到录像文件"))):(this.recordPlayer.setLoading(i,!1,"closed"),this.realPlayer.sendErrorMessage(104,{channelList:[e.channel]}))}),(t=>{this.recordPlayer.setLoading(i,!1,"closed"),t?this.recordPlayer.sendErrorMessage(203,{channelList:[e.channel],apiErrorInfo:t}):this.recordPlayer.sendErrorMessage(108,{channelList:[e.channel],selectIndex:i,apiErrorInfo:t})}))}recordPlay(e,t,s,i,r,a,l,n){return new Promise((o=>{this.recordPlayer.playRecord({...e,streamServerIp:e.innerIp,selectIndex:t,ssId:s,ssIdList:i,isJumpPlay:r,playRecordByTime:l||this.recordPlayer.config.playCenterRecordByTime&&!!s,recordList:n},a).then((()=>o()))}))}setTimeLine(e){this.recordPlayer.setTimeLine(e)}clickRecordTimeLine(e,t){let s=this.recordList[this.playIndex],i=s.startTime;i=new Date(1e3*i).setHours(0),i=new Date(i).setMinutes(0),i=new Date(i).setSeconds(0)/1e3,this.playCenterRecordByTime||(i+=e);let r={channelList:[s.channel],startTime:i,endTime:s.endTime,recordSource:s.recordSource,isUpdateRecords:!0,ssId:t,isJumpPlay:!0};this.getRecordList(r,{DecodeStart(){this.player.playByTime(e)}})}playNextRecord(e,t){if(t){if(!i.validFunction(this.getRecordRtspByTime))return void this.recordPlayer.sendErrorMessage(608,{insert:[this.i18n.$t("wsPlayer.request.recording.interface"),"getRecordRtspByTime"]});let s=this.recordList[e],r=s.recordList.find((e=>e.ssId===t));$.data.streamType=r.streamType||"0",$.data.recordType="1",$.data.recordSource=r.recordSource,$.data.startTime=new Date(1e3*r.startTime).setHours(0,0,0)/1e3,$.data.endTime=new Date(1e3*r.endTime).setHours(23,59,59)/1e3,$.data.channelId=r.channelId,$.data.ssId=t,$.data.streamId=r.streamId||"",this.wssRedirectParam($);let a=Array.from(new Set(s.recordList.map((e=>e.ssId))));this.getRecordRtspByTime(JSON.parse(JSON.stringify($))).then((i=>{let l=this.processResponse(i,opt.channel);if(!l)return this.realPlayer.sendErrorMessage(104,{channelList:[opt.channel]});l.channelId=r.channelId,l.rtspURL=this.getCurrentRtsp(l.url,l.token),l.wsList=this.getWSUrl(l.url),l.startTime=$.data.startTime,l.endTime=$.data.endTime,this.recordPlay(l,e,t,a,!0).then((()=>{this.setTimeLine(s.recordList)}))}))}else;}changeTimeLine(e){let t=this.recordList[e];t&&t.isPlaying&&this.setTimeLine(t.recordList)}videoClosed(e,t,s){this.recordList[e]&&(this.recordList[e].isPlaying=!1)}setPlayIndex(e){this.playIndex=e}}class b{constructor(e={},t){this.i18n=e.locale,this.el=e.el,this.wsPlayer=t,this.prefixUrl=e.prefixUrl||"./static",this.$el=WSPlayerJQ.$("#"+this.el),this.$el&&!this.$el.children().length&&this.__createPanTilt(),this.channel=null,this.channelCodeForPositionList=[],this.setPtzDirection=e.setPtzDirection,this.setPtzCamera=e.setPtzCamera,this.controlSitPosition=e.controlSitPosition,this.mousedownCanvasEvent=this.__mousedownCanvasEvent.bind(this),this.mousemoveCanvasEvent=this.__mousemoveCanvasEvent.bind(this),this.mouseupCanvasEvent=this.__mouseupCanvasEvent.bind(this),this.clickDirectFlag=!1,this.setPtzDirectionPromiseList=[],this.setPtzDirectionRun=!1,this.setPtzCameraPromiseList=[],this.setPtzCameraRun=!1}setChannel(e){this.channel={...e};let t=this.wsPlayer.selectIndex,s=this.channelCodeForPositionList[t];if(!e)return WSPlayerJQ.$(".ws-pan-tilt-mask",this.$el).css({display:"block"}),WSPlayerJQ.$(".ws-pan-tilt-mask-position",this.$el).css({display:"none"}),void this.__removeCanvasEvent();s?s!==e.id?(this.channelCodeForPositionList[t]=null,this.__removeCanvasEvent()):this.__openSitPosition(!0):this.openSitPositionFlag&&this.__removeCanvasEvent();let i=e.capability;switch(e.cameraType+""){case"1":parseInt(i,2)&parseInt("100",2)||parseInt(i,2)&parseInt("10000000000000000",2)?WSPlayerJQ.$(".ws-pan-tilt-mask-zoom",this.$el).css({display:"none"}):WSPlayerJQ.$(".ws-pan-tilt-mask-zoom",this.$el).css({display:"block"}),parseInt(i,2)&parseInt("10000000000000000",2)?(WSPlayerJQ.$(".ws-pan-tilt-mask-direction",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-pan-tilt-mask-position",this.$el).css({display:"block"}),this.__removeCanvasEvent()):(WSPlayerJQ.$(".ws-pan-tilt-mask-direction",this.$el).css({display:"block"}),WSPlayerJQ.$(".ws-pan-tilt-mask-position",this.$el).css({display:"none"})),WSPlayerJQ.$(".ws-pan-tilt-mask-aperture",this.$el).css({display:"block"});break;case"2":WSPlayerJQ.$(".ws-pan-tilt-mask",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-pan-tilt-mask-position",this.$el).css({display:"none"});break;default:WSPlayerJQ.$(".ws-pan-tilt-mask",this.$el).css({display:"block"}),WSPlayerJQ.$(".ws-pan-tilt-mask-position",this.$el).css({display:"none"}),this.__removeCanvasEvent()}}__createPanTilt(){this.$el.append(`\n            <div class="ws-pan-tilt-control">\n                <div class="ws-pan-tilt-circle-wrapper">\n                    \x3c!--云台方向控制--\x3e\n                    <div class="ws-pan-tilt-circle-rotate">\n                        <div class="ws-pan-tilt-circle">\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-t.svg" title="${this.i18n.$t("video.player.ptz.up")}" direct="1"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-rt.svg" title="${this.i18n.$t("video.player.ptz.upper.right")}" direct="7"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-r.svg" title="${this.i18n.$t("video.player.ptz.right")}" direct="4"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-rb.svg" title="${this.i18n.$t("video.player.ptz.lower.right")}" direct="8"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-b.svg" title="${this.i18n.$t("video.player.ptz.down")}" direct="2"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-lb.svg" title="${this.i18n.$t("video.player.ptz.lower.left")}" direct="6"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-l.svg" title="${this.i18n.$t("video.player.ptz.left")}" direct="3"/></div>\n                            <div class="ws-pan-tilt-direction-item"><img src="${this.prefixUrl}/WSPlayer/icon/arrow-lt.svg" title="${this.i18n.$t("video.player.ptz.upper.left")}" direct="5"/></div>\n                            <div class="ws-pan-tilt-inner-circle">\n                                <img\n                                    class="ws-pan-tilt-pzt-select"\n                                    src="${this.prefixUrl}/WSPlayer/icon/ptz-select.svg"\n                                    title="${this.i18n.$t("inspec.ptz.position1")}"\n                                />\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                \n                \x3c!--云台变倍、聚焦、光圈控制--\x3e\n                <div class="ws-cloud-control-wrapper">\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon1.svg" title="${this.i18n.$t("video.player.ptz.zoom")}-" operateType="1" direct="2"/></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon2.svg" title="${this.i18n.$t("video.player.ptz.zoom")}+" operateType="1" direct="1"/></div>\n                    <div class="cloud-control-separate"></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon3.svg" title="${this.i18n.$t("video.player.ptz.focus")}-" operateType="2" direct="2"/></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon4.svg" title="${this.i18n.$t("video.player.ptz.focus")}+" operateType="2" direct="1"/></div>\n                    <div class="cloud-control-separate"></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon5.svg" title="${this.i18n.$t("video.player.ptz.halo")}-" operateType="3" direct="2"/></div>\n                    <div class="ws-pan-tilt-control-item"><img src="${this.prefixUrl}/WSPlayer/icon/ptz-icon6.svg" title="${this.i18n.$t("video.player.ptz.halo")}+" operateType="3" direct="1"/></div>\n                </div>\n                \n                \x3c!--遮罩，当通道没有云台功能时，使用遮罩遮住云台按钮--\x3e\n                \x3c!--方向按钮遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-direction"></div>\n                \x3c!--三维定位遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-position"></div>\n                \x3c!--变倍、聚焦遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-zoom"></div>\n                \x3c!--光圈遮罩--\x3e\n                <div class="ws-pan-tilt-mask ws-pan-tilt-mask-aperture"></div>\n            </div>\n        `),WSPlayerJQ.$(".ws-pan-tilt-circle",this.$el).mouseup((e=>{if(this.clickDirectFlag){this.clickDirectFlag=!1;let t=this.__getDirect(e.target);t&&this.__setPtzDirection.call(this,t,"0")}})),WSPlayerJQ.$(".ws-pan-tilt-circle",this.$el).mouseout((e=>{if(this.clickDirectFlag){this.clickDirectFlag=!1;let t=this.__getDirect(e.target);t&&this.__setPtzDirection.call(this,t,"0")}})),WSPlayerJQ.$(".ws-pan-tilt-circle",this.$el).mousedown(this._throttle((e=>{if(!this.clickDirectFlag){let t=this.__getDirect(e.target);t&&(this.clickDirectFlag=!0,this.__setPtzDirection.call(this,t,"1"))}}),1e3)),WSPlayerJQ.$(".ws-pan-tilt-control-item img",this.$el).mouseup((e=>{this.operateFlag&&(this.operateFlag=!1,this.__setPtzCamera(e.target.getAttribute("operateType"),e.target.getAttribute("direct"),"0"))})),WSPlayerJQ.$(".ws-pan-tilt-control-item img",this.$el).mouseout((e=>{this.operateFlag&&(this.operateFlag=!1,this.__setPtzCamera(e.target.getAttribute("operateType"),e.target.getAttribute("direct"),"0"))})),WSPlayerJQ.$(".ws-pan-tilt-control-item img",this.$el).mousedown(this._throttle((e=>{this.operateFlag=!0,this.__setPtzCamera(e.target.getAttribute("operateType"),e.target.getAttribute("direct"),"1")}),1e3)),WSPlayerJQ.$(".ws-pan-tilt-pzt-select",this.$el).click((e=>{this.__openSitPosition(!this.openSitPositionFlag)}))}__getDirect(e){let t=e.getAttribute("direct");if(!t){let s=e.childNodes[0];s&&s.getAttribute&&(t=s.getAttribute("direct"))}return t}__setPtzDirection(e,t){const s={project:"PSDK",method:"DMS.Ptz.OperateDirect",data:{direct:e,command:t,stepX:"4",stepY:"4",channelId:this.channel.id}};if(this.setPtzDirectionPromiseList.push((()=>new Promise(((e,i)=>{this.setPtzDirection&&this.setPtzDirection(s).then((s=>{let i=s.data||s;"1"===t&&i.result&&"0"===i.result&&this.wsPlayer.sendErrorMessage(701,{insert:[s.data.lockUser.userName],apiErrorInfo:s}),e()})).catch((e=>{let s=[""];1103===(e.data||e).code&&(s=[`：${this.i18n.$t("error2084")}`]),"1"===t&&this.wsPlayer.sendErrorMessage(704,{apiErrorInfo:e,insert:s}),i()}))})))),!this.setPtzDirectionRun){let e=()=>{this.setPtzDirectionRun=!0,this.setPtzDirectionPromiseList[0]().then((()=>{})).catch((()=>{})).finally((()=>{this.setPtzDirectionPromiseList.shift(),this.setPtzDirectionPromiseList.length?e():this.setPtzDirectionRun=!1}))};e()}}__setPtzCamera(e,t,s){const i={project:"PSDK",method:"DMS.Ptz.OperateCamera",data:{operateType:e,direct:t,command:s,step:"4",channelId:this.channel.id}};if(this.setPtzCameraPromiseList.push((()=>new Promise(((e,t)=>{this.setPtzCamera&&this.setPtzCamera(i).then((t=>{let i=t.data||t;"1"===s&&i.result&&"0"===i.result&&this.wsPlayer.sendErrorMessage(701,{insert:[t.data.lockUser.userName],apiErrorInfo:t}),e()})).catch((e=>{let r=["",this.i18n.$t("video.player.ptz.zoom"),this.i18n.$t("video.player.ptz.focus"),this.i18n.$t("video.player.ptz.halo")],a=["","+","-"],l=["","",""];1103===(e.data||e).code&&(l=[r[i.data.operateType],a[i.data.direct],`：${this.i18n.$t("error2084")}`]),"1"===s&&this.wsPlayer.sendErrorMessage(703,{apiErrorInfo:e,insert:l}),t()}))})))),!this.setPtzCameraRun){let e=()=>{this.setPtzCameraRun=!0,this.setPtzCameraPromiseList[0]().then((()=>{})).catch((()=>{})).finally((()=>{this.setPtzCameraPromiseList.shift(),this.setPtzCameraPromiseList.length?e():this.setPtzCameraRun=!1}))};e()}}__openSitPosition(e){this.openSitPositionFlag=e;let t=this.wsPlayer.playerList,s=this.wsPlayer.selectIndex;this.canvasElem=t[s].pztCanvasElem,this.canvasElem.addEventListener("mousedown",this.mousedownCanvasEvent),this.canvasElem.addEventListener("mousemove",this.mousemoveCanvasEvent),this.canvasElem.addEventListener("mouseup",this.mouseupCanvasEvent),this.canvasContext=this.canvasElem.getContext("2d"),this.canvasContext.lineWidth=2,this.canvasContext.strokeStyle="#009cff",this.openSitPositionFlag?(this.channelCodeForPositionList[s]=this.channel.id,WSPlayerJQ.$(this.canvasElem).css({display:"block"}),WSPlayerJQ.$(".ws-pan-tilt-pzt-select",this.$el)[0].src=`${this.prefixUrl}/WSPlayer/icon/ptz-select-hover.svg`):(this.channelCodeForPositionList[s]=null,WSPlayerJQ.$(this.canvasElem).css({display:"none"}),WSPlayerJQ.$(".ws-pan-tilt-pzt-select",this.$el)[0].src=`${this.prefixUrl}/WSPlayer/icon/ptz-select.svg`)}__mousedownCanvasEvent(e){e.target===this.canvasElem&&(e.offsetX||e.layerX)&&(this.pointX=e.offsetX||e.layerX,this.pointY=e.offsetY||e.layerY,this.startDraw=!0)}__mousemoveCanvasEvent(e){if(e.target===this.canvasElem&&this.startDraw&&(e.offsetX||e.layerX)){const t=e.offsetX||e.layerX,s=e.offsetY||e.layerY,i=t-this.pointX,r=s-this.pointY;this.canvasContext.clearRect(0,0,this.canvasElem.width,this.canvasElem.height),this.canvasContext.beginPath(),this.canvasContext.strokeRect(this.pointX,this.pointY,i,r)}}__mouseupCanvasEvent(e){if(e.target===this.canvasElem&&(e.offsetX||e.layerX)){this.startDraw=!1;const t=e.offsetX||e.layerX,s=e.offsetY||e.layerY;let i="",r="",a="";const l=(t+this.pointX)/2,n=(s+this.pointY)/2,o=this.canvasElem.width/2,c=this.canvasElem.height/2,d=Math.abs(t-this.pointX),h=Math.abs(s-this.pointY),p=t<this.pointX;i=8192*(l-o)*2/this.canvasElem.width,r=8192*(n-c)*2/this.canvasElem.height,t===this.pointX||s===this.pointY?a=0:(a=this.canvasElem.width*this.canvasElem.height/(d*h),p&&(a=-a)),this.canvasContext.clearRect(0,0,this.canvasElem.width,this.canvasElem.height),this.__controlSitPosition(i,r,a)}}__removeCanvasEvent(){this.canvasElem&&(this.canvasElem.removeEventListener("mousedown",this.mousedownCanvasEvent),this.canvasElem.removeEventListener("mousemove",this.mousemoveCanvasEvent),this.canvasElem.removeEventListener("mouseup",this.mouseupCanvasEvent),WSPlayerJQ.$(this.canvasElem).css({display:"none"}),this.canvasElem=null,this.canvasContext=null,this.openSitPositionFlag=!1,WSPlayerJQ.$(".ws-pan-tilt-pzt-select",this.$el)[0].src=`${this.prefixUrl}/WSPlayer/icon/ptz-select.svg`)}__controlSitPosition(e,t,s){const i={project:"PSDK",method:"DMS.Ptz.SitPosition",data:{magicId:localStorage.getItem("magicId")||"",pointX:String(Math.round(e)),pointY:String(Math.round(t)),pointZ:String(Math.round(s)),extend:"1",channelId:this.channel.id}};this.controlSitPosition&&this.controlSitPosition(i).then((e=>{let t=e.data||e;t.result&&"0"===t.result&&this.wsPlayer.sendErrorMessage(701,{insert:[e.data.lockUser.userName],apiErrorInfo:e})})).catch((e=>{let t=[""];1103===(e.data||e).code&&(t[0]=`：${this.i18n.$t("error2084")}`),this.wsPlayer.sendErrorMessage(702,{apiErrorInfo:e,insert:t})}))}_throttle(e,t){let s=0;return function(...i){const r=(new Date).getTime();if(!(r-s<t))return s=r,e.apply(this,i)}}}const D={num:1,maxNum:25,showControl:!0,draggable:!1,showRecordProgressBar:!0,isDynamicLoadLib:!0,onlyLoadSingleLib:!1,useNginxProxy:!1,openIvs:!0,ivsTypeArr:[1,2],useH264MSE:!0,useH265MSE:!0,showIcons:{streamChangeSelect:!0,ivsIcon:!0,talkIcon:!0,localRecordIcon:!0,audioIcon:!0,snapshotIcon:!0,closeIcon:!0},downloadMp4Record:!localStorage.playSDKLogLevel,localRecordSize:100,playCenterRecordByTime:!1,localeLang:"zhCN",cacheMode:1,isWebView:!1},C=100,T=100/C;let k,L,W=null,E=20,R={},_=!0,M="",J={x:0,y:0},Q=[],N={},A={},z=0,K="",U=()=>{try{k.removeEventListener("selectstart",B),k.removeEventListener("mousemove",V),k.removeEventListener("mouseup",F),k.addEventListener("selectstart",B),k.addEventListener("mousemove",V),k.addEventListener("mouseup",F)}catch(e){setTimeout((()=>{U()}),300)}};function B(){return!1}function V(e){if(!M)return;let t=L.getBoundingClientRect(),s=t.width/C,i=t.height/C;switch(M){case"right-border":if(J.x+=e.movementX,z!==Math.round(J.x/s)){if(z=Math.round(J.x/s),A.lStep+z<=0)return void(z=1-A.lStep);if(A.lStep+z>=C)return void(z=C-A.lStep-1);document.querySelector("#division-move-line").style.left=(A.lStep+z)*T+"%"}break;case"bottom-border":if(J.y+=e.movementY,z!==Math.round(J.y/i)){if(z=Math.round(J.y/i),A.tStep+z<=0)return void(z=1-A.tStep);A.tStep+z>=C&&(z=C-A.tStep-1),document.querySelector("#division-move-line").style.top=(A.tStep+z)*T+"%"}}}function F(){if(M){let e=[],t=!0;switch(M){case"right-border":if(!z)return;let{l_window:s,r_window:i}=N;if(e=[...s.map((e=>(e.wStep-=z,e.lStep+=z,e.wStep<1&&(t=!1),e))),...i.map((e=>(e.wStep+=z,e.wStep<1&&(t=!1),e)))],!t)return M="",document.querySelector("#division-move-line").remove(),G(),void R.onError("不能拖拽到当前位置，请重新拖拽");e.forEach((e=>Q[e.selectIndex]=e));break;case"bottom-border":if(!z)return;let{t_window:r,b_window:a}=N;if(e=[...r.map((e=>(e.hStep-=z,e.tStep+=z,e.hStep<1&&(t=!1),e))),...a.map((e=>(e.hStep+=z,e.hStep<1&&(t=!1),e)))],!t)return M="",document.querySelector("#division-move-line").remove(),G(),void R.onError("不能拖拽到当前位置，请重新拖拽");e.forEach((e=>Q[e.selectIndex]=e))}M="",G()}}function O(e=[]){if(!e.length||1===e.length)return Q=[{wStep:C,hStep:C,tStep:0,lStep:0,selectIndex:0}],Q;e=e.sort(((e,t)=>e.tStep-t.tStep));for(let t=0;t<e.length-1;t++)if(e[t].tStep===e[t+1].tStep&&e[t].lStep>e[t+1].lStep){let s={...e[t]};e[t]={...e[t+1]},e[t+1]=s}return e.map(((e,t)=>(e.selectIndex=t,e)))}function H(e,t){let s=document.querySelector("#temp-preview-line");s.style.display="block",s.style.background="#1d79f4",s.style.opacity=.4,"col"===t&&(s.style.left=`${Math.floor(e.lStep+e.wStep/2)}%`,s.style.top=`${e.tStep}%`,s.style.width="2px",s.style.height=`${e.hStep}%`),"raw"===t&&(s.style.left=`${e.lStep}%`,s.style.top=`${Math.floor(e.tStep+e.hStep/2)}%`,s.style.width=`${e.wStep}%`,s.style.height="2px")}function q(e,t){Q.length!==E?1===e.wStep&&1===e.hStep||("col"===t&&(Q.push({...e,lStep:e.lStep+Math.floor(e.wStep/2),wStep:Math.ceil(e.wStep/2)}),Q[e.selectIndex]={...e,wStep:Math.floor(e.wStep/2)}),"raw"===t&&(Q.push({...e,tStep:e.tStep+Math.floor(e.hStep/2),hStep:Math.ceil(e.hStep/2)}),Q[e.selectIndex]={...e,hStep:Math.floor(e.hStep/2)}),G()):R.onError(W.$t("wsPlayer.custom.player.max.support.tip",[E]))}function j(e,t){let s="";return"x"===t&&(s=`<div id="division-move-line" style="position: absolute; background: #1d79f4; top: ${e.tStep*T}%; left: ${e.lStep*T}%; width: 2px; height: ${e.hStep*T}%; "></div>`),"y"===t&&(s=`<div id="division-move-line" style="position: absolute; background: #1d79f4; top: ${e.tStep*T}%; left: ${e.lStep*T}%; width: ${e.wStep*T}%; height: 2px; "></div>`),s}function X(e){return`<div\n        id="dom-item-${e.selectIndex}"\n        class="${_?"dom-item-flex":"dom-item"}"\n        style="top: ${e.top}%; left: ${e.left}%; width: ${e.width}%; height: ${e.height}%;"\n    >\n        <div class="point-n-resize" id="dom-item-${e.selectIndex}-bottom-border" style="bottom: -4px; left: 0; width: 100%; height: 6px;"></div>\n        <div class="point-e-resize" id="dom-item-${e.selectIndex}-right-border" style="top: 0; right: -4px; width: 6px; height: 100%;"></div>\n        <div style="width: 68px; display: flex;">\n            <div id="dom-item-${e.selectIndex}-btn-col" class="dom-item-split-btn"></div>\n            <div id="dom-item-${e.selectIndex}-btn-raw" class="dom-item-split-btn" style="margin-left: 11px; transform: rotate(90deg)"></div>\n        </div>\n        <div id="dom-item-${e.selectIndex}-delete-btn" class="dom-item-merge-btn"></div>\n    </div>`}function Y(e=[]){_=!1;let t=[];e.forEach((e=>{t.push(X({selectIndex:e.selectIndex,width:e.wStep*T,height:e.hStep*T,top:e.tStep*T,left:e.lStep*T}))})),L.innerHTML=t.join("")+'<div id="temp-preview-line" style="position: absolute; z-index: 10; display: none;"></div>',e.forEach((e=>{document.querySelector(`#dom-item-${e.selectIndex}-btn-raw`).addEventListener("click",(()=>{q(e,"raw")})),document.querySelector(`#dom-item-${e.selectIndex}-btn-col`).addEventListener("click",(()=>{q(e,"col")})),document.querySelector(`#dom-item-${e.selectIndex}-delete-btn`).addEventListener("click",(()=>{!function(e){if(1===Q.length)return void R.onError(W.$t("wsPlayer.current.window.cannot.be.merged"));let t=Q.findIndex((t=>t.wStep===e.wStep&&t.lStep===e.lStep&&(t.tStep+t.hStep===e.tStep||e.tStep+e.hStep===t.tStep)||t.hStep===e.hStep&&t.tStep===e.tStep&&(t.lStep+t.wStep===e.lStep||e.lStep+e.wStep===t.lStep)));if(t>-1){let s=Q[t];s.tStep===e.tStep&&(s.selectIndex>e.selectIndex?(Q[e.selectIndex].wStep=e.wStep+s.wStep,Q.splice(s.selectIndex,1)):(Q[s.selectIndex].wStep=s.wStep+e.wStep,Q.splice(e.selectIndex,1))),s.lStep===e.lStep&&(s.selectIndex>e.selectIndex?(Q[e.selectIndex].hStep=e.hStep+s.hStep,Q.splice(s.selectIndex,1)):(Q[s.selectIndex].hStep=s.hStep+e.hStep,Q.splice(e.selectIndex,1)))}else R.onError(W.$t("wsPlayer.current.window.cannot.be.merged"));G()}(e)})),document.querySelector(`#dom-item-${e.selectIndex}-btn-raw`).addEventListener("mouseenter",(t=>{H(e,"raw")})),document.querySelector(`#dom-item-${e.selectIndex}-btn-raw`).addEventListener("mouseleave",(e=>{document.querySelector("#temp-preview-line").style.display="none"})),document.querySelector(`#dom-item-${e.selectIndex}-btn-col`).addEventListener("mouseenter",(t=>{H(e,"col")})),document.querySelector(`#dom-item-${e.selectIndex}-btn-col`).addEventListener("mouseleave",(e=>{document.querySelector("#temp-preview-line").style.display="none"})),document.querySelector(`#dom-item-${e.selectIndex}-right-border`).addEventListener("mousedown",(t=>{t.stopPropagation(),e.lStep+e.wStep!==C&&(M="right-border",J={x:0},z=0,N=function({wStep:e,lStep:t,selectIndex:s},i){let r=i.filter((s=>s.lStep+s.wStep===t+e)).sort(((e,t)=>e.tStep-t.tStep)),a=i.filter((s=>s.lStep===t+e)).sort(((e,t)=>e.tStep-t.tStep));for(var l=0;l<r.length-1;l++)r[l].tStep+r[l].hStep!==r[l+1].tStep&&(s<=r[l].selectIndex?r.splice(l+1):(r.splice(0,l),l=-1));let n=-1,o=-1;return a.forEach((e=>{let t=r.findIndex((t=>e.tStep===t.tStep)),s=r.findLastIndex((t=>e.tStep+e.hStep===t.tStep+t.hStep));t>-1&&(n=0===t?0:Math.min(t,n)),s>-1&&(o=Math.max(s,o))})),a=a.filter((e=>e.tStep>=r[n].tStep&&e.tStep+e.hStep<=r[o].tStep+r[o].hStep)),{l_window:a,r_window:r}}({...e},JSON.parse(JSON.stringify(Q))),A={hStep:N.r_window.reduce(((e,t)=>e+t.hStep),0),tStep:N.r_window[0].tStep,lStep:N.l_window[0].lStep},K=j(A,"x"),L.innerHTML+=K)})),document.querySelector(`#dom-item-${e.selectIndex}-bottom-border`).addEventListener("mousedown",(t=>{e.tStep+e.hStep!==C&&(M="bottom-border",J={y:0},z=0,N=function({hStep:e,tStep:t,selectIndex:s},i){let r=i.filter((s=>s.tStep+s.hStep===t+e)).sort(((e,t)=>e.lStep-t.lStep)),a=i.filter((s=>s.tStep===t+e)).sort(((e,t)=>e.lStep-t.lStep));for(var l=0;l<r.length-1;l++)r[l].lStep+r[l].wStep!==r[l+1].lStep&&(s<=r[l].selectIndex?r.splice(l+1):(r.splice(0,l),l=-1));let n=-1,o=-1;return a.forEach((e=>{let t=r.findIndex((t=>e.lStep===t.lStep)),s=r.findLastIndex((t=>e.lStep+e.wStep===t.lStep+t.wStep));t>-1&&(n=0===t?0:Math.min(t,n)),s>-1&&(o=Math.max(s,o))})),a=a.filter((e=>e.lStep>=r[n].lStep&&e.lStep+e.wStep<=r[o].lStep+r[o].wStep)),{t_window:a,b_window:r}}({...e},JSON.parse(JSON.stringify(Q))),A={wStep:N.b_window.reduce(((e,t)=>e+t.wStep),0),lStep:N.b_window[0].lStep,tStep:N.t_window[0].tStep},K=j(A,"y"),L.innerHTML+=K)}))}))}function G(){L.innerHTML="",Q=O(Q),Y(Q)}const Z=e=>{let{division:t,windowId:s,callback:i,maxNum:r,locale:a}=e;if(W=a,E=r,k=document.querySelector(`#${s}`),k.innerHTML=function(e){return`<div id="${e}-container" style="width: 100%; height: 100%; border: 1px solid #aaa; position: relative;"></div>\n        <div class="window-division-bottom-container">\n            <button class="window-division-btn window-division-btn-primary" id="${e}-confirm-btn">${W.$t("common.ok")}</button>\n            <button class="window-division-btn window-division-btn-info" id="${e}-reset-btn">${W.$t("common.reset")}</button>\n            <button class="window-division-btn window-division-btn-info" id="${e}-cancel-btn">${W.$t("common.cancel")}</button>\n        </div>\n    `}(s),L=document.querySelector(`#${s}-container`),R=i,U(),document.querySelector(`#${s}-confirm-btn`).addEventListener("click",(()=>{i.onConfirm&&i.onConfirm(JSON.stringify(Q))})),document.querySelector(`#${s}-cancel-btn`).addEventListener("click",(()=>{i.onCancel&&i.onCancel()})),document.querySelector(`#${s}-reset-btn`).addEventListener("click",(()=>{Q=[{wStep:100,hStep:100,tStep:0,lStep:0,selectIndex:0}],Y(Q)})),t)if([1,4,9,16,25].includes(Number(t)))!function(e=4){_=!0;let t=[],s=100/Math.sqrt(e);for(var i=0;i<e;i++)t.push({selectIndex:i,width:s,height:s});var r=t.map((e=>X(e)));L.innerHTML=r.join("")}(Number(t));else if("string"!=typeof t)i.onError&&i.onError(W.$t("wsPlayer.income.format.error"));else try{Q=O(JSON.parse(t)),Y(Q)}catch(l){i.onError&&i.onError(W.$t("wsPlayer.income.format.error"))}else Y(O())},ee=[3600,1800,900,300];const te=class{constructor(e){this.records=e.records||[],this.currentTime=Number(e.currentTime),this.getCurrentTime=e.getCurrentTime,this.rulerCanvas=null,this.rulerCtx=null,this.canvas_middle_length=0,this.rulerLength=e.rulerLength||{short:15,middle:25,long:40},this.canvasBgColor="transparent",this.rulerColor="#afafaf",this.textColor="#fff",this.recordsColor="#318efd",this.alarmRecordsColor="#f64153",this.cursorColor="#fff",this.oldScaleInfo={scale_step:this.scale_step,scale_step_offsetX:this.scale_step_offsetX},this.scale_step=5*e.rulerScale||10,this.scale_step_offsetX=this.scale_step%30,this.time_ruler_line=ee[0],this.move_1px_time=1,this.moveCurrentTime=0,this.move_offset=0,this.isMouseDown=!1,this.initRuler(e)}initRuler(e){if(!e.canvasId)return e.onError&&e.onError({code:801,message:"请传入canvas的id"});this.rulerCanvas=document.querySelector(e.canvasId),this.rulerCtx=this.rulerCanvas.getContext("2d"),this.canvas_middle_length=Math.floor(this.rulerCanvas.width/2),this.initDrawLine(),this.addEventListener()}initDrawLine(){this.clearRuler(),this.rulerCtx.fillStyle=this.canvasBgColor,this.rulerCtx.fillRect(0,0,this.rulerCanvas.width,this.rulerCanvas.height),this.setScaleStepOffset(),this.setMoveTime(),!this.isMouseDown&&this.getMoveOffset(),this.renderRecordList(),this.renderLeftRuler(),this.renderRightRuler(),this.drawMarkRuler()}updateProgress(e){e&&(e.currentTime||e.records&&e.records.length)?(this.currentTime=e.currentTime||this.currentTime,this.records=e.records||this.records,this.initDrawLine()):(this.currentTime=0,this.records=[],this.initDrawLine())}addEventListener(){let e,t=null;const s=e=>{t=null,this.isMouseDown=!1,this.currentTime=this.currentTime-this.moveCurrentTime,this.moveCurrentTime=0,this.initDrawLine(),this.getCurrentTime&&this.getCurrentTime(parseInt(this.currentTime))},i=e=>{t=null,this.isMouseDown=!1,this.currentTime=this.currentTime-this.moveCurrentTime,this.moveCurrentTime=0,this.initDrawLine()},r=e=>{if(!this.isMouseDown){this.oldScaleInfo={scale_step:this.scale_step,scale_step_offsetX:this.scale_step_offsetX};const t=e.deltaY||e.wheelDelta;console.log(t,"deltaY"),t<0&&(this.scale_step+=5,this.scale_step>=120&&(this.scale_step=115)),t>0&&(this.scale_step-=5,this.scale_step<10&&(this.scale_step=10)),this.initDrawLine()}},a=s=>{if(s.touches&&2===s.touches.length&&null!==t){this.isMouseDown=!1;const e=s.touches[0],i=s.touches[1],a=this.calculateDistance(e,i),l=a/t;return console.log("Scale:",l),r({wheelDelta:l-1}),void(t=a)}if(this.isMouseDown){const t="movementX"in s?s.movementX:s.touches[0].clientX-e;this.move_offset+=t,this.moveCurrentTime+=t*this.move_1px_time,this.initDrawLine()}};this.rulerCanvas.addEventListener("touchstart",(s=>{if(s.touches&&2===s.touches.length){const e=s.touches[0],i=s.touches[1];t=this.calculateDistance(e,i)}else s.preventDefault(),e=s.touches[0].clientX,this.isMouseDown=!0})),this.rulerCanvas.addEventListener("mousedown",(t=>{e=t.clientX,this.isMouseDown=!0})),this.rulerCanvas.addEventListener("touchmove",function(e,t=160){var s,i,r=0;return function(){var a=+new Date;s=this,i=arguments,a-r>t&&(e.apply(s,i),r=a)}}(a)),this.rulerCanvas.addEventListener("mousemove",a),this.rulerCanvas.addEventListener("mouseup",s),this.rulerCanvas.addEventListener("touchend",s),this.rulerCanvas.addEventListener("mouseleave",i),this.rulerCanvas.addEventListener("touchleave",i),this.rulerCanvas.addEventListener("mouseout",i),this.rulerCanvas.addEventListener("mousewheel",r);let l=()=>{this.rulerCanvas.width===this.rulerCanvas.getBoundingClientRect().width&&this.rulerCanvas.height===this.rulerCanvas.getBoundingClientRect().height||(this.rulerCanvas.width=this.rulerCanvas.getBoundingClientRect().width,this.rulerCanvas.height=this.rulerCanvas.getBoundingClientRect().height,this.canvas_middle_length=Math.floor(this.rulerCanvas.width/2),this.initDrawLine()),window.requestAnimationFrame(l)};window.requestAnimationFrame(l)}calculateDistance(e,t){const s=t.clientX-e.clientX,i=t.clientY-e.clientY;return Math.sqrt(s*s+i*i)}clearRuler(){this.rulerCtx&&this.rulerCtx.clearRect(0,0,this.rulerCanvas.width,this.rulerCanvas.height)}setScaleStepOffset(){this.scale_step_offsetX=this.scale_step%30,this.scale_step_offsetX<10&&(this.scale_step_offsetX=10)}setMoveTime(){this.time_ruler_line=ee[Math.floor((this.scale_step+1)/30)],this.move_1px_time=this.time_ruler_line/10/this.scale_step_offsetX}getMoveOffset(){this.move_offset=-Math.floor(this.currentTime%this.time_ruler_line/this.move_1px_time)}renderLeftRuler(){let e=0;for(;e<=this.canvas_middle_length+this.move_offset;){let t=this.canvas_middle_length+this.move_offset-e;e/this.scale_step_offsetX%10==0?(this.drawRulerLine(t,this.rulerLength.long),this.rulerCtx.fillStyle=this.textColor,this.rulerCtx.fillText(this.getRenderTime(0-e),t,this.rulerLength.long+10)):e/this.scale_step_offsetX%10==5?this.drawRulerLine(t,this.rulerLength.middle):this.drawRulerLine(t,this.rulerLength.short),e+=this.scale_step_offsetX}}renderRightRuler(){let e=0;for(;e<=this.canvas_middle_length-this.move_offset;){let t=this.canvas_middle_length+this.move_offset+e;e/this.scale_step_offsetX%10==0?(this.drawRulerLine(t,this.rulerLength.long),this.rulerCtx.fillStyle=this.textColor,this.rulerCtx.fillText(this.getRenderTime(e),t,this.rulerLength.long+10)):e/this.scale_step_offsetX%10==5?this.drawRulerLine(t,this.rulerLength.middle):this.drawRulerLine(t,this.rulerLength.short),e+=this.scale_step_offsetX}}getRenderTime(e){let t=this.currentTime-this.currentTime%this.time_ruler_line;return this.formatter(1e3*(t+e*this.move_1px_time),"HH:mm")}drawRulerLine(e,t,s=this.rulerColor){this.rulerCtx.beginPath(),this.rulerCtx.strokeStyle=s,this.rulerCtx.moveTo(e,0),this.rulerCtx.lineTo(e,t),this.rulerCtx.stroke(),this.rulerCtx.closePath()}renderRecordList(){let e=this.records.filter((e=>!e.isImportant)),t=this.records.filter((e=>e.isImportant));this.renderRecordOnCanvas(e,"records"),this.renderRecordOnCanvas(t,"alarmRecords")}renderRecordOnCanvas(e,t){let s=this.currentTime-this.moveCurrentTime,i=s-this.canvas_middle_length*this.move_1px_time,r=s+this.canvas_middle_length*this.move_1px_time,a=0;this.rulerCtx.beginPath(),e.forEach((e=>{e.endTime<i||e.startTime>r||(e.endTime<r&&e.endTime>i?(a=e.startTime<i?i:e.startTime,this.rulerCtx.fillStyle=this[`${t}Color`],this.rulerCtx.fillRect(this.canvas_middle_length-Math.floor(s-a)/this.move_1px_time,0,(e.endTime-a)/this.move_1px_time,this.rulerCanvas.height)):e.startTime>i&&e.startTime<r?(a=e.endTime>r?r:e.endTime,this.rulerCtx.fillStyle=this[`${t}Color`],this.rulerCtx.fillRect(this.canvas_middle_length-(s-e.startTime)/this.move_1px_time,0,(a-e.startTime)/this.move_1px_time,this.rulerCanvas.height)):e.startTime<i&&e.endTime>r&&(this.rulerCtx.fillStyle=this[`${t}Color`],this.rulerCtx.fillRect(0,0,this.rulerCanvas.width,this.rulerCanvas.height)))})),this.rulerCtx.closePath()}drawMarkRuler(){this.rulerCtx.beginPath(),this.rulerCtx.strokeStyle=this.cursorColor,this.rulerCtx.moveTo(this.canvas_middle_length-2,0),this.rulerCtx.lineTo(this.canvas_middle_length+2,0),this.rulerCtx.lineTo(this.canvas_middle_length,8),this.rulerCtx.lineTo(this.canvas_middle_length-2,0),this.rulerCtx.stroke(),this.rulerCtx.fillStyle=this.cursorColor,this.rulerCtx.fill(),this.rulerCtx.closePath(),this.rulerCtx.fillStyle=this.textColor,this.rulerCtx.fillText(this.formatter(1e3*(this.currentTime-this.moveCurrentTime),"YYYY-MM-DD HH:mm:ss"),this.canvas_middle_length-55,this.rulerCanvas.height-10)}addZero(e){return e<10?"0"+e:e}formatter(e,t){let s=(e=new Date(e)).getFullYear(),i=this.addZero(e.getMonth()+1),r=this.addZero(e.getDate()),a=this.addZero(e.getHours()),l=this.addZero(e.getMinutes()),n=this.addZero(e.getSeconds()),o="";switch(t){case"YYYY-MM-DD HH:mm:ss":o=`${s}-${i}-${r} ${a}:${l}:${n}`;break;case"HH:mm":o=`${a}:${l}`}return o}};!function(e){function t(e,s){if(!(this instanceof t))return new t(e,s);if(e instanceof Node||e instanceof t){if(e.length)return e;this.length=1,this[0]=e}else if(e instanceof Array)this.length=e.length,e.forEach(((e,t)=>{this[t]=e}));else{if("string"!=typeof e)return[];if(e.startsWith("<")||e.startsWith("\n")){const t=(new DOMParser).parseFromString(e,"text/html").body.childNodes;this.length=t.length;for(let e=0;e<this.length;e++)this[e]=t[e]}else{const i=(s=s instanceof t?s[0]:s||document).querySelectorAll(e);this.length=i.length;for(let e=0;e<this.length;e++)this[e]=i[e]}}}t.prototype.each=function(e){for(var t=0;t<this.length;t++)e.call(this[t],t,this[t]);return this},t.prototype.css=function(e,t){if("object"==typeof e)for(const s in e)e.hasOwnProperty(s)&&this.each((function(){this.style[s]=e[s]}));else{if(void 0===t)return getComputedStyle(this[0])[e];this.each((function(){this.style[e]=t}))}return this},t.prototype.click=function(e){return this.each((function(){this.addEventListener("click",e)}))},t.prototype.src=function(e){return this.each((function(){if("IMG"!==this.tagName&&"AUDIO"!==this.tagName&&"VIDEO"!==this.tagName)throw new Error('The "src" method can only be used on <img>, <audio>, or <video> elements.');if(void 0===e)return this.src;this.src=e}))},t.prototype.dblclick=function(e){return this.each((function(){this.addEventListener("dblclick",e)}))},t.prototype.parseHTML=function(e){const t=document.createElement("div");return t.innerHTML=e,t.childNodes},t.prototype.html=function(e){return void 0===e?this[0].innerHTML:(this.each((function(){this.innerHTML=e})),this)},t.prototype.empty=function(){return this.each((function(){for(;this.firstChild;)this.removeChild(this.firstChild)}))},t.prototype.addClass=function(e){return this.each((function(){this.classList.contains(e)||this.classList.add(e)}))},t.prototype.removeClass=function(e){if("string"!=typeof e)throw new TypeError("Invalid class name provided to removeClass method.");return this.each((function(){if(this.classList)this.classList.remove(e);else{const t=this.className.split(/\s+/).filter((t=>t!==e));this.className=t.join(" ")}})),this},t.prototype.remove=function(){for(let e=0;e<this.length;e++)this[e].parentNode&&this[e].parentNode.removeChild(this[e])},t.prototype.children=function(e){const s=[];return this.each((function(){const e=this.children;for(let t=0;t<e.length;t++){const i=e[t];i.nodeType===Node.ELEMENT_NODE&&s.push(i)}})),new t(s,this.context)},t.prototype.siblings=function(e){const s=[];return this.each((function(){const i=this.parentNode;if(i){const r=i.children;for(let i=0;i<r.length;i++){const a=r[i];a!==this&&a.nodeType===Node.ELEMENT_NODE&&(e?e instanceof Node||e instanceof t?e===a&&s.push(a):a.matches(e)&&s.push(a):s.push(a))}}})),new t(s,this.context)},t.prototype.show=function(){return this.each((function(){this.style.display=""})),this},t.prototype.hide=function(){return this.each((function(){this.style.display="none"})),this},t.prototype.append=function(e){if("string"==typeof e)this.each((function(){this.insertAdjacentHTML("beforeend",e)}));else{if(!(e instanceof Node||Array.isArray(e)))throw new TypeError("Invalid content provided to append method.");{const t=Array.isArray(e)?e:[e];this.each((function(){t.forEach((e=>this.appendChild(e)))}))}}return this},t.prototype.attr=function(e,t){if(void 0!==t)return this.each((function(s,i){i.setAttribute(e,t)}));let s;for(let i=0;i<this.length;i++){const t=this[i].getAttribute(e);if(null!==t){s=t;break}}return s},t.prototype.on=function(e,t){return this.each((function(){e.split(/\s+/).forEach((function(e){this.addEventListener(e,t)}),this)}))},t.prototype.html=function(e){return void 0!==e?this.each((function(){this.innerHTML=e})):this[0].innerHTML},t.prototype.text=function(e){return void 0!==e?this.each((function(){this.textContent=e})):this[0].textContent},t.prototype.width=function(e){return void 0!==e?this.each((function(){this.style.width=e})):this[0]&&this[0].clientWidth||null},t.prototype.height=function(e){return void 0!==e?this.each((function(){this.style.height=e})):this[0]&&this[0].clientHeight||null},t.prototype.mouseup=function(e){return this.each((function(){this.addEventListener("mouseup",e)}))},t.prototype.mouseout=function(e){return this.each((function(){this.addEventListener("mouseout",e)}))},t.prototype.mousedown=function(e){return this.each((function(){this.addEventListener("mousedown",e)}))},e.WSPlayerJQ={$:function(s,i){return e.$&&e.$.fn&&function(e,t){if(!e||!t)return!1;let s=e.split("."),i=t.split(".");for(let r=0;r<i.length;r++){if(s[r]>i[r])return!0;if(s[r]<i[r])return!1}return!0}(e.$.fn.jquery,"3.6.0")?e.$(s,i):new t(s,i)}}}(window);class se{constructor(e){if(!e.type)return console.error("type 为必传参数，请校验入参"),!1;if(this.options=e,this.type=e.type,this.config=i.mergeObject(D,e.config),this.config.localeI18n&&m.setI18n(this.config.localeI18n),m.setLocal(this.config.localeLang),this.wsAdaption={selfAdaption:m.$t("wsPlayer.screen.selfAdaption"),stretching:m.$t("wsPlayer.screen.stretching")},t.updateLocale(m),this.setWSUrl=e.setWSUrl,this.WS_TIMEOUT=e.WS_TIMEOUT||1,this.wsProtocol=e.protocol,this.isIntranet=e.isIntranet,this.rememberWSList=[],this.ENV=localStorage.ENV||e.ENV,this.intranetMap=e.intranetMap,this.proxyServerIp=e.proxyServerIp||e.serverIp,this.streamServerIp=e.streamServerIp||e.serverIp,this.prefixUrl=e.prefixUrl?`/${e.prefixUrl}`:"./static",this.rtspResponseTimeout=e.rtspResponseTimeout-0,this.procedure=new I({i18n:m,type:this.type,player:this,playCenterRecordByTime:this.config.playCenterRecordByTime,getRealRtsp:e.getRealRtsp,getRecords:e.getRecords,getRecordRtspByTime:e.getRecordRtspByTime,getRecordRtspByFile:e.getRecordRtspByFile,getTalkRtsp:e.getTalkRtsp,stopTalk:e.stopTalk}),e.pztEl&&this.initPanTilt(e),this.sendMessage=e.receiveMessageFromWSPlayer||function(e,t,s){},this.el=e.el,this.fetchChannelAuthority=e.getChannelAuthority,this.$el=WSPlayerJQ.$("#"+this.el),this.$el.empty(),!this.$el.length)return void this.sendErrorMessage(503);this.width=this.$el.attr("width"),this.height=this.$el.attr("height"),this.$el.height(`${this.height}px`),this.$el.width(`${this.width}px`),this.$el.addClass("ws-player"),this.$el.append('<div class="player-wrapper"></div>'),this.$wrapper=WSPlayerJQ.$(".player-wrapper",this.$el),this.playerList=[],this.playerAdapter="selfAdaption",this.canvas={},this.ctx={},this.showNum=1,this.maxWindow=1,this.recordProgressRuler=null,this.clickRecordsTimer=null,this.$el.attr("inited",!0);let{isVersionCompliance:s,browserType:r,errorCode:a}=i.checkBrowser();switch(this.isHttps="https:"===location.protocol,this.config.isDynamicLoadLib&&this.loadLibPlay(s),this.setMaxWindow(),this.currentDragWindowIndex=-1,this.beforeShowNum=1,this.type){case"real":this.createRealPlayer(e);break;case"record":this.createRecordPlayer(e)}this.setSelectIndex(0),this.setPlayerNum(this.config.division||this.config.num),this.setCanvasGetContext(),this.bindUpdatePlayerWindow=this.__updatePlayerWindow.bind(this),window.addEventListener("resize",this.bindUpdatePlayerWindow),window.wsPlayerManager||(window.wsPlayerManager=new S),this.__updateTopBarStyle(),this.__showBottomBarMore()}setCanvasGetContext(){var e;window.wsCanvasGetContextSet||(window.wsCanvasGetContextSet=!0,HTMLCanvasElement.prototype.getContext=(e=HTMLCanvasElement.prototype.getContext,function(t,s){return"webgl"===t&&(s=Object.assign({},s,{preserveDrawingBuffer:!0})),e.call(this,t,s)}))}setMaxWindow(){let e=parseInt(this.config.maxNum,10);this.maxWindow=e>16?25:e>9?16:e>8?9:e>6?8:e>4?6:e}createRealPlayer(){this.config.showControl?this.__addRealControl():this.$wrapper.addClass("nocontrol"),Array(this.maxWindow).fill(1).forEach(((e,t)=>{let s=new w({locale:m,wrapperDomId:this.el,index:t,wsPlayer:this});this.playerList.push(s)}))}createRecordPlayer(){this.config.showRecordProgressBar&&this.__addRecordControl(),this.config.showControl&&this.__addRealControl(),!this.config.showRecordProgressBar&&!this.config.showControl&&this.$wrapper.addClass("nocontrol"),Array(this.maxWindow).fill(1).forEach(((e,t)=>{let s=new P({locale:m,wrapperDomId:this.el,index:t,wsPlayer:this});this.playerList.push(s)}))}loadScript(e,t){let s=document.createElement("script");s.src=e,document.head.appendChild(s);var i=!1;"function"==typeof t&&(s.onload=s.onreadystatechange=function(){i||s.readyState&&!/loaded|complete/.test(s.readyState)||(s.onload=s.onreadystatechange=null,i=!0,t())})}loadLibPlay(e){let t=this;if(window.loadLibPlayerFlag)return this.config.onlyLoadSingleLib=window.onlyLoadSingleLib,void setTimeout((()=>{t.sendMessage("initializationCompleted")}),300);if(window.loadLibPlayerFlag=!0,window.m_bClientInitialized=!1,window.Module||(window.Module={}),Module.onRuntimeInitialized=function(){setTimeout((()=>{window.m_nModuleInitialized=!0,console.warn("wsplayer 初始化完成，多线程"),t.sendMessage("initializationCompleted")}),300)},!this.isHttps||!e||this.config.onlyLoadSingleLib)return this.loadLibRenderEngine(),this.loadLibIVSDrawer(),!this.config.isWebView&&this.loadLibASPLite(),this.config.onlyLoadSingleLib=!0,window.onlyLoadSingleLib=this.config.onlyLoadSingleLib,void setTimeout((()=>{window.m_nModuleInitialized=!0,console.warn("wsplayer 初始化完成，单线程"),this.sendMessage("initializationCompleted")}),300);try{new SharedArrayBuffer(1),this.loadLibPlaySDK(),this.loadLibStreamClient()}catch(s){this.loadLibRenderEngine(),this.config.onlyLoadSingleLib=!0,window.onlyLoadSingleLib=this.config.onlyLoadSingleLib,setTimeout((()=>{window.m_nModuleInitialized=!0,this.sendMessage("initializationCompleted")}),300)}this.loadLibIVSDrawer(),!this.config.isWebView&&this.loadLibASPLite()}loadPlaySDKInterface(){this.loadScript(`${this.prefixUrl}/WSPlayer/playSDKInterface.js`,null)}loadLibPlaySDK(){let e=`${this.prefixUrl}/WSPlayer/multiThread/libplay.js`;this.loadScript(e,null)}loadLibStreamClient(){let e=`${this.prefixUrl}/WSPlayer/multiThread/libStreamClient.js`;this.loadScript(e,(function(){Multi_Client_Module().then((e=>{window.SCModule=e,window.SCModule._GLOBAL_Init(2),window.m_bClientInitialized=!0}))}))}loadLibRenderEngine(){let e=`${this.prefixUrl}/WSPlayer/commonThread/libRenderEngine.js`;this.loadScript(e,(function(){RenderEngine_Module().then((e=>{window.REModule=e}))}))}loadLibASPLite(){let e=`${this.prefixUrl}/WSPlayer/commonThread/libmavasp_litepacket.js`,t=`${this.prefixUrl}/WSPlayer/commonThread/libmavasp_litepacket.data`;this.loadScript(e,(function(){ASPLite_Module.locateFile=function(e,s){return e.endsWith(".data")?t:s+e},ASPLite_Module(ASPLite_Module).then((e=>{window.ASPLiteModule=e}))}))}loadLibIVSDrawer(){let e=`${this.prefixUrl}/WSPlayer/commonThread/libIVSDrawer.js`;this.loadScript(e,(function(){IVSDrawer_Module().then((e=>{window.IVSModule=e}))}))}playReal(e){this.__getWSUrl(e).then((t=>{e.wsURL=e.wsURL||t,e.playerAdapter=e.playerAdapter||this.playerAdapter;let s=this.playerList[e.selectIndex];s.playType=e.playType,e.selectIndex+1<this.showNum?this.setSelectIndex(e.selectIndex+1):this.selectIndex===e.selectIndex&&s&&this.setPtzChannel(e.channelData),s&&s.init(e)}))}playRecord(e,t={}){return new Promise((s=>{let i=this.playerList[e.selectIndex];if(!i)return console.warn("未传入必传参数 selecIndex");i.playType=e.playType,this.__getWSUrl(e).then((r=>{e.wsURL=e.wsURL||r,e.playerAdapter=e.playerAdapter||this.playerAdapter,e.isPlayback=!0,WSPlayerJQ.$(".ws-record-play",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-pause",this.$el).css({display:"block"});for(let e in t)i[e]=t[e];i&&i.init(e),s()}))}))}openVolume(e){let t=this.playerList[void 0===e?this.selectIndex:e];!t.isAudioPlay&&t.openVolume()}closeVolume(e){let t=this.playerList[void 0===e?this.selectIndex:e];t.isAudioPlay&&t.closeVolume()}setVolume(e,t){let s=this.playerList[void 0===e?this.selectIndex:e];t>0?s.openVolume(t):s.closeVolume()}picCap(e,t){this.playerList[void 0===e?this.selectIndex:e].picCap(t)}play(e){if("real"===this.type)return void this.sendErrorMessage(611,{method:"play",arguments:{index:e}});let t=this.playerList[void 0===e?this.selectIndex:e];t?"pause"===t.status&&t.play():this.sendErrorMessage(601,{method:"play",arguments:{index:e}})}pause(e){if("real"===this.type)return void this.sendErrorMessage(612,{method:"pause",arguments:{index:e}});let t=this.playerList[void 0===e?this.selectIndex:e];t?"playing"===t.status&&t.pause():this.sendErrorMessage(601,{method:"pause",arguments:{index:e}})}playSpeed(e,t){if(![.125,.25,.5,1,1.25,1.5,2,4,8].includes(e))return void console.error(m.$t("wsPlayer.error.206"));if("real"===this.type)return void this.sendErrorMessage(607,{method:"playSpeed",arguments:{speed:e,index:t}});let s=this.playerList[void 0===t?this.selectIndex:t];s?s.playSpeed(parseFloat(e)):this.sendErrorMessage(601,{method:"playSpeed",arguments:{speed:e,index:t}})}setSelectIndex(e){if(e=Number(e),this.selectIndex===e||void 0===e)return;let t=this.playerList[e];if(t){if(this.procedure&&this.procedure.setPlayIndex(e),"record"===this.type){let s=t.status;"playing"===s?(WSPlayerJQ.$(".ws-record-play",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-pause",this.$el).css({display:"block"})):"pause"===s&&(WSPlayerJQ.$(".ws-record-pause",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-play",this.$el).css({display:"block"})),["playing","pause"].includes(s)?this.setTimeLine(t.options.recordList):(this.setTimeLine([]),WSPlayerJQ.$(".ws-record-pause",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-record-play",this.$el).css({display:"block"})),this.__setPlaySpeed("",e)}this.sendMessage("selectWindowChanged",{channelId:(t.options||{}).channelId,playIndex:e}),this.selectIndex=e,this.setPtzChannel((t.options||{}).channelData),this.playerList.forEach(((t,s)=>{s===e?t.$el.removeClass("unselected").addClass("selected"):t.$el.removeClass("selected").addClass("unselected")}))}else this.sendErrorMessage(601,{method:"setSelectIndex",arguments:{index:e}})}createCustomDialog(){let e=`<div id="${this.el}-custom-container" class="custom-division-container"></div>`;this.$el.append(e),Z({locale:m,maxNum:this.config.maxNum,division:localStorage.customDivision||"",windowId:`${this.el}-custom-container`,callback:{onError:e=>{this.sendErrorMessage(610,{mtehod:"customDivisionError",arguments:{},insert:[e]})},onConfirm:e=>{localStorage.customDivision=e,WSPlayerJQ.$(`#${this.el}-custom-container`).remove(),this.setCustomPlayer(e)},onCancel:()=>{WSPlayerJQ.$(`#${this.el}-custom-container`).remove()}}})}createCustomDom(e){this.playerList.forEach(((t,s)=>{t.customDomId=`${t.domId}-${e}`;let i=`\n                <div id="${t.customDomId}" style="display: none;" class="ws-custom-dom-style"></div>\n            `;WSPlayerJQ.$(".ws-full-content",t.$el).append(i),t.customDomElem=WSPlayerJQ.$(`#${t.customDomId}`)}))}numberToList(e){let t=100/e,s=[];for(var i=0;i<e;i++)for(var r=0;r<e;r++)s.push({lStep:r*t,tStep:i*t,wStep:t,hStep:t});return s}renderPlayerNum(e,t){let s=[];s="number"==typeof t?this.numberToList(t):t;let i=100;for(var r=0;r<e.length;r++){let t=e[r].getAttribute("id"),a=t.split("-"),l=Number(a[a.length-1]);s[l]?WSPlayerJQ.$(`#${t}`).css({top:`${s[l].tStep}%`,left:`${s[l].lStep}%`,width:`${s[l].wStep}%`,height:`${s[l].hStep}%`,visibility:"visible","z-index":i--}):WSPlayerJQ.$(`#${t}`).css({top:"150%",left:0,width:0,height:0,visibility:"hidden"})}}resetPlayerScreen(e,t){for(let s=0;s<t;s++)e[s]&&WSPlayerJQ.$(`#${e[s].getAttribute("id")}`).css({top:"150%",left:0,width:0,height:0,visibility:"hidden"})}setPlayerNum(e,t){if(Number(e)>this.config.maxNum)return console.error(m.$t("wsPlayer.error.609"));this.setSelectIndex(0),Number(e)>0?this.setDefaultPlayer(Number(e),t):this.setCustomPlayer(e,t)}setDefaultPlayer(e,s){let i=WSPlayerJQ.$(`#${this.el} .wsplayer-item`);this.resetPlayerScreen(i,this.config.maxNum);let r=parseInt(e)||1;switch(r){case 1:r=1,this.renderPlayerNum(i,1);break;case 2:r=JSON.stringify(t.windowDefaultCustomDivision[2]),this.renderPlayerNum(i,t.windowDefaultCustomDivision[2]);break;case 3:r=JSON.stringify(t.windowDefaultCustomDivision[3]),this.renderPlayerNum(i,t.windowDefaultCustomDivision[3]);break;case 4:r=4,this.renderPlayerNum(i,2);break;case 5:case 6:r=JSON.stringify(t.windowDefaultCustomDivision[6]),this.renderPlayerNum(i,t.windowDefaultCustomDivision[6]);break;case 7:case 8:r=JSON.stringify(t.windowDefaultCustomDivision[8]),this.renderPlayerNum(i,t.windowDefaultCustomDivision[8]);break;case 9:r=9,this.renderPlayerNum(i,3);break;case 10:case 11:case 12:case 13:case 14:case 15:case 16:r=16,this.renderPlayerNum(i,4);break;case 17:case 18:case 19:case 20:case 21:case 22:case 23:case 24:case 25:r=25,this.renderPlayerNum(i,5)}r>this.maxWindow&&(r=this.maxWindow),this.showNum!==r&&(this.showNum=r,!s&&this.sendMessage("windowNumChanged",this.showNum),setTimeout((()=>{this.__updatePlayerWindow()}),200))}setCustomPlayer(e,t){let s=WSPlayerJQ.$(`#${this.el} .wsplayer-item`);this.resetPlayerScreen(s,this.config.maxNum),this.renderPlayerNum(s,JSON.parse(e)),this.showNum=e,!t&&this.sendMessage("windowNumChanged",this.showNum),setTimeout((()=>{this.__updatePlayerWindow()}),200)}switchPlayerScreen(e,t,s){WSPlayerJQ.$(`#${this.el}-${t}`).css({top:`${e[s].tStep}%`,left:`${e[s].lStep}%`,width:`${e[s].wStep}%`,height:`${e[s].hStep}%`}),WSPlayerJQ.$(`#${this.el}-${s}`).css({top:`${e[t].tStep}%`,left:`${e[t].lStep}%`,width:`${e[t].wStep}%`,height:`${e[t].hStep}%`});let i=WSPlayerJQ.$(`#${this.el}-${t} .default-status`).css("transform"),r=WSPlayerJQ.$(`#${this.el}-${s} .default-status`).css("transform");WSPlayerJQ.$(`#${this.el}-${t} .default-status`).css({transform:r}),WSPlayerJQ.$(`#${this.el}-${s} .default-status`).css({transform:i}),WSPlayerJQ.$(`#${this.el}-${t}`).attr("id",`${this.el}-temp`),WSPlayerJQ.$(`#${this.el}-${s}`).attr("id",`${this.el}-${t}`).removeClass(`wsplayer-item-${s}`).addClass(`wsplayer-item-${t}`),WSPlayerJQ.$(`#${this.el}-temp`).attr("id",`${this.el}-${s}`).removeClass(`wsplayer-item-${t}`).addClass(`wsplayer-item-${s}`);let a=this.playerList[t];this.playerList[t]=this.playerList[s],this.playerList[s]=a,this.playerList[t].index=t,this.playerList[t].currentIndex=t,this.playerList[t].domId=`${this.el}-${s}`,this.playerList[s].index=s,this.playerList[s].currentIndex=s,this.playerList[s].domId=`${this.el}-${t}`}changeDragWindow(e){if(this.currentDragWindowIndex<0||this.currentDragWindowIndex===e)return void(this.currentDragWindowIndex=-1);let t=[];t="number"==typeof this.showNum?this.numberToList(Math.sqrt(this.showNum)):JSON.parse(this.showNum),this.switchPlayerScreen(t,this.currentDragWindowIndex,e),this.sendMessage("dragWindow",{dragIndex:this.currentDragWindowIndex,dropIndex:e}),this.currentDragWindowIndex===this.selectIndex&&this.setSelectIndex(e),this.currentDragWindowIndex=-1,setTimeout((()=>{this.__updatePlayerWindow()}),200)}setPlayerAdapter(e){this.playerAdapter!==e&&(["selfAdaption","stretching"].includes(e)?(this.playerAdapter=e,WSPlayerJQ.$(".ws-select-show-option",this.$el).text(this.wsAdaption[e]),this.__updatePlayerWindow()):this.sendErrorMessage(606,{method:"setPlayerAdapter",arguments:{playerAdapter:e}}))}setTimeLine(e=[]){this.config.showRecordProgressBar&&(this.timeList=e,this.recordProgressRuler&&this.recordProgressRuler.updateProgress({records:e}))}setFullScreen(){let e=this.$el[0].children[0];e.requestFullscreen?e.requestFullscreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.msRequestFullscreen&&e.msRequestFullscreen(),this.__updatePlayerWindow()}setExitFullScreen(){document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen()}close(e){let t=Number(e);if(isNaN(t))return void(this.playerList&&this.playerList.forEach((e=>{e.close()})));let s=this.playerList[t];s&&s.close()}destroy(){this.close(),window.removeEventListener("resize",this.bindUpdatePlayerWindow)}__addRealControl(){this.$el.append(`\n            <div class="ws-control">\n                <div class="ws-flex ws-control-record ws-flex-left">\n                    <div class="ws-ctrl-record-icon ws-record-play" style="display: none" title='${m.$t("wsPlayer.play")}'></div>\n                    <div class="ws-ctrl-record-icon ws-record-pause" title='${m.$t("wsPlayer.pause")}'></div>\n                    <div class="ws-ctrl-record-icon ws-record-speed-sub" title='${m.$t("wsPlayer.speed.sub")}'></div>\n                    <div class="ws-ctrl-record-icon ws-record-speed-txt">1x</div>\n                    <div class="ws-ctrl-record-icon ws-record-speed-add" title='${m.$t("wsPlayer.speed.add")}'></div>\n                </div>\n                <div style="flex: 1;"></div>\n                <div class="ws-flex ws-flex-end">\n                    <div id="btn-icon-adaption" class="ws-select-self-adaption">\n                        <div class="ws-select-show select">\n                            <div class="ws-select-show-option">${m.$t("wsPlayer.screen.selfAdaption")}</div>\n                            \x3c!-- 下拉箭头 --\x3e\n                            <img src="${this.prefixUrl}/WSPlayer/icon/spread.png" />\n                        </div>\n                        <div class="ws-self-adaption-type" style="display: none">\n                            <ul class="ws-select-ul">\n                                \x3c!--自定义--\x3e\n                                <li title='${m.$t("wsPlayer.screen.selfAdaption")}' value="selfAdaption" class="ws-select-type-item">${m.$t("wsPlayer.screen.selfAdaption")}</li>\n                                \x3c!--拉伸--\x3e\n                                <li title='${m.$t("wsPlayer.screen.stretching")}' value="stretching" class="ws-select-type-item">${m.$t("wsPlayer.screen.stretching")}</li>\n                            </ul>\n                        </div>\n                    </div>\n                    <span class="ws-ctrl-btn-spread ws-ctrl-btn-spread-1"></span>\n                    <div id="btn-icon-1" class="ws-ctrl-icon one-screen-icon" title='${m.$t("wsPlayer.screen.one")}'></div>\x3c!--单屏--\x3e\n                    <div id="btn-icon-2" class="ws-ctrl-icon two-screen-icon" title='${m.$t("wsPlayer.screen.split",[2])}'></div>\x3c!--2分屏--\x3e\n                    <div id="btn-icon-4" class="ws-ctrl-icon four-screen-icon" title='${m.$t("wsPlayer.screen.split",[4])}'></div>\x3c!--4分屏--\x3e\n                    <div id="btn-icon-9" class="ws-ctrl-icon nine-screen-icon" title='${m.$t("wsPlayer.screen.split",[9])}'></div>\x3c!--9分屏--\x3e\n                    <div id="btn-icon-16" class="ws-ctrl-icon sixteen-screen-icon" title='${m.$t("wsPlayer.screen.split",[16])}'></div>\x3c!--16分屏--\x3e\n                    <div id="btn-icon-25" class="ws-ctrl-icon twenty-five-screen-icon" title='${m.$t("wsPlayer.screen.split",[25])}'></div>\x3c!--25分屏--\x3e\n                    <div id="btn-icon-3" class="ws-ctrl-icon three-screen-icon" title='${m.$t("wsPlayer.screen.split",[3])}'></div>\x3c!--3分屏--\x3e\n                    <div id="btn-icon-6" class="ws-ctrl-icon six-screen-icon" title='${m.$t("wsPlayer.screen.split",[6])}'></div>\x3c!--6分屏--\x3e\n                    <div id="btn-icon-8" class="ws-ctrl-icon eight-screen-icon" title='${m.$t("wsPlayer.screen.split",[8])}'></div>\x3c!--8分屏--\x3e\n                    <div id="btn-icon-custom" class="ws-ctrl-icon custom-screen-icon" title='${m.$t("wsPlayer.screen.custom.split")}'></div>\x3c!--自定义分屏--\x3e\n                    <span class="ws-ctrl-btn-spread ws-ctrl-btn-spread-2"></span>\n                    <div id="btn-icon-close" class="ws-ctrl-icon close-all-video" title='${m.$t("wsPlayer.one.click.off")}'></div>\x3c!--一键关闭--\x3e\n                    <div id="btn-icon-full" class="ws-ctrl-icon full-screen-icon" title='${m.$t("wsPlayer.screen.full")}'></div>\x3c!--全屏--\x3e\n                </div>\n                <div class="ws-ctrl-ellipsis" title='${m.$t("wsplayer.more")}'>\n                    <ul class="ws-ctrl-ellipsis-list">\n                        <li id="ws-record-pause">暂停播放</li>\n                        <li id="ws-record-play">继续播放</li>\n                        <li id="ws-record-speed-add">快进</li>\n                        <li id="ws-record-speed-sub">快退</li>\n                        <li id="ws-select-stretching">拉伸</li>\n                        <li id="ws-select-selfAdaption">自适应</li>\n                        <li id="one-screen-icon">1分屏</li>\n                        <li id="two-screen-icon">2分屏</li>\n                        <li id="four-screen-icon">4分屏</li>\n                        <li id="nine-screen-icon">9分屏</li>\n                        <li id="sixteen-screen-icon">16分屏</li>\n                        <li id="twenty-five-screen-icon">25分屏</li>\n                        <li id="three-screen-icon">3分屏</li>\n                        <li id="six-screen-icon">6分屏</li>\n                        <li id="eight-screen-icon">8分屏</li>\n                        <li id="custom-screen-icon">自定义分屏</li>\n                        <li id="close-all-video">关闭全部</li>\n                        <li id="full-screen-icon">全屏</li>\n                    </ul>\n                </div>\n            </div>\n        `),this.maxWindow<=16&&(WSPlayerJQ.$(".twenty-five-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#twenty-five-screen-icon",this.$el).css({display:"none"})),this.maxWindow<=9&&(WSPlayerJQ.$(".sixteen-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#sixteen-screen-icon",this.$el).css({display:"none"})),this.maxWindow<=8&&(WSPlayerJQ.$(".nine-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#nine-screen-icon",this.$el).css({display:"none"})),this.maxWindow<=6&&(WSPlayerJQ.$(".eight-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#eight-screen-icon",this.$el).css({display:"none"})),this.maxWindow<=4&&(WSPlayerJQ.$(".six-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#six-screen-icon",this.$el).css({display:"none"})),this.maxWindow<=3&&(WSPlayerJQ.$(".four-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#four-screen-icon",this.$el).css({display:"none"})),this.maxWindow<=2&&(WSPlayerJQ.$(".three-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#three-screen-icon",this.$el).css({display:"none"})),1===this.maxWindow&&(WSPlayerJQ.$(".two-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#two-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$(".one-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#one-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$(".custom-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$("#custom-screen-icon",this.$el).css({display:"none"}),WSPlayerJQ.$(".ws-ctrl-btn-spread-2",this.$el).css({display:"none"})),WSPlayerJQ.$("#ws-select-stretching",this.$el).click((()=>{this.setPlayerAdapter("stretching")})),WSPlayerJQ.$("#ws-select-selfAdaption",this.$el).click((()=>{this.setPlayerAdapter("selfAdaption")})),WSPlayerJQ.$(".full-screen-icon",this.$el).click((()=>{this.setFullScreen()})),WSPlayerJQ.$("#full-screen-icon",this.$el).click((()=>{this.setFullScreen()})),WSPlayerJQ.$(".one-screen-icon",this.$el).click((()=>{this.setPlayerNum(1)})),WSPlayerJQ.$("#one-screen-icon",this.$el).click((()=>{this.setPlayerNum(1)})),WSPlayerJQ.$(".two-screen-icon",this.$el).click((()=>{this.setPlayerNum(2)})),WSPlayerJQ.$("#two-screen-icon",this.$el).click((()=>{this.setPlayerNum(2)})),WSPlayerJQ.$(".three-screen-icon",this.$el).click((()=>{this.setPlayerNum(3)})),WSPlayerJQ.$("#three-screen-icon",this.$el).click((()=>{this.setPlayerNum(3)})),WSPlayerJQ.$(".four-screen-icon",this.$el).click((()=>{this.setPlayerNum(4)})),WSPlayerJQ.$("#four-screen-icon",this.$el).click((()=>{this.setPlayerNum(4)})),WSPlayerJQ.$(".six-screen-icon",this.$el).click((()=>{this.setPlayerNum(6)})),WSPlayerJQ.$("#six-screen-icon",this.$el).click((()=>{this.setPlayerNum(6)})),WSPlayerJQ.$(".eight-screen-icon",this.$el).click((()=>{this.setPlayerNum(8)})),WSPlayerJQ.$("#eight-screen-icon",this.$el).click((()=>{this.setPlayerNum(8)})),WSPlayerJQ.$(".nine-screen-icon",this.$el).click((()=>{this.setPlayerNum(9)})),WSPlayerJQ.$("#nine-screen-icon",this.$el).click((()=>{this.setPlayerNum(9)})),WSPlayerJQ.$(".sixteen-screen-icon",this.$el).click((()=>{this.setPlayerNum(16)})),WSPlayerJQ.$("#sixteen-screen-icon",this.$el).click((()=>{this.setPlayerNum(16)})),WSPlayerJQ.$(".twenty-five-screen-icon",this.$el).click((()=>{this.setPlayerNum(25)})),WSPlayerJQ.$("#twenty-five-screen-icon",this.$el).click((()=>{this.setPlayerNum(25)})),WSPlayerJQ.$(".close-all-video",this.$el).click((()=>{this.close()})),WSPlayerJQ.$("#close-all-video",this.$el).click((()=>{this.close()})),WSPlayerJQ.$(".custom-screen-icon",this.$el).click((()=>{this.createCustomDialog()})),WSPlayerJQ.$("#custom-screen-icon",this.$el).click((()=>{this.createCustomDialog()})),WSPlayerJQ.$(".ws-ctrl-ellipsis",this.$el).click((()=>{this.createHideList()})),this.selfAdaptionSelectShow=!1,WSPlayerJQ.$(".ws-select-self-adaption",this.$el).click((e=>{this.selfAdaptionSelectShow?(WSPlayerJQ.$(".ws-self-adaption-type",this.$el).hide(),this.selfAdaptionSelectShow=!1):(WSPlayerJQ.$(".ws-self-adaption-type",this.$el).show(),this.selfAdaptionSelectShow=!0,WSPlayerJQ.$(".ws-select-ul .ws-select-type-item",this.$el).css({background:"none"}),WSPlayerJQ.$(`.ws-select-ul [value=${this.playerAdapter}]`,this.$el).css({background:"#1A78EA"}))})),WSPlayerJQ.$(".ws-self-adaption-type",this.$el).click((e=>{let t=e.target.getAttribute("value");this.setPlayerAdapter(t),WSPlayerJQ.$(".ws-select-show-option",this.$el).text(this.wsAdaption[t])})),"record"!==this.type&&(WSPlayerJQ.$(".ws-control-record",this.$el).css({display:"none"}),WSPlayerJQ.$("#ws-record-pause",this.$el).css({display:"none"}),WSPlayerJQ.$("#ws-record-play",this.$el).css({display:"none"}),WSPlayerJQ.$("#ws-record-speed-add",this.$el).css({display:"none"}),WSPlayerJQ.$("#ws-record-speed-sub",this.$el).css({display:"none"})),WSPlayerJQ.$(".ws-record-pause",this.$el).click((e=>{this.pause()})),WSPlayerJQ.$("#ws-record-pause",this.$el).click((e=>{this.pause()})),WSPlayerJQ.$(".ws-record-play",this.$el).click((e=>{this.play()})),WSPlayerJQ.$("#ws-record-play",this.$el).click((e=>{this.play()})),WSPlayerJQ.$(".ws-record-speed-sub",this.$el).click((e=>{"playing"===this.playerList[this.selectIndex].status&&this.__setPlaySpeed("PREV")})),WSPlayerJQ.$("#ws-record-speed-sub",this.$el).click((e=>{"playing"===this.playerList[this.selectIndex].status&&this.__setPlaySpeed("PREV")})),WSPlayerJQ.$(".ws-record-speed-add",this.$el).click((e=>{"playing"===this.playerList[this.selectIndex].status&&this.__setPlaySpeed("NEXT")})),WSPlayerJQ.$("#ws-record-speed-add",this.$el).click((e=>{"playing"===this.playerList[this.selectIndex].status&&this.__setPlaySpeed("NEXT")}))}createHideList(){let e=WSPlayerJQ.$(".ws-ctrl-ellipsis-list",this.$el);""===e.css("display")||"none"===e.css("display")?e.css("display","block"):e.css("display","none")}__setPlaySpeed(e,t){let s,i,r=[{value:.125,label:"0.125x"},{value:.25,label:"0.25x"},{value:.5,label:"0.5x"},{value:1,label:"1x"},{value:1.25,label:"1.25x"},{value:1.5,label:"1.5x"},{value:2,label:"2x"},{value:4,label:"4x"},{value:8,label:"8x"}],a=this.playerList[void 0===t?this.selectIndex:t];r.some(((l,n)=>{if(l.value===a.speed)return i="PREV"===e?n-1:"NEXT"===e?n+1:n,s=r[i],!s||(i?i===r.length-1?WSPlayerJQ.$(".ws-record-speed-add",this.$el).css({cursor:"not-allowed"}):(WSPlayerJQ.$(".ws-record-speed-sub",this.$el).css({cursor:"pointer"}),WSPlayerJQ.$(".ws-record-speed-add",this.$el).css({cursor:"pointer"})):WSPlayerJQ.$(".ws-record-speed-sub",this.$el).css({cursor:"not-allowed"}),WSPlayerJQ.$(".ws-record-speed-txt",this.$el).text(s.label),"playing"===a.status&&this.playSpeed(s.value,t),!0)}))}__addRecordControl(){this.$el.append('\n            <div class="ws-control ws-record-control">\n            <canvas id="wsplayer-record-progress" height="60" class="ws-record-area"></canvas>\n            </div>\n        '),this.recordProgressRuler=new te({canvasId:`#${this.el} canvas#wsplayer-record-progress`,records:[],currentTime:0,rulerLength:{short:10,middle:20,long:25},rulerScale:2,getCurrentTime:e=>{this.clickRecordsTimer&&clearTimeout(this.clickRecordsTimer),this.clickRecordsTimer=setTimeout((()=>{clearTimeout(this.clickRecordsTimer),this.clickRecordsTimer=null}),1500),this.clickRecordTimeLine(e),this.play()}})}__setTimeRecordArea(e=[]){if(e.length){let t=WSPlayerJQ.$(".ws-record-control",this.$el).width();this.canvas.width=t;let s=[],i=[],r=this.ctx.createLinearGradient(0,0,0,60);r.addColorStop(0,"rgba(77, 201, 233, 0.1)"),r.addColorStop(1,"#1c79f4");let a=this.ctx.createLinearGradient(0,0,0,60);a.addColorStop(0,"rgba(251, 121, 101, 0.1)"),a.addColorStop(1,"#b52c2c"),e.forEach((e=>{e.width=(e.endTime-e.startTime)*t/86400;let r=new Date(1e3*e.startTime),a=r.getHours(),l=r.getMinutes(),n=r.getSeconds();e.left=(3600*a+60*l+n)/86400*t,e.isImportant?i.push(e):s.push(e)})),s.forEach((e=>{this.ctx.clearRect(e.left,0,e.width,60),this.ctx.fillStyle=r,this.ctx.fillRect(e.left,0,e.width,60)})),i.forEach((e=>{this.ctx.clearRect(e.left,0,e.width,60),this.ctx.fillStyle=a,this.ctx.fillRect(e.left,0,e.width,60)}))}else this.canvas.width=0}__setPlayingTime(e,t,s,i,r,a,l){this.selectIndex===e&&(new Date(`${t}-${s}-${i} ${r}:${a}:${l}`).getTime(),this.clickRecordsTimer||this.recordProgressRuler&&this.recordProgressRuler.updateProgress({currentTime:new Date(`${t}-${s}-${i} ${r}:${a}:${l}`).getTime()/1e3}))}showMsgInWindow(e,t){let s=this.playerList[void 0===e?this.selectIndex:e];s?s.showMsgInWindow(t):this.sendErrorMessage(601,{method:"showMsgInWindow",arguments:{index:e,msg:t}})}autoSetWSUrl(e,t){return new Promise(((s,i)=>{let r=(e=e.map((e=>t+"://"+e))).filter((e=>this.rememberWSList.includes(e)));if(r.length)return void s(r[0]);let a=0,l=r=>{let n,o=new WebSocket(r),c=!1;o.onopen=()=>{c=!0,this.rememberWSList.push(r),clearTimeout(n),o.close(),s(e[a-1])},o.onerror=()=>{clearTimeout(n),c=!1,a>=e.length?i({code:-105,message:m.$t("wsPlayer.auto.recognition.failed",e[0].replace(t+":",window.location.protocol))}):l(e[a++])},n=setTimeout((()=>{!c&&o.close()}),1e3*this.WS_TIMEOUT)};l(e[a++])}))}_getStreamWSUrl(e,s){if(this.intranetMap)for(let t in this.intranetMap)t.includes(s)&&(s=this.intranetMap[t]);if(!s)return void console.warn("please configure 【streamServerIp】 in new PlayerManager({...})");if(s.includes(":"))return`${e}://${s}`;let i="";return i="wss"===e?"real"===this.type?t.websocketPorts.realmonitor_wss:t.websocketPorts.playback_wss:"real"===this.type?t.websocketPorts.realmonitor_ws:t.websocketPorts.playback_ws,`${e}://${s}:${i}`}_getNginxWSUrl(e,s,i){let r=s.match(/\d{1,3}(\.\d{1,3}){3}/g)[0];r||(r=s.split("//")[1].split(":")[0]);let a="real"===this.type?t.websocketPorts.realmonitor:t.websocketPorts.playback;return this.proxyServerIp?`${e}://${this.proxyServerIp}/${a}?serverIp=${i||r}`:(console.warn("please configure 【proxyServerIp】 in new PlayerManager({...})\n now i use 【location.host】 instead of 【proxyServerIp】 or set configure【localStorage.wsplayerProxyIp】"),`${e}://${localStorage.wsplayerProxyIp||location.host}/${a}?serverIp=${i||r}`)}__getWSUrl(e){let{rtspURL:t,streamServerIp:s,wsList:i,playType:r,wssDirect:a,talkData:l}=e;return new Promise(((e,l)=>{if("url"===r)return void e();if(localStorage.wsUrl)return void e(localStorage.wsUrl);if(this.setWSUrl)return void e(this.setWSUrl(i));let n=this.wsProtocol||(this.isHttps?"wss":"ws");"DEVTOOL"===this.ENV&&this.config.useNginxProxy?e(this._getNginxWSUrl("wss",t,s||this.streamServerIp)):this.autoSetWSUrl(i,n,a).then((t=>{e(t)})).catch((i=>{if(console.warn(i.message),this.isHttps&&this.config.useNginxProxy&&this.proxyServerIp)return void e(this._getNginxWSUrl("wss",t,"ICC"===this.ENV&&s||this.streamServerIp||s));let r=this._getStreamWSUrl(n,this.streamServerIp||s);r&&e(r)}))}))}__updatePlayerWindow(){setTimeout((()=>{this.playerList.forEach((e=>{e.updateAdapter(this.playerAdapter)}))}),24),this.__updateTopBarStyle(),this.__renderDefaultStats(),this.__showBottomBarMore()}__renderDefaultStats(){setTimeout((()=>{this.playerList.forEach((e=>{e.renderDefaultStats()}))}),24)}__updateTopBarStyle(){setTimeout((()=>{this.playerList.forEach((e=>{e.updateTopBarStyle()}))}),24)}__showBottomBarMore(){setTimeout((()=>{this.$el.width()<("real"===this.type?540:700)?WSPlayerJQ.$(".ws-ctrl-ellipsis",this.$el).css({display:"unset"}):WSPlayerJQ.$(".ws-ctrl-ellipsis",this.$el).css({display:"none"})}),24)}__startTalk(e){this.procedure&&this.procedure.startTalk(e)}talkByUrl(e){this.playerList[e.selectIndex].talkByUrl(e)}playRealVideo(e){let{channelList:t=[],streamType:s="2",windowIndex:i}=e;this.procedure&&this.procedure.playRealVideo(t,s,i)}realByUrl(e){if(this.selectIndex=e.selectIndex,!e.rtspURL&&!e.wsURL)return console.error(m.$t("wsPlayer.error.106"));this.playReal({playType:"url",rtspURL:e.rtspURL,wsURL:e.wsURL,channelId:e.channelId,streamType:e.streamType,streamServerIp:e.streamServerIp,playerAdapter:e.playerAdapter,selectIndex:e.selectIndex-0,channelData:e.channelData||{}})}changeStreamType(e,t,s){this.procedure&&this.procedure.playRealVideo([e],t,s)}playRecordVideo(e){this.procedure&&this.procedure.getRecordList(e)}recordByUrl(e){if(!e.records&&(e.records=[]),!e.rtspURL&&!e.wsURL)return console.error(m.$t("wsPlayer.error.106"));e.records.length||console.warn(m.$t(m.$t("wsPlayer.error.107"))),e.records=e.records.sort(((e,t)=>e.startTime-t.startTime)),this.playRecord({playType:"url",wsURL:e.wsURL,rtspURL:e.rtspURL,records:e.records||[],channelId:e.channelId,startTime:e.startTime||e.records[0]&&e.records[0].startTime,endTime:e.endTime||e.records&&[e.records.length-1].endTime,playerAdapter:e.playerAdapter,selectIndex:e.selectIndex-0,channelData:e.channelData||{},playRecordByTime:"boolean"!=typeof e.playRecordByTime||e.playRecordByTime,ssId:e.records&&e.records[0]&&e.records[0].ssId||""})}clickRecordTimeLine(e){let t=this.playerList[this.selectIndex];this.timeList.some((s=>{if(e>=s.startTime&&e<s.endTime){if(t.options.playRecordByTime&&t.options.ssId===s.ssId)t.playSDK.SetSeekTime(e-t.options.startTime);else{if("url"===t.playType)return this.sendMessage("switchStartTime",{timeStamp:e,channelData:t.options.channelData,hasVideoRecord:!0}),!0;this.procedure&&this.procedure.clickRecordTimeLine(e,s.ssId)}return!0}}))||("url"===t.playType&&this.sendMessage("switchStartTime",{timeStamp:e,channelData:t.options.channelData,hasVideoRecord:!1}),console.warn("所选时间点无录像"))}jumpPlayByTime(e,t){if("real"===this.type)return void this.sendErrorMessage(613,{method:"jumpPlayByTime",arguments:{time:e,selectIndex:t}});let s=this.playerList[void 0===t?this.selectIndex:t];if(!s)return void this.sendErrorMessage(601,{method:"jumpPlayByTime",arguments:{time:e,selectIndex:t}});if(!["playing","pause"].includes(s.status))return;let i=e.split(":"),r=60*(i[0]||0)*60+60*(i[1]||0)+1*(i[2]||0);3!==i.length||!r&&0!==r||r>=86400?this.sendErrorMessage(605,{method:"jumpPlayByTime",arguments:{time:e}}):this.clickRecordTimeLine(r)}playNextRecord(e,t){this.procedure&&this.procedure.playNextRecord(e,t)}videoClosed(e,t,s,i){this.sendMessage("closeVideo",{selectIndex:e,changeVideoFlag:t,channelData:s,customDomId:i}),this.procedure&&this.procedure.videoClosed(e,t,s)}sendErrorMessage(e,s={}){let i=t.errorInfo[e];s.insert&&(s.insert.forEach(((e,t)=>{i=i.replace(`{${t}}`,e)})),delete s.insert),this.sendMessage("errorInfo",{errorCode:e,errorInfo:i,errorData:s})}startLocalRecord(e,t,s,i){let r=this.playerList[void 0===e?this.selectIndex:e];r?"playing"===r.status||"pause"===r.status?r.isRecording?this.sendErrorMessage(602,{method:"startLocalRecord",arguments:{selectIndex:e,name:t,size:s}}):(r.isRecording=!0,r.startRecord(t,s,i),WSPlayerJQ.$(".record-icon",r.$el).addClass("recording")):this.sendErrorMessage(603,{method:"startLocalRecord",arguments:{selectIndex:e,name:t,size:s}}):this.sendErrorMessage(601,{method:"startLocalRecord",arguments:{selectIndex:e,name:t,size:s}})}stopLocalRecord(e){let t=this.playerList[void 0===e?this.selectIndex:e];t?t.isRecording?(t.isRecording=!1,t.playSDK.StopRecord(),WSPlayerJQ.$(".record-icon",t.$el).removeClass("recording")):this.sendErrorMessage(604,{method:"stopLocalRecord",arguments:{selectIndex:e}}):this.sendErrorMessage(601,{method:"stopLocalRecord",arguments:{selectIndex:e}})}setLoading(e,t,s){let i=this.playerList[void 0===e?this.selectIndex:e];i?i.setLoading(t,s):this.sendErrorMessage(601,{method:"setLoading",arguments:{selectIndex:e}})}setIvs({showIvs:e,selectIndex:t,ivsType:s=[1,2]}){let i=this.playerList[void 0===t?this.selectIndex:t];i?(i.playSDK.OpenIVS(),e?(s.includes(1)||i.playSDK.SetIvsEnable(3,0),s.includes(2)||(i.playSDK.SetIvsEnable(1,0),i.playSDK.SetIvsEnable(14,0))):i.playSDK.CloseIVS()):this.sendErrorMessage(601,{method:"setIvs",arguments:{showIvs:e,selectIndex:t,ivsType:s}})}initPanTilt(e){this.panTilt=new b({...e,prefixUrl:this.prefixUrl,locale:m},this)}setPtzChannel(e){this.panTilt&&this.panTilt.setChannel(e)}stopTalk(e){this.procedure&&this.procedure.stopTalk({data:e})}}__publicField(se,"version","1.3.2"),e.WSPlayer=se,e.default=se,Object.defineProperties(e,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})}));
