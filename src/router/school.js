export default [
  {
    path: "/school",
    component: () => import("@/layout/SchoolLayout.vue"),
    redirect: "/school/home",
    meta: {
      identity_id: 2,
    },
    children: [
      {
        path: "/public/message/center",
        component: () => import("@/views/school/message/center.vue"),
      },
      {
        path: "/school/index",
        component: () => import("@/views/school/index.vue"),
      },
      {
        path: "/school/qualifications",
        component: () => import("@/views/system/school.vue"),
      },
      {
        path: "/school/qualificationsApplication",
        component: () => import("@/views/school/qualificationsApplication/index.vue"),
      },
      {
        path: "/school/beian",
        component: () => import("@/views/system/beian.vue"),
      },
      {
        path: "/school/home",
        meta: {
          no_bread: true
        },
        component: () => import("@/views/school/home.vue"),
      },
      {
        path: "/children/home",
        meta: {
          no_bread: true
        },
        component: () => import("@/views/school/childhome.vue"),
      },
      {
        path: "/teacher/home",
        meta: {
          no_bread: true
        },
        component: () => import("@/views/school/teacherhome.vue"),
      },
      {
        path: "/school/class",
        component: () => import("@/views/system/class/list.vue"),
      },
      {
        path: "/teacher/home",
        meta: {
          no_bread: true
        },
        component: () => import("@/views/school/teacherhome.vue"),
      },
      {
        path: "/school/class",
        component: () => import("@/views/system/class/list.vue"),
      },
      // {
      //   path: "/school/finance/data",
      //   component: () => import("@/views/school/finance/data.vue"),
      // },
      {
        path: "/school/finance/income",
        component: () => import("@/views/school/finance/list.vue"),
      },
      {
        path: "/school/finance/expenses",
        component: () => import("@/views/school/finance/list.vue"),
      },
      {
        path: "/school/finance/bill",
        component: () => import("@/views/school/finance/bill.vue"),
      },
      {
        path: "/school/goods/list",
        component: () => import("@/views/school/goods/list.vue"),
      },
      // {
      //   path: "/school/goods/data",
      //   component: () => import("@/views/school/goods/data.vue"),
      // },
      {
        path: "/school/goods/record",
        component: () => import("@/views/school/goods/record.vue"),
      },
      {
        path: "/organ/inspect/list",
        component: () => import("@/views/inspect/list.vue"),
      },
      {
        path: "/organ/inspect/list/:type",
        component: () => import("@/views/inspect/listCity.vue"),
      },
      {
        path: "/organ/inspect/index",
        meta: { en_title: 'school_inspect', no_bread: true },
        component: () => import("@/views/inspect/index.vue"),
      },
      {
        path: "/organ/inspect/detail",
        meta: { en_title: 'school_inspect', no_bread: true },
        component: () => import("@/views/inspect/detail.vue"),
      },
      {
        path: "/organ/inspect/report",
        meta: { en_title: 'school_inspect', no_bread: true },
        component: () => import("@/views/inspect/report.vue"),
      },
      {
        path: "/school/calendar",
        component: () => import("@/views/system/calendar.vue"),
      },
      {
        path: "/student/potential/list",
        meta: {
          no_bread: true
        },
        component: () => import("@/views/student/potential/list.vue"),
      },
      {
        path: "/student/potential/data",
        meta: {
          no_bread: true
        },
        component: () => import("@/views/student/potential/data.vue"),
      },
      {
        path: "/student/potential/report",
        component: () => import("@/views/student/potential/report.vue"),
      },
      {
        path: "/student/potential/intention",
        component: () => import("@/views/student/intention.vue"),
      },
      {
        path: "/student/potential/channel",
        component: () => import("@/views/student/channel.vue"),
      },
      {
        path: "/student/potential/introduce",
        component: () => import("@/views/student/introduce/index.vue"),
      },
      {
        path: "/school/poster",
        component: () => import("@/views/school/poster/gallery.vue"),
      },
      {
        path: "/work/student/list",
        component: () => import("@/views/student/list.vue"),
      },
      {
        path: "/work/observation",
        component: () => import("@/views/school/observation/list.vue"),
      },
      {
        path: "/work/oneday",
        component: () => import("@/views/school/oneday/list.vue"),
      },
      {
        path: "/work/plan",
        component: () => import("@/views/school/plan/list.vue"),
      },
      {
        path: "/attendance/student",
        component: () => import("@/views/school/attendance/student.vue"),
      },
      {
        path: "/attendance/teacher/setting",
        component: () => import("@/views/school/attendance/setting.vue"),
      },
      {
        path: "/attendance/teacher/day",
        component: () => import("@/views/school/attendance/teacher.vue"),
      },
      {
        path: "/attendance/teacher/month",
        component: () => import("@/views/school/attendance/teacher.vue"),
      },
      {
        path: "/attendance/classday",
        component: () => import("@/views/school/attendance/class.vue"),
      },
      {
        path: "/attendance/classmonth",
        component: () => import("@/views/school/attendance/class.vue"),
      },
      {
        path: "/attendance/manage/record",
        component: () => import("@/views/school/attendance/record.vue"),
      },
      {
        path: "/attendance/manage/leave",
        component: () => import("@/views/school/attendance/leave.vue"),
      },
      {
        path: "/work/record",
        component: () => import("@/views/school/liferecord/list.vue"),
      },
      {
        path: "/work/report",
        component: () => import("@/views/school/report/list.vue"),
      },
      {
        path: "/personnel/staff/list",
        component: () => import("@/views/school/staff/list.vue"),
      },
      {
        path: "/healthcare/control/",
        children: [
          {
            path: "/healthcare/control/disease",
            component: () => import("@/views/school/disease/list.vue"),
          },
          {
            path: "/healthcare/control/sanitation",
            component: () => import("@/views/school/sanitation/list.vue"),
          },
        ]
      },
      {
        path: "/healthcare/examination",
        component: () => import("@/views/school/examination/list.vue"),
      },
      {
        name: 'HealthcareVaccine',
        path: "/healthcare/vaccine/list",
        component: () => import("@/views/school/vaccine/list.vue"),
      },
      {
        path: "/public/examination/detail/:id",
        component: () => import("@/views/school/examination/detail.vue"),
      },
      {
        path: "/healthcare/recipe",
        component: () => import("@/views/school/recipe/list.vue"),
      },
      {
        path: "/healthcare/food",
        component: () => import("@/views/school/food/list.vue"),
      },
      {
        path: "/healthcare/tell",
        component: () => import("@/views/school/tellparent/list.vue"),
      },
      {
        path: "/healthcare/allergy",
        component: () => import("@/views/school/allergy/list.vue"),
      },
      {
        path: "/school/daycheck/list",
        component: () => import("@/views/school/daycheck/list.vue"),
      },
      {
        path: "/school/daycheck/info",
        component: () => import("@/views/school/daycheck/info.vue"),
      },
      {
        path: "/parenting/news",
        component: () => import("@/views/school/news/list.vue"),
      },
      {
        path: "/parenting/classnews",
        component: () => import("@/views/school/classnews/list.vue"),
      },
      {
        path: "/parenting/parents",
        component: () => import("@/views/school/parents/list.vue"),
      },
      {
        path: "/public/news/:id",
        component: () => import("@/views/school/news/detail.vue"),
      },
      {
        path: "/parenting/notice/school",
        component: () => import("@/views/system/message.vue"),
      },
      {
        path: "/parenting/notice/class",
        component: () => import("@/views/system/message.vue"),
      },
      {
        path: "/school/enterexam/info",
        component: () => import("@/views/school/enterexam/info.vue"),
      },
      {
        path: "/secure/video",
        component: () => import("@/views/school/camera/list.vue"),
      },
      {
        path: "/school/hardware/tv/list",
        component: () => import("@/views/system/hardware/list.vue"),
      },
      {
        path: "/school/hardware/classbrand/list",
        component: () => import("@/views/system/hardware/list.vue"),
      },
      {
        path: "/school/hardware/monitor/list",
        component: () => import("@/views/school/monitor/list.vue"),
      },

      {
        path: "/school/hardware/monitor/auth",
        component: () => import("@/views/school/monitor/auth.vue"),
      },
      {
        path: "/school/hardware/monitor/live",
        component: () => import("@/views/school/monitor/live.vue"),
      },
      {
        path: "/school/hardware/monitor/playback",
        component: () => import("@/views/school/monitor/playback.vue"),
      },
      {
        path: "/school/hardware/screen/list",
        component: () => import("@/views/system/hardware/list.vue"),
      },
      {
        path: "/school/hardware/screen/setting",
        component: () => import("@/views/system/hardware/screen/setting.vue"),
      },
      {
        path: "/school/hardware/attend/list",
        component: () => import("@/views/system/hardware/list.vue"),
      },
      {
        path: "/school/hardware/attend/material",
        component: () => import("@/views/system/hardware/attend/material.vue"),
      },
      {
        path: '/school/hardware/attend/face',
        component: () => import("@/views/system/hardware/school/face.vue"),
      },
      {
        path: '/school/hardware/attend/record',
        component: () => import("@/views/system/hardware/school/record.vue"),
      },
      {
        path: '/school/attend/record',
        component: () => import("@/views/system/hardware/school/record.vue"),
      },
      {
        path: '/organ/approval/list',
        component: () => import("@/views/school/approval/list.vue"),
      },
      {
        path: '/organ/approval/record',
        component: () => import("@/views/school/approval/list.vue"),
      },
      {
        path: '/school/childcare-subsidies/list',
        component: () => import("@/views/school/childcare-subsidies/list.vue"),
      },
      {
        path: '/school/childcare-subsidies/project-list',
        component: () => import("@/views/school/childcare-subsidies/project-list.vue"),
      },
      {
        path: '/school/childcare-subsidies/info',
        component: () => import("@/views/school/childcare-subsidies/info.vue"),
      },
      {
        path: "/school/questionnaire/list",
        component: () => import("@/views/questionnaire/list.vue"),
      }, {
        path: '/school/rental/list',
        component: () => import("@/views/school/rental/list.vue"),
      }, {
        path: '/school/rental/info',
        component: () => import("@/views/school/rental/info.vue"),
      },
      {
        path: '/school/gzh/content-setting',
        component: () => import("@/views/school/gzh/template-settings.vue"),
      }, {
        path: '/school/gzh/menu',
        component: () => import("@/views/school/gzh/menu.vue"),
      }, {
        path: '/school/gzh/notice',
        component: () => import("@/views/school/gzh/notice.vue"),
      }, {
        path: '/school/rental/list',
        component: () => import("@/views/school/rental/list.vue"),
      }, {
        path: '/school/rental/info',
        component: () => import("@/views/school/rental/info.vue"),
      },
      {
        path: "/healthcare/stage",
        component: () => import("@/views/government/health/stage.vue"),
      },
      {
        path: '/school/guidecenter/parenting/list',
        component: () => import("@/views/guidecenter/health/parenting.vue"),
      },
      {
        path: '/school/guidecenter/expert/list',
        component: () => import("@/views/guidecenter/expert/message.vue"),
      },
      {
        path: '/school/guidecenter/expert/live',
        component: () => import("@/views/guidecenter/expert/live.vue"),
      },
      {
        path: '/school/tuition/record',
        component: () => import("@/views/school/tuitionManage/record.vue"),
      }, {
        path: '/school/tuition/bill',
        component: () => import("@/views/school/tuitionManage/detail.vue"),
      }, {
        path: '/school/tuition/recordDetail',
        component: () => import("@/views/school/tuitionManage/detail.vue"),
      }, {
        path: '/school/seal',
        component: () => import("@/views/school/tuitionManage/seal.vue"),
      }, {
        path: "/school/invoice/list",
        component: () => import("@/views/school/invoice/list.vue")
      },{
        path: "/school/hardware/air/data",
        component: () => import("@/views/system/hardware/air/data.vue"),
      },
      {
        path: "/school/hardware/air/list",
        component: () => import("@/views/system/hardware/air/list.vue"),
      },
      {
        path: "/school/hardware/childband/data",
        component: () => import("@/views/system/hardware/childband/data.vue"),
      },
      {
        path: "/school/hardware/childband/list",
        component: () => import("@/views/system/hardware/childband/list.vue"),
      },
      {
        path: "/school/hardware/cup/list",
        component: () => import("@/views/system/hardware/cup/list.vue"),
      },
      {
        path: "/school/operation/list",
        component: () => import("@/views/school/operation/list.vue"),
      },
      {
        path: "/school/institution/device/2",
        component: () => import("@/views/school/institution/device/index.vue")
      },
      {
        path: "/school/gas",
        component: () => import("@/views/school/institution/gas/index.vue")
      },
      {
        path: "/school/gas/check",
        component: () => import("@/views/school/institution/gas/check.vue")
      },
      {
        path: "/school/proof",
        component: () => import("@/views/school/institution/proof/index.vue")
      },
      {
        path: "/school/proof/check",
        component: () => import("@/views/school/institution/proof/check.vue")
      },
      {
        path: "/school/institution/agency/1",
        component: () => import("@/views/school/institution/agency/index.vue")
      },
      {
        path: "/school/institution/agency/2",
        component: () => import("@/views/school/institution/agency/index.vue")
      },
      {
        path: "/school/institution/agency/3",
        component: () => import("@/views/school/institution/agency/index.vue")
      },
      {
        path: "/school/institution/agency/4",
        component: () => import("@/views/school/institution/agency/index.vue")
      },
      {
        path: "/school/institution/agency/5",
        component: () => import("@/views/school/institution/agency/index.vue")
      },
      {
        path: "/school/institution/house",
        component: () => import("@/views/school/institution/house/index.vue")
      },
      {
        path: "/school/institution/house/check",
        component: () => import("@/views/school/institution/house/check.vue")
      },
      {
        path: "/school/institution/fire",
        component: () => import("@/views/school/institution/fire/index.vue")
      },
      {
        path: "/school/institution/fire/check",
        component: () => import("@/views/school/institution/fire/check.vue")
      },
      {
        path: "/school/disinfect/list",
        component: () => import("@/views/school/institution/disinfect/index.vue")
      },
      {
        path: "/school/disinfect/check",
        component: () => import("@/views/school/institution/disinfect/check.vue")
      },
      {
        path: "/school/sanitation/task",
        component: () => import("@/views/school/institution/sanitation/index.vue")
      },
      {
        path: "/school/sanitation/check",
        component: () => import("@/views/school/institution/sanitation/check.vue")
      },
      {
        path: "/school/robotweek/list",
        component: () => import("@/views/system/robotweek/list.vue"),
      },
      {
        path: "/manage/activity/index/:type(2)",//1协会端2学校端
        component: () => import("@/views/system/activity/index.vue")
      },
      {
        path: "/school/milk",
        component: () => import("@/views/school/foodsafe/milk/list.vue")
      },
      {
        path: "/school/emergency/training",
        component: () => import("@/views/school/institution/emergency/check.vue")
      },
      {
        path: "/school/orderFood",
        component: () => import("@/views/school/foodsafe/orderFood/index.vue")
      },
      {
        path: "/school/inspection",
        component: () => import("@/views/school/foodsafe/inspection/list.vue")
      },
      {
        path: "/school/byo",
        component: () => import("@/views/school/foodsafe/byo/list.vue")
      },
      {
        path: "/school/recipe",
        component: () => import("@/views/school/foodsafe/recipe/list.vue")
      },
      {
        path: "/school/keepSample",
        component: () => import("@/views/school/foodsafe/keepSample/list.vue")
      },
      {
        path: "/school/report",
        component: () => import("@/views/school/foodsafe/report/list.vue")
      },
      {
        path: "/school/entrance/leave",
        component: () => import("@/views/school/entrance/leave/list.vue"),
      },
      {
        path: "/school/entrance/transfer",
        component: () => import("@/views/school/entrance/transfer/list.vue"),
      },
      {
        path: "/school/entrance/enter",
        component: () => import("@/views/school/entrance/enter/list.vue")
      },
      {
        path: "/school/entrance/guid",
        component: () => import("@/views/school/entrance/enter/guid.vue")
      },
      {
        path: "/school/personnel/familyDoctorTransfer",
        component: () => import("@/views/school/personnel/familyDoctor/familyDoctorTransfer.vue")
      },
      {
        path: "/school/personnel/familyDoctorContract",
        component: () => import("@/views/school/personnel/familyDoctor/familyDoctorContract.vue")
      },
      {
        path: "/school/personnel/familyDoctorService",
        component: () => import("@/views/school/personnel/familyDoctor/familyDoctorService.vue")
      },
      {
        path: "/school/personnel/familyDoctorContract",
        component: () => import("@/views/school/personnel/familyDoctor/familyDoctorContract.vue")
      },
      {
        path: "/school/personnel/familyDoctorService",
        component: () => import("@/views/school/personnel/familyDoctor/familyDoctorService.vue")
      },
      {
        path: "/school/personnel/parentalFeedback",
        component: () => import("@/views/school/personnel/parentalFeedback/index.vue")
      },
      {
        path: "/school/personnel/templatemanager",
        component: () => import("@/views/templatemanager/list.vue"),
      },
      {
        path: "/school/agencyInspection",
        component: () => import("@/views/school/agencyInspection/index.vue")
      },
      // {
      //   path: "/school/childCareManage/guid",
      //   component: () => import("@/views/school/childCareManage/guid.vue")
      // },
      {
        path: "/school/recruitment/job",
        component: () => import("@/views/school/recruitment/job/list.vue")
      },
      {
        path: "/school/recruitment/mgr",
        component: () => import("@/views/school/recruitment/mgr/list.vue")
      },
      {
        path: "/school/feedback/list",
        component: () => import("@/views/feedback/list.vue")
      },
      // 指导知识
      {
        path: "/school/guidance/baby",
        component: () => import("@/views/government/guidance/baby.vue"),
      },
      {
        path: "/school/guidance/health",
        component: () => import("@/views/government/guidance/health.vue"),
      },
      {
        path: "/school/checkFeedback/list",
        component: () => import("@/views/school/checkFeedback/list.vue"),
      },
      {
        path: "/school/experience",
        component: () => import("@/views/school/experience/list.vue"),
      },
      {
        path: '/school/train/trainList',
        component: () => import('@/views/train/Train.vue')
      }, {
        path: '/school/train/certType',
        component: () => import('@/views/train/CertType.vue')
      }, {
        path: '/school/train/certList',
        component: () => import('@/views/train/CertList.vue')
      }
    ],
  },
  {
    path: "/school/select",
    name: "select",
    meta: {
      identity_id: 2,
    },
    component: () => import("@/views/ChangeSchool.vue"),
  },
];
