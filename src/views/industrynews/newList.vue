<template>
  <div
    class="page"
    v-loading="pageLoading"
    :class="{ 'page-nobread': !breadStore.breadcrumbShow }"
  >
    <el-scrollbar class="page-table" v-if="isList && !isDetail">
      <div
        class="page-search"
        :class="seachOpen ? 'page-search-open' : 'page-search-close'"
      >
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="标题名称：">
                  <el-input v-model="tableSearch.title" placeholder="请输入" clearable />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="动态类别：">
                  <el-select v-model="tableSearch.type_level_id" placeholder="请选择" clearable>
                    <el-option
                      v-for="(item, index) in type_arr"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                      >{{ item.name }}</el-option
                    >
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="发布人：">
                  <el-input
                    v-model="tableSearch.user_name"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
              </el-col>

              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="发布状态：">
                  <el-select
                    v-model="tableSearch.submit_status"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in status_arr"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="12">
                <el-form-item label="发布时间：">
                  <el-date-picker
                    value-format="YYYY-MM-DD HH:mm:ss"
                    format="YYYY-MM-DD HH:mm:ss"
                    v-model="startendtime"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="请选择"
                    end-placeholder="请选择"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button
                type="primary"
                @click="seachOpen = true"
                link
                v-if="!seachOpen"
                >展开</el-button
              >
              <el-button
                type="primary"
                @click="seachOpen = false"
                link
                v-if="seachOpen"
                >收起</el-button
              >
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt">
        <div class="left">
          <el-button type="primary" @click="add">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <Plus /> </el-icon
            >新增</el-button
          >
        </div>
      </div>
      <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
        <el-table-column
          type="index"
          min-width="60"
          fixed="left"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="title"
          min-width="220"
          fixed="left"
          label="标题"
        ></el-table-column>
        <el-table-column label="动态类别" align="center" min-width="120">
          <template #default="scope">
            {{
              scope.row.type_level_id === 4
                ? "政策类别"
                : scope.row.type_level_id === 5
                ? "市场趋势"
                : scope.row.type_level_id === 6
                ? "行业研究"
                : "—"
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="created_at"
          min-width="160"
          align="center"
          label="创建时间"
        ></el-table-column>
        <el-table-column
          prop="operate_time"
          min-width="160"
          align="center"
          label="发布时间"
        ></el-table-column>
        <el-table-column
          prop="user_name"
          min-width="100"
          align="center"
          label="发布人"
        ></el-table-column>
        <el-table-column label="发布状态" align="center" min-width="120">
          <template #default="scope">
            {{ id2name(scope.row.submit_status, status_arr) }}
          </template>
        </el-table-column>
        <el-table-column width="200" fixed="right">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button
              type="primary"
              v-if="scope.row.submit_status === 0"
              @click="publish(scope.row)"
              link
              >发布</el-button
            >
            <el-button type="primary" v-else @click="publish(scope.row)" link
              >停发</el-button
            >
            <el-button
              type="primary"
              @click="edit(scope.row)"
              link
              :disabled="scope.row.submit_status === 1"
              >编辑</el-button
            >
            <el-button type="primary" @click="see(scope.row)" link
              >查看</el-button
            >
            <el-button
              type="danger"
              @click="del(scope.row)"
              link
              :disabled="scope.row.submit_status === 1"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :page-size="tablePage.per_page"
          background
          layout="prev, pager, next , sizes, total"
          :total="tablePage.total"
          v-model:current-page="tablePage.page"
          v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-scrollbar>
    <el-scrollbar
      ref="scrollFormlEl"
      class="page-form back-page"
      v-show="!isList"
    >
      <el-page-header
        :icon="ArrowLeft"
        title="返回"
        @back="
          isList = true;
          breadStore.breadcrumbShow = true;
        "
      >
        <template #content>
          <p class="b20 mb20 mt20">
            {{ formData.id == 0 ? "新增" : "编辑" }}{{ baseName }}
          </p>
        </template>
      </el-page-header>
      <div
        class="edit-box flex w1200 margin-auto"
        style="padding-bottom: 100px"
      >
        <Editor
          output-format="html"
          v-model="formData.content"
          ref="editor"
          api-key="qoas9g4yril4tvqnj224o60x0d5w04lt3a5vbx5fnysk35vg"
          :tinymceScriptSrc="tinymceScriptSrc"
          :init="EditorConfig"
        />
        <div class="news-right" style="width: 45%">
          <el-form
            :model="formData"
            class="news-message"
            :rules="formDataRules"
            ref="formEl"
            :scroll-to-error="true"
          >
            <el-form-item label="标题名称" prop="title">
              <el-input
                autosize
                maxlength="200"
                class="title"
                v-model="formData.title"
                placeholder="输入标题"
              />
            </el-form-item>
            <el-form-item label="动态类别" prop="type_level_id">
              <el-radio-group v-model="formData.type_level_id">
                <el-radio value="4" label="4">政策类别</el-radio>
                <el-radio value="5" label="5">市场趋势</el-radio>
                <el-radio value="6" label="6">行业研究</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="发布区域" prop="area">
              <el-row :gutter="20">
                <el-col :sm="12" :md="12" :lg="12">
                  <el-select
                    @change="getCity"
                    :popper-append-to-body="false"
                    v-model="formData.province"
                    disabled
                    placeholder="请选择省份"
                  >
                    <el-option
                      v-for="(item, index) in provinceList"
                      :key="index"
                      :label="item.text"
                      :value="item.code"
                      >{{ item.text }}</el-option
                    >
                  </el-select>
                </el-col>
                <el-col :sm="12" :md="12" :lg="12">
                  <el-select
                    @change="getArea"
                    :popper-append-to-body="false"
                    v-model="formData.city"
                    disabled
                    placeholder="请选择城市"
                  >
                    <el-option
                      v-for="(item, index) in cityList"
                      :key="index"
                      :label="item.text"
                      :value="item.code"
                      >{{ item.text }}</el-option
                    >
                  </el-select>
                </el-col>
                <el-col :sm="12" :md="12" :lg="12">
                  <el-form-item prop="area">
                    <el-select
                      :popper-append-to-body="false"
                      v-model="formData.area"
                      placeholder="请选择区/县"
                    >
                      <el-option
                        v-for="(item, index) in areaList"
                        :key="index"
                        :label="item.text"
                        :value="item.code"
                        >{{ item.text }}</el-option
                      >
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item prop="school_class" label="托育机构类型">
              <el-checkbox-group v-model="formData.school_class">
                <el-checkbox
                  v-for="(item, index) in school_class_arr"
                  :key="index"
                  :label="item.id"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
            <el-form-item prop="is_top" label="是否置顶">
              <el-radio-group v-model="formData.is_top">
                <el-radio
                  v-for="(item, index) in top_arr"
                  :key="index"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="发布类型">
              <el-radio-group v-model="formData.operate_status">
                <el-radio value="1" label="1">即时发布</el-radio>
                <el-radio value="2" label="2">定时发布</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              prop="operate_time"
              label="发布时间"
              v-if="formData.operate_status == '2'"
            >
              <el-date-picker
                style="margin-top: 12px"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                v-model="formData.operate_time"
                type="datetime"
                clearable
                placeholder="发布时间"
              />
            </el-form-item>
          </el-form>
          <div class="btn flex-bt" style="width: 306px; margin-left: 20px">
            <div class="left">
              <el-button @click="submit(formEl, 1)" type="primary"
                >发布</el-button
              >
              <el-button @click="submit(formEl, 0)">保存</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <el-scrollbar
      ref="scrollFormlEl1"
      class="page-form back-page"
      v-show="isDetail"
    >
      <el-page-header
        :icon="ArrowLeft"
        title="返回"
        @back="
          isDetail = false;
          breadStore.breadcrumbShow = true;
        "
      >
        <template #content>
          <p class="b20 mb20 mt20">
            {{ "查看详情" }}
          </p>
        </template>
      </el-page-header>
      <div class="edit-box flex w1200 margin-auto">
        <div class="news-detail">
          <div class="title line-2">{{ formData.title }}</div>
          <div class="time">{{ formData.operate_time }}</div>
          <div class="des">{{ formData.abstract }}</div>
          <div class="content" v-html="formData.content || ''"></div>
        </div>
        <!-- <div class="news">
          <p class="b16">推荐新闻</p>
        </div> -->
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { dayjs } from 'element-plus'
import {
  Plus,
  Headset,
  ArrowLeft,
  RefreshRight,
  CircleCloseFilled,
} from "@element-plus/icons-vue";
import { watch, onMounted, reactive, ref, computed } from "vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
import { useConfigStore } from "@/stores/config-store";
import { useRoute } from "vue-router";
import { str2arr, id2name, port_ids } from "@/config/select";
import { upload } from "@/assets/utils/obs.js";
import { useBreadStore } from '@/stores/bread-store';
import Editor from '@/assets/utils/@tinymce/tinymce-vue'
import { EditorConfig } from "@/config/editor";

const tinymceScriptSrc = 'https://zhihuituoyupt.obs.cn-east-3.myhuaweicloud.com/web/assets/tinymce/tinymce.js'
const route = useRoute();
const breadStore = useBreadStore();
const { organizations_id, identity_id, organizations } = useAuthStore().userInfo;
const { schoolId, isSuperAdmin } = useAuthStore();
const { provinceList, getCityApi, getAreaApi } = useConfigStore();
let baseUrl = "/api/back/systemdata";
let baseName = ref('');
let seachOpen = ref(false);
let type_arr = [
  {
    id: 4,
    name: '政策类别'
  },
  {
    id: 5,
    name: '市场趋势'
  },
  {
    id: 6,
    name: '行业研究'
  }
]
let school_class_arr = [
  {
    id: 1,
    name: '公办'
  },
  {
    id: 2,
    name: '公办民营'
  },
  {
    id: 3,
    name: '民办'
  },
  {
    id: 4,
    name: '民办公助'
  }
]
let status_arr = [
  {
    id: 0,
    name: '保存未发布'
  },
  {
    id: 1,
    name: '已发布'
  }
]
let top_arr = [
  {
    id: 1,
    name: '是'
  },
  {
    id: 0,
    name: '否'
  }
]
let tabList = ref([])
let isDetail = ref(false);
let isList = ref(true);
let editor = ref(null);
let formEl = ref(null);
let scrollFormlEl = ref(null);
let scrollFormlEl1 = ref(null);
onMounted(() => {

})
const resetSearch = async () => {
  tableSearch.title = "";
  tableSearch.type = 6;
  tableSearch.user_name = '';
  tableSearch.submit_status = '';
  tableSearch.operate_time_begin = '';
  tableSearch.operate_time_end = '';
  tablePage.page = 1;
  list();
};

const search = async () => {
  tablePage.page = 1;
  list();
};
let pageLoading = ref(true);
let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let startendtime = computed({
  get() {
    return [tableSearch.operate_time_begin, tableSearch.operate_time_end];
  },
  set(newValue) {
    [tableSearch.operate_time_begin, tableSearch.operate_time_end] = [...newValue];
  },
});
let apiLoading = ref(false);
let tableSearch = reactive({
  operate_time_begin: "", //发布时间开始时间
  operate_time_end: "", //发布时间结束时间
  type: 6, //6:政策类别,7:市场趋势,8:行业研究
  type_level_id: null,
  title: "", //标题
  user_name: "", //发布人
  submit_status: "", //发布状态0:保存未发布，1：已发布
});
// 省市区街道
const cityList = ref([]);
const areaList = ref([]);
async function getCity() {
  formData.city = "";
  cityList.value = await getCityApi(formData.province);
}
async function getArea() {
  formData.area = "";
  areaList.value = await getAreaApi(formData.city);
}
let tableData = ref([]);
const list = async (data) => {
  isList.value = true;
  pageLoading.value = true;
  tableData.value = [];

  await api({
    url: `${baseUrl}/industryFundList`,
    method: 'post',
    data: {
      ...tableSearch,
      per_page: tablePage.per_page,
      page: tablePage.page
    }
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
};

let formData = reactive({
  title: "", // 标题
  type_level_id: "", // 动态类别
  province: "", // 省
  city: "", // 市
  area: "", // 区/县
  school_class: [], // 托育机构类型
  is_top: "", // 是否置顶 0 不置顶 1：置顶
  submit_status: "", // 发布/保存 0:保存未发布，1：已发布
  operate_status: "" // 发布类型 1 既时发布 2：定时发布
});
const add = async () => {

  setFormData();
  breadStore.breadcrumbShow = false;
  getAddress()
  setTimeout(() => {
    formEl.value.clearValidate()
    isList.value = false;
  }, 0);
};
function getAddress() {
  const { province, city } = organizations;
  formData.province = province.toString();
  getCity()
  formData.city = city.toString();
  getArea()
}

let formDataRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
  ],
  type_level_id: [
    { required: true, message: '请选择动态类别', trigger: 'change' },
  ],
  school_class: [
    { required: true, message: '请选择托育机构类型', trigger: 'change' },
  ],
  area: [
    { required: true, message: '请选择区/县', trigger: 'change' }
  ],
  is_top: [
    { required: true, message: '请选择是否置顶', trigger: 'change' },
  ],
  operate_time: [
    { required: true, message: '请选择发布时间', trigger: 'change' },
  ],
}
const setFormData = (data = {}) => {
  console.log(data.is_top);

  formData.id = data?.id || "";
  formData.title = data?.title || "";
  formData.content = data?.content || '';
  formData.type_level_id = data?.type_level_id?.toString() || '';
  formData.province = data?.province?.toString() || '';
  formData.city = data?.city?.toString() || '';
  formData.area = data?.area?.toString() || '';
  formData.school_class = data?.id ? data?.school_class.split(',').map(Number) : [];
  formData.operate_time = data?.operate_time || '';
  formData.is_top = data.is_top;
  formData.operate_status = data.operate_status?.toString() || "";
};
const submit = async (formEl, submit_status) => {
  if (!formEl) return
  let valid = await formEl.validate((valid) => { })
  console.log(valid);
  if (!valid) {
    return false;
  }

  apiLoading.value = true;

  let apitype = 'addindustryFund';
  formData.submit_status = submit_status
  let params = { ...formData }
  if (params.id) {
    apitype = 'editIndustryFund'
  } else {
    delete params.id
  }
  params.school_class = params.school_class.join(',')
  let res = await api({
    url: `${baseUrl}/${apitype}`,
    method: "POST",
    data: params
  })
  apiLoading.value = false;
  if (res) {
    ElMessage.success(params.id ? "修改成功" : '新增成功');
    isList.value = true;
    await list();
  } else {
    ElMessage.error(params.id ? "修改失败" : '新增失败');
  }
};

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};

const see = async (data) => {
  if (!data.id) {
    return
  }
  pageLoading.value = true;
  await api({
    url: `${baseUrl}/industryFundDetail`,
    method: "post",
    data: {
      id: data.id
    }
  }).then((res) => {
    pageLoading.value = false;
    if (res) {
      setFormData(res);
      breadStore.breadcrumbShow = false;
      setTimeout(() => {
        scrollFormlEl1.value.setScrollTop(0);
        isDetail.value = true;
      }, 0);
    }
  });
};

const edit = async (data) => {
  if (!data.id) {
    return
  }
  getDetail(data.id)
  breadStore.breadcrumbShow = false;
  isList.value = false;
};
const getDetail = async (id) => {
  await api({
    url: `${baseUrl}/industryFundDetail`,
    method: 'post',
    data: {
      id: id
    }
  }).then((res) => {
    if (res) {
      getAddress()
      setFormData(res);
    }
  });
};
const publish = async (row) => {
  ElMessageBox.confirm(`确认${row.submit_status === 0 ? '发布' : '停发'}该动态吗`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        method: 'post',
        url: `${baseUrl}/release`,
        data: {
            id: row.id,
            submit_status: row.submit_status === 0 ? 1 : 0
        }
      });
      if (res) {
        ElMessage.success(`${row.submit_status === 0 ? '发布成功' : '已停发'}该动态吗`);
        list();
      }
    })
    .catch(() => { });
};

const del = async (data) => {
  ElMessageBox.confirm("确认删除该条动态吗？", "", {
    dangerouslyUseHTMLString: true,
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        method: 'post',
        url: `${baseUrl}/delIndustryFund`,
        data: {
          id: data.id,
        },
      });
      if (res) {
        ElMessage.success("删除成功");
        list();
      }
    })
    .catch(() => { });
};
async function getCate() {
  let res = await api({
    method: 'get',
    url: `${baseUrl}/cate`,
  });
  if (res) {
    tabList.value = res
  }
}
watch(
  () => route,
  (newVal) => {
    tableSearch.title = ''
    list();
  },
  { immediate: true, deep: true }
);
watch(() => formData.operate_status, (newVal) => {
  if (newVal == '1') {
    formData.operate_time = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
  }
},{ immediate: true, deep: true })
</script>
<style lang="scss">
:deep(.mce-content-body img) {
  max-width: 100% !important;
  height: auto !important;
}

.customClass {
  z-index: 1500;
}

.preview-dialog {
  padding: 0 !important;
  border-radius: 0 !important;

  .el-dialog__header {
    display: none !important;
  }

  .el-dialog__body {
    padding: 0 0 20px 0 !important;
    border-radius: 0 !important;
    margin: 0 !important;
    overflow: auto;
  }

  .title {
    width: 375px;
    height: 44px;
    background: #ffffff;
    text-align: center;
    line-height: 44px;
  }

  .detail-title {
    font-size: 22px;
    font-weight: 500;
    color: #111111;
    line-height: 30px;
    width: 345px;
    margin: 20px 15px 9px;
  }

  .detail-time {
    font-size: 14px;
    color: #aeb0bc;
    line-height: 22px;
    width: 345px;
    margin: 0 15px 20px;
  }

  .detail-des {
    font-size: 15px;
    color: #787c8d;
    line-height: 22px;
    width: 345px;
    margin: 0 15px 20px;
  }

  .detail-content {
    font-size: 16px;
    color: #262937;
    line-height: 28px;
    margin-bottom: 20px;
    width: 345px;
    margin: 0 auto;

    img,
    video {
      max-width: 100% !important;
      width: 100% !important;
      height: auto !important;
      display: block;
    }
    video {
      margin: 8px auto;
    }
  }
}

.news-message {
  width: 506px;
  background: #ffffff;
  border-radius: 10px;
  border: 2px solid #eee;
  margin-left: 20px;
  padding: 24px;
  margin-bottom: 20px !important;

  .title .el-textarea__inner {
    border: none !important;
    box-shadow: none !important;
    font-size: 20px;
    font-weight: 500;
    padding: 0;
  }

  .description .el-textarea__inner {
    font-size: 14px !important;
    box-shadow: none !important;
    padding: 0;
  }
}
</style>
<style lang="scss" scoped>
.dialog-p {
  line-height: 20px;
  color: #262626;
  margin-bottom: 16px;

  span {
    color: rgba(0, 0, 0, 0.65);
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  margin: 0;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader .avatar {
  width: 256px;
  height: 108px;
}

.el-icon.avatar-uploader-icon {
  font-size: 14px;
  font-style: normal;
  color: #8c939d;
  width: 256px;
  height: 108px;
  text-align: center;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
}

.music-uploader {
  height: 36px;
}

.music-uploader-icon {
  height: 36px;
  display: flex;
  align-items: center;
}

.remove {
  position: absolute;
  right: 6px;
  top: 6px;
  z-index: 10;
}

.news {
  padding: 24px;
}
</style>
