<template>
    <div class="page tabs-page" v-loading="pageLoading">
        <el-scrollbar class="page-table" v-if="detailData == '' && submitData == ''">
            <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
                <el-form inline>
                    <div class="left">
                        <el-row :gutter="20">
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="所属区县" prop="code">
                                    <el-select v-model="tableSearch.area" placeholder="请选择" @change="router.replace(route.path + '?code=' + tableSearch.area)">
                                        <el-option label="江阳区" :value="510502"></el-option>
                                        <el-option label="龙马潭区" :value="510504"></el-option>
                                        <el-option label="纳溪区" :value="510503"></el-option>
                                        <el-option label="泸县" :value="510521"></el-option>
                                        <el-option label="合江县" :value="510522"></el-option>
                                        <el-option label="叙永县" :value="510524"></el-option>
                                        <el-option label="古蔺县" :value="510525"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :sm="16" :md="16" :lg="16">
                                <el-form-item label="时间范围：">
                                    <el-date-picker value-format="YYYY-MM-DD" format="YYYY-MM-DD" v-model="startendtime"
                                        type="daterange" start-placeholder="请选择" end-placeholder="请选择" />
                                </el-form-item>
                            </el-col>
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="检查状态：">
                                    <el-select v-model="tableSearch.notice_status" placeholder="请选择">
                                        <el-option v-for="(item, index) in check_status_arr" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="right">
                        <el-form-item>
                            <el-button type="primary" @click="search">查询</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                            <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
                            <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <div class="table-title flex-bt" style="height: 24px;">
            </div>
            <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
                <el-table-column type="index" min-width="60" fixed="left" label="序号"
                    :index="indexSort"></el-table-column>
                <el-table-column prop="area_name" min-width="120" label="区县"></el-table-column>
                <el-table-column prop="start_date" min-width="120" label="开始时间"></el-table-column>
                <el-table-column prop="end_date" min-width="120" label="结束时间"></el-table-column>
                <el-table-column prop="title" min-width="220" label="通知标题"></el-table-column>
                <el-table-column prop="notice_status" min-width="120" label="检查状态">
                    <template #default="scope">
                        {{ id2name(scope.row.notice_status, check_status_arr) }}
                    </template>
                </el-table-column>
                <el-table-column prop="checked_school_num" min-width="120" label="已上传机构数"></el-table-column>
                <el-table-column min-width="120" label="未上传机构数">
                    <template #default="scope">
                        {{ scope.row.school_num - scope.row.checked_school_num }}
                    </template>
                </el-table-column>
                <el-table-column width="200" fixed="right">
                    <template #header>
                        <span style="padding-left: 2px">操作</span>
                    </template>
                    <template #default="scope">
                        <el-button type="primary" @click="jumpDetail(scope.row)" link>
                            查看详情
                        </el-button>
                        <el-button type="primary" @click="detailData = (scope.row)" link>
                            查看监管报告
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
                    :total="tablePage.total" v-model:current-page="tablePage.page"
                    v-model:page-size="tablePage.per_page" :page-sizes="[10, 20, 30, 40, 50]"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </el-scrollbar>
        <CheckDetail :detailData="detailData" :close="() => { detailData = false; }"
            v-if="detailData && submitData == ''">
        </CheckDetail>
    </div>
</template>
<script setup>
import { Plus } from "@element-plus/icons-vue";
import { reactive, ref, onMounted, computed, provide, watch } from "vue";
import api from "@/axios.js";
import { useRoute, useRouter } from "vue-router";
import { id2name } from "@/config/select";
import { usePageFeatures, useSelect } from "./hooks/useDevice.js";
import { useAuthStore } from "@/stores/auth-store";
import CheckSubmit from "./components/check-submit.vue";
import CheckDetail from "./components/check-detail.vue";

const schoolId = '';
const route = useRoute();
const router = useRouter();
const { apis } = usePageFeatures();
const { house_type } = useSelect();

let seachOpen = ref(false)
let pageLoading = ref(false);
let tableSearch = reactive({
    title: '',
    start_date: '',
    notice_status: '',
    end_date: '',
    area: ''
});
let detailData = ref('');
let submitData = ref('');

let startendtime = computed({
    get() {
        return [tableSearch.start_date, tableSearch.end_date];
    },
    set(newValue) {
        [tableSearch.start_date, tableSearch.end_date] = [...newValue];
    },
});
const tableData = ref([]);
const tablePage = reactive({
    total: 0,
    page: 1,
    per_page: 10,
});
function indexSort(n) {
    let sort = tablePage.total - ((tablePage.page - 1) * tablePage.per_page + n);
    if (sort > 0) {
        return sort;
    }
}
const search = () => {
    getList();
};

function jumpDetail(row) {
    if (pageType.value == 1) {
        router.push('/manage/house/check/detail?id=' + row.id)
    } else if (pageType.value == 2) {
        router.push('/manage/gas/check/detail?id=' + row.id)
    } else if (pageType.value == 3) {
        router.push('/manage/proof/check/detail?id=' + row.id)
    }
}
let build_type = ref([
    {
        id: 1,
        name: '房屋装修'
    },
    {
        id: 2,
        name: '设施设备'
    },
    {
        id: 3,
        name: '装饰材料'
    }
]);
let status_arr = [{ "id": 0, "name": "未上传" }, { "id": 1, "name": "已上传" }]
let check_status_arr = [{ "id": 2, "name": "未开始" }, { "id": 1, "name": "进行中" }, { "id": 3, "name": "已结束" }]

const resetSearch = async () => {
    tableSearch.title = '';
    tableSearch.notice_status = '';
    tableSearch.start_date = '';
    tableSearch.end_date = '';
    tablePage.page = 1;
    getList();
};
const handleSizeChange = () => {
    getList();
};
const handleCurrentChange = () => {
    getList();
};
const push = (row) => {
    ElMessageBox.confirm(`确认上传该条数据吗？`, "提示", {
        type: "warning",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
    }).then(async () => {
        const res = await api({
            method: "post",
            url: apis.change,
            data: {
                id: row.id,
            },
        });
        if (res) {
            ElMessage.success("上传成功");
            getList();
        }
    });
};
const getList = async () => {
    try {
        pageLoading.value = true;
        const { total, ...restTablePage } = tablePage;
        const res = await api({
            url: baseUrl + '/noticelist',
            params: {
                ...tableSearch,
                ...restTablePage,
                school_id: schoolId,
                area: route.query.code
            },
        });
        if (res) {
            tableData.value = res.data || [];
            tablePage.total = res.total || 0;
        }
        pageLoading.value = false;
    } catch (err) {
        pageLoading.value = false;
        console.error(err);
    }
};
onMounted(() => {
});
let baseUrl = ''
let baseName = ref('');
let pageType = ref(1);
watch(
    () => route,
    (newVal) => {
        if (route.path.indexOf('/manage/house') != - 1) {
            baseUrl = '/api/back/goverhouse';
            baseName.value = '机构房屋建设'
            pageType.value = 1
            getList();
        } else if (route.path.indexOf('/manage/gas') != - 1) {
            baseUrl = '/api/back/govergasequipment'
            baseName.value = '机构燃气安全'
            pageType.value = 2
            getList();
        } else if (route.path.indexOf('/manage/proof') != - 1) {
            baseUrl = '/api/back/goverproof'
            baseName.value = '机构防暴设备设施'
            pageType.value = 3
            getList();
        }
    },
    { immediate: true, deep: true }
);

</script>
<style lang="scss" scoped>
.excellent {
    color: #70b603;
}

.good {
    color: #02a7f0;
}

.qualified {
    color: #f59a23;
}

.unqualified {
    color: #d9001b;
}

.unrelease {
    color: #b8741a;
}

.released {
    color: #70b603;
}
</style>
