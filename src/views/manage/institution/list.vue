<template>
  <el-scrollbar class="page-table" v-loading="pageLoading" v-if="detailId == ''">
    <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
      <el-form inline>
        <div class="left">
          <el-row :gutter="20">
            <el-col :sm="8" :md="8" :lg="8">
              <el-form-item label="机构名称：">
                <el-input v-model="tableSearch.name" placeholder="请填写" />
              </el-form-item>
            </el-col>
            <el-col :sm="8" :md="8" :lg="8">
              <el-form-item label="机构类型：">
                <el-select v-model="tableSearch.school_class" placeholder="请选择">
                  <el-option v-for="(item, index) in school_class" :key="index" :label="item.name"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="8" :md="8" :lg="8">
              <el-form-item label="机构性质：">
                <el-select v-model="tableSearch.free_type" placeholder="请选择">
                  <el-option v-for="(item, index) in free_type" :key="index" :label="item.name"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="8" :md="8" :lg="8">
              <el-form-item label="状态：">
                <el-select v-model="tableSearch.gas_status" placeholder="请选择">
                  <el-option v-for="(item, index) in status_arr" :key="index" :label="item.name"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
            <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="table-title flex-bt">
      <div class="left">
        <el-date-picker value-format="YYYY" format="YYYY" v-model="tableSearch.year" @change="getList" type="year"
          placeholder="请选择" />
      </div>
      <div class="right">
      </div>
    </div>
    <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
      <el-table-column type="index" min-width="60" fixed="left" label="序号" :index="indexSort"></el-table-column>
      <el-table-column prop="name" min-width="180" label="机构名称"></el-table-column>
      <el-table-column prop="area_name" min-width="120" label="所属区县"></el-table-column>
      <el-table-column prop="street_name" min-width="120" label="所属街道"></el-table-column>
      <el-table-column v-if="pageType == 1" prop="build_type_name" min-width="150" label="建设类型"></el-table-column>
      <el-table-column v-if="pageType == 1" prop="community" min-width="150" label="建设区域"></el-table-column>
      <el-table-column v-if="pageType == 1" prop="build_start" min-width="140" label="建设开始时间"></el-table-column>
      <el-table-column v-if="pageType == 1" prop="build_end" min-width="140" label="建设结束时间"></el-table-column>

      <el-table-column v-if="pageType == 2" prop="name" min-width="150" label="制度名称"></el-table-column>
      <el-table-column v-if="pageType == 2" prop="status_name" min-width="140" label="上传状态"></el-table-column>
      <el-table-column v-if="pageType == 2" prop="upload_time" min-width="160" label="上传时间"></el-table-column>
      <el-table-column width="100" fixed="right">
        <template #header>
          <span style="padding-left: 2px">操作</span>
        </template>
        <template #default="scope">
          <el-button type="primary" @click="see(scope.row)" link>
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
        :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
        :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </el-scrollbar>
  <el-scrollbar v-loading="pageLoading" v-else>
    <PageHouseDetail v-if="pageType == 1" />
    <PageDetail v-if="pageType == 2" />
  </el-scrollbar>
</template>
<script setup>
import { Plus } from "@element-plus/icons-vue";
import { reactive, ref, onMounted, watch } from "vue";
import api from "@/axios.js";
import { useRoute, useRouter } from "vue-router";
import { id2name, school_class, free_type } from "@/config/select";
import { usePageFeatures, useSelect } from "./hooks/useDevice.js";
import { useAuthStore } from "@/stores/auth-store";
import { dayjs } from 'element-plus'
import hideNumber from "@/views/components/hideNumber.vue";
import PageHouseDetail from "./components/page-house-detail.vue";
import PageDetail from "./components/page-detail.vue";

const schoolId = '';
const route = useRoute();
const router = useRouter();
const { apis } = usePageFeatures();
const { house_type } = useSelect();
let pageLoading = ref(false);
let tableSearch = reactive({
  name: '',
  school_class: '',
  gas_status: '',
  free_type: '',
  year: dayjs().format('YYYY')
});

const tableData = ref([]);
const tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
function indexSort(n) {
  let sort = tablePage.total - ((tablePage.page - 1) * tablePage.per_page + n);
  if (sort > 0) {
    return sort;
  }
}
const search = () => {
  getList();
};
let status_arr = [{ "id": 0, "name": "未上传" }, { "id": 1, "name": "已上传" }]
let seachOpen = ref(false);

const resetSearch = async () => {
  tableSearch.school_class = '';
  tableSearch.name = '';
  tableSearch.free_type = '';
  tableSearch.gas_status = '';
  tablePage.page = 1;
  getList();
};
const handleSizeChange = () => {
  getList();
};
const handleCurrentChange = () => {
  getList();
};
const add = () => {
  router.push(route.path + "?option=3");
};
const see = (row) => {
  router.push(route.path + `?detail_id=${row.id}`);
};
const edit = (row) => {
  router.push(route.path + `?option=4&id=${row.id}`);
};
const torecord = (row) => {
  router.push(route.path + `?option=2&id=${row.id}`);
};
const del = (row) => {
  ElMessageBox.confirm(`确认删除该条数据吗？`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  }).then(async () => {
    const res = await api({
      method: "post",
      url: apis.del,
      data: {
        id: row.id,
      },
    });
    if (res) {
      ElMessage.success("删除成功");
      getList();
    }
  });
};
const push = (row) => {
  ElMessageBox.confirm(`确认上传该条数据吗？`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  }).then(async () => {
    const res = await api({
      method: "post",
      url: apis.change,
      data: {
        id: row.id,
      },
    });
    if (res) {
      ElMessage.success("上传成功");
      getList();
    }
  });
};

let tableInfo = ref('');
const getList = async () => {
  try {
    pageLoading.value = true;
    const { total, ...restTablePage } = tablePage;
    let url = 'houselist';
    if (baseUrl == '/api/back/govergasequipment') {
      url = 'schoolsystemlist'
    }
    if (baseUrl == '/api/back/goverproof') {
      url = 'schoolsystemlist'
    }
    const res = await api({
      url: baseUrl + '/' + url,
      params: {
        ...tableSearch,
        ...restTablePage,
        area: cityCode.value,
        school_id: route.query.school_id
      },
    });
    if (res) {
      tableData.value = res.data || [];
      tablePage.total = res.total || 0;
    }
    pageLoading.value = false;
  } catch (err) {
    pageLoading.value = false;
    console.error(err);
  }
};
onMounted(() => {
});
let detailId = ref('')
let cityCode = ref('')
watch(
  () => route,
  (newVal) => {
    detailId.value = newVal.query.detail_id || "";
    cityCode.value = newVal.query.code || "";
  },
  { immediate: true, deep: true }
);
let baseUrl = ''
let baseName = ref('');
let pageType = ref(1);
watch(
  () => route,
  (newVal) => {
    if (route.path.indexOf('/manage/house') != - 1) {
      baseUrl = '/api/back/goverhouse';
      baseName.value = '机构房屋建设'
      getList();
      pageType.value = 1;
    } else if (route.path.indexOf('/manage/gas') != - 1) {
      baseUrl = '/api/back/govergasequipment'
      baseName.value = '机构燃气安全'
      getList();
      pageType.value = 2;
    } else if (route.path.indexOf('/manage/proof') != - 1) {
      baseUrl = '/api/back/goverproof'
      baseName.value = '机构防暴设备设施'
      getList();
      pageType.value = 3;
    }
  },
  { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped>
.excellent {
  color: #70b603;
}

.good {
  color: #02a7f0;
}

.qualified {
  color: #f59a23;
}

.unqualified {
  color: #d9001b;
}

.unrelease {
  color: #b8741a;
}

.released {
  color: #70b603;
}
</style>
