<template>
    <div class="page tabs-page" v-loading="pageLoading">
        <el-scrollbar class="page-table" v-if="detailData == '' && submitData == ''">
            <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
                <el-form inline>
                    <div class="left">
                        <el-row :gutter="20">
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="区县：">
                                    <el-select v-model="tableSearch.area" placeholder="请选择">
                                        <el-option label="江阳区" :value="510502"></el-option>
                                        <el-option label="龙马潭区" :value="510504"></el-option>
                                        <el-option label="纳溪区" :value="510503"></el-option>
                                        <el-option label="泸县" :value="510521"></el-option>
                                        <el-option label="合江县" :value="510522"></el-option>
                                        <el-option label="叙永县" :value="510524"></el-option>
                                        <el-option label="古蔺县" :value="510525"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="机构名称：">
                                    <el-input v-model="tableSearch.title" placeholder="请填写" />
                                </el-form-item>
                            </el-col>
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="机构类型：">
                                    <el-select v-model="tableSearch.school_class" placeholder="请选择">
                                        <el-option label="公办" :value="1"></el-option>
                                        <el-option label="公办民营" :value="2"></el-option>
                                        <el-option label="民办" :value="3"></el-option>
                                        <el-option label="民办公助" :value="4"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="机构性质：">
                                    <el-select v-model="tableSearch.free_type" placeholder="请选择">
                                        <el-option label="非盈利性" :value="1"></el-option>
                                        <el-option label="盈利性" :value="2"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :sm="8" :md="8" :lg="8">
                                <el-form-item label="上传状态：">
                                    <el-select v-model="tableSearch.status" placeholder="请选择">
                                        <el-option v-for="(item, index) in status_arr" :key="index" :label="item.name"
                                            :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="right">
                        <el-form-item>
                            <el-button type="primary" @click="search">查询</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                            <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
                            <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <div class="table-title flex-bt" style="height: 24px;">
            </div>
            <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
                <el-table-column type="index" min-width="60" fixed="left" label="序号"
                    :index="indexSort"></el-table-column>
                <el-table-column prop="start_date" min-width="120" label="开始时间"></el-table-column>
                <el-table-column prop="end_date" min-width="120" label="结束时间"></el-table-column>
                <el-table-column prop="title" min-width="220" label="通知标题"></el-table-column>
                <el-table-column prop="name" min-width="220" label="机构名称"></el-table-column>
                <el-table-column prop="area_name" min-width="120" label="所属区县"></el-table-column>
                <el-table-column prop="street_name" min-width="120" label="所属街道"></el-table-column>
                <el-table-column prop="school_class_name" min-width="140" label="机构类型"></el-table-column>
                <el-table-column prop="free_type_name" min-width="140" label="机构性质"></el-table-column>
                <el-table-column prop="no_house_num" min-width="100" label="状态">
                    <template #default="scope">
                        {{ id2name(scope.row.check_status, status_arr) }}
                    </template>
                </el-table-column>
                <el-table-column width="200" fixed="right">
                    <template #header>
                        <span style="padding-left: 2px">操作</span>
                    </template>
                    <template #default="scope">
                        <el-button type="primary" @click="submitData = (scope.row)" link>
                            查看检查报告
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
                    :total="tablePage.total" v-model:current-page="tablePage.page"
                    v-model:page-size="tablePage.per_page" :page-sizes="[10, 20, 30, 40, 50]"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </el-scrollbar>
        <CheckDetail :detailData="detailData" :close="() => { detailData = false; }"
            v-if="detailData && submitData == ''">
        </CheckDetail>
        <CheckSubmit :submitData="submitData" :close="() => { submitData = false; }"
            v-if="detailData == '' && submitData">
        </CheckSubmit>
    </div>
</template>
<script setup>
import { Plus } from "@element-plus/icons-vue";
import { reactive, ref, onMounted, computed, watch } from "vue";
import api from "@/axios.js";
import { useRoute, useRouter } from "vue-router";
import { id2name } from "@/config/select";
import { usePageFeatures, useSelect } from "./hooks/useDevice.js";
import { useAuthStore } from "@/stores/auth-store";
import CheckSubmit from "./components/check-submit.vue";
import CheckDetail from "./components/check-detail.vue";

const schoolId = '';
const route = useRoute();
const router = useRouter();
const { apis } = usePageFeatures();
const { house_type } = useSelect();
let pageLoading = ref(false);
let tableSearch = reactive({
    title: '',
    house_status: '',
    gas_status: '',
    status: '',
    school_class: '',
    area: '',
    free_type: ''
});
let detailData = ref('');
let submitData = ref('');
let seachOpen = ref(false);

const tableData = ref([]);
const tablePage = reactive({
    total: 0,
    page: 1,
    per_page: 10,
});
function indexSort(n) {
    let sort = tablePage.total - ((tablePage.page - 1) * tablePage.per_page + n);
    if (sort > 0) {
        return sort;
    }
}
const search = () => {
    getList();
};
let build_type = ref([
    {
        id: 1,
        name: '房屋装修'
    },
    {
        id: 2,
        name: '设施设备'
    },
    {
        id: 3,
        name: '装饰材料'
    }
]);
let status_arr = [{ "id": 0, "name": "未上传" }, { "id": 1, "name": "已上传" }]
let check_status_arr = [{ "id": 2, "name": "未开始" }, { "id": 1, "name": "进行中" }, { "id": 3, "name": "已结束" }]

const resetSearch = async () => {
    tableSearch.title = '';
    tableSearch.school_class = '';
    tableSearch.free_type = '';
    tableSearch.area = '';
    tableSearch.status = '';
    tableSearch.house_status = '';
    tablePage.page = 1;
    getList();
};
const handleSizeChange = () => {
    getList();
};
const handleCurrentChange = () => {
    getList();
};
const push = (row) => {
    ElMessageBox.confirm(`确认上传该条数据吗？`, "提示", {
        type: "warning",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
    }).then(async () => {
        const res = await api({
            method: "post",
            url: apis.change,
            data: {
                id: row.id,
            },
        });
        if (res) {
            ElMessage.success("上传成功");
            getList();
        }
    });
};
const getList = async () => {
    tableSearch.house_status = tableData.status;
    tableSearch.gas_status = tableData.status;
    try {
        pageLoading.value = true;
        const { total, ...restTablePage } = tablePage;
        const res = await api({
            url: baseUrl + '/noticeschoollist',
            params: {
                ...tableSearch,
                ...restTablePage,
                school_id: schoolId,
            },
        });
        if (res) {
            tableData.value = res.data || [];
            tablePage.total = res.total || 0;
        }
        pageLoading.value = false;
    } catch (err) {
        pageLoading.value = false;
        console.error(err);
    }
};
onMounted(() => {
});
let baseUrl = ''
let baseName = ref('');
watch(
    () => route,
    (newVal) => {
        if (route.path.indexOf('/manage/house') != - 1) {
            baseUrl = '/api/back/goverhouse';
            baseName.value = '机构房屋建设'
            getList();
        } else if (route.path.indexOf('/manage/gas') != - 1) {
            baseUrl = '/api/back/govergasequipment'
            baseName.value = '机构燃气安全'
            getList();
        } else if (route.path.indexOf('/manage/proof') != - 1) {
            baseUrl = '/api/back/goverproof'
            baseName.value = '机构防暴设备设施'
            getList();
        }
    },
    { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped>
.excellent {
    color: #70b603;
}

.good {
    color: #02a7f0;
}

.qualified {
    color: #f59a23;
}

.unqualified {
    color: #d9001b;
}

.unrelease {
    color: #b8741a;
}

.released {
    color: #70b603;
}
</style>
