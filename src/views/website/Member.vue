<template>
  <div class="page" v-loading="pageLoading" :class="{ 'page-nobread': !breadStore.breadcrumbShow }">
    <el-scrollbar class="page-table" v-if="isList && !isDetail">

      <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
        <el-tab-pane label="单位会员" name="unit"></el-tab-pane>
        <el-tab-pane label="个人会员" name="person"></el-tab-pane>
        <el-tab-pane label="待审批" name="shenpi"></el-tab-pane>
        <el-tab-pane label="审批记录" name="shenpiRecord"></el-tab-pane>
      </el-tabs>
      <div class="page-search" v-show="activeName == 'unit' || activeName == 'person'"
        :class="seachOpen ? 'page-search-open' : 'page-search-close'">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="会员名称：">
                  <el-input v-model="tableSearch.name" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="单位性质：">
                  <el-select v-model="tableSearch.category_id" placeholder="请选择">
                    <!-- <el-option label="全部" value="" /> -->
                    <el-option :label="item.name" :value="item.id" :key="item.id" v-for="item in cateList" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="会员状态：">
                  <el-select v-model="tableSearch.status" placeholder="请选择">
                    <!-- <el-option label="全部" value="" /> -->
                    <el-option :label="item.name" :value="item.id" :key="item.id" v-for="item in statusList" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="page-search" v-show="activeName == 'shenpiRecord' || activeName == 'shenpi'"
        :class="seachOpen ? 'page-search-open' : 'page-search-close'">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <!-- <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="会员名称：">
                  <el-input v-model="tableSearch.name" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="会员类型：">
                  <el-select v-model="memberTypeCode" placeholder="请选择">
                    <!-- <el-option label="全部" value="" /> -->
                    <el-option label="单位会员" value="unit" />
                    <el-option label="个人会员" value="person" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8" v-show="activeName == 'shenpiRecord'">
                <el-form-item label="审批结果：">
                  <el-select v-model="pass" placeholder="请选择">
                    <!-- <el-option label="全部" value="" /> -->
                    <el-option label="通过" :value="1" />
                    <el-option label="驳回" :value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt" v-show="activeName == 'unit' || activeName == 'person'">
        <div class="left">
          <el-button type="primary" @click="add">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <Plus />
            </el-icon>添加</el-button>

        </div>
        <div class="right">

        </div>
      </div>

      <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
        <!-- <el-table-column type="selection" width="40" /> -->
        <el-table-column prop="id" fixed="left" label="序号" width="60" />
        <template v-if="activeName == 'unit' || activeName == 'person'">
          <el-table-column prop="name" fixed="left" label="会员名称" width="200">
            <template #default="scope">
              <el-popover placement="top" :width="150" trigger="hover">
                <template #reference>
                  <p class="line-1">{{ scope.row.name }}</p>
                </template>
                <div style="padding: 10px;">{{ scope.row.name }}</div>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column prop="unit" label="工作单位" v-if="activeName == 'person'">
          </el-table-column>
          <el-table-column prop="unit_address" label="单位地址" v-if="activeName == 'person'">
          </el-table-column>
          <el-table-column prop="clicks" label="单位性质" v-if="activeName == 'unit'">
            <template #default="scope">
              {{ scope.row.type_name || scope.row.cate_name }}
            </template>
          </el-table-column>
          <el-table-column prop="clicks" label="所在省市区" v-if="activeName == 'unit'" width="200">
            <template #default="scope">
              {{ scope.row.province_name + scope.row.city_name + scope.row.area_name + scope.row.link_address }}
            </template>
          </el-table-column>

          <el-table-column :prop="activeName == 'unit' ? 'link_phone' : 'phone'" label="联系电话" min-width="160" />
          <el-table-column label="会员状态" min-width="100">
            <template #default="scope">
              <p class="status">
                <span :class="{ blue: scope.row.now_status == 1, gray: scope.row.now_status != 1 }"></span>
                {{ scope.row.status_name }}
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="end_time" label="会员到期时间"
            :formatter="(v) => { return v.end_time ? v.end_time.slice(0, 10) : '-' }" min-width="160" />

          <el-table-column width="200" fixed="right">
            <template #header>
              <span style="padding-left: 2px">操作</span>
            </template>
            <template #default="scope">
              <el-button type="primary" @click="del(scope.row)" link>移除</el-button>
              <el-button type="primary" @click="see(scope.row)" link>详情</el-button>
              <el-button type="primary" @click="edit(scope.row)" link>编辑</el-button>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column label="申请手机号" min-width="110">
            <template #default="scope">
              {{ scope.row.memberType == '1' ? scope.row.link_phone : scope.row.phone }}
            </template>
          </el-table-column>
          <el-table-column prop="clicks" label="申请会员类别" min-width="110">
            <template #default="scope">
              {{ scope.row.memberType == '1' ? '单位会员' : '个人会员' }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="申请人/单位" min-width="110">
          </el-table-column>
          <el-table-column prop="created_at" label="申请提交时间" min-width="160">
            <template #default="scope">
              {{ scope.row.created_at ? timeFun(scope.row.created_at, 1) : '' }}
            </template>
          </el-table-column>
          <template v-if="activeName == 'shenpiRecord'">
            <el-table-column prop="updated_at" label="审批时间" min-width="160">
              <template #default="scope">
                {{ scope.row.updated_at || scope.row.upda_at }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="审批结果" min-width="100">
              <template #default="scope">
                {{ scope.row.status == 1 ? '已通过' : '已驳回' }}
              </template>
            </el-table-column>
            <el-table-column prop="msg" label="驳回意见" min-width="160" v-if="passResult == 2">
            </el-table-column>
          </template>

          <el-table-column width="100" label="操作" fixed="right"
            v-if="!(activeName == 'shenpiRecord' && passResult == 1)">
            <template #default="scope">
              <el-button type="primary" @click="see(scope.row, 'shenpi')" link
                v-show="activeName == 'shenpi'">立即审批</el-button>
              <el-button type="primary" @click="see(scope.row, 'shenpi')" link
                v-show="scope.row.status == 2">查看</el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div class="pagination">
        <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
          :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-scrollbar>
    <el-scrollbar ref="scrollFormlEl" class="page-form back-page" v-show="!isList">
      <el-page-header :icon="ArrowLeft" title="返回" @back="isList = true; breadStore.breadcrumbShow = true;">
        <template #content>
          <p class="b20 mb20 mt20">
            {{ (activeName == 'unit' || formData.id) ? '单位' : '个人' }}会员{{ (formData.id || formDataP.id) ? '编辑' : '添加' }}
          </p>
        </template>
      </el-page-header>
      <div class="edit margin-auto" style="padding-bottom: 100px;" v-if="activeName == 'unit' || formData.id">
        <el-form :model="formData" label-width="auto" :rules="formDataRules" ref="formEl" label-position="left"
          class="edit-form">
          <h3>基础信息</h3>
          <el-form-item label="单位姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="单位性质" prop="type">
            <el-select v-model="formData.type" placeholder="请选择">
              <el-option :label="item.name" :value="item.id" v-for="item in cateList" :key="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="所在市区" prop="province">
            <div class="flex flex-bt">
              <el-select v-model="formData.province" placeholder="请选择省" @change="changeAdd(1)">
                <el-option :label="item.text" :value="item.code * 1" v-for="item in provinceList" />
              </el-select>
              <el-select v-model="formData.city" placeholder="请选择市" @change="changeAdd(2)" style="margin: 0 10px">
                <el-option :label="item.text" :value="item.code * 1" v-for="item in cityList" />
              </el-select>
              <el-select v-model="formData.area" placeholder="请选择区县">
                <el-option :label="item.text" :value="item.code * 1" v-for="item in areaList" />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="联系人姓名" prop="link_name">
            <el-input v-model="formData.link_name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="联系人职务" prop="job">
            <el-input v-model="formData.job" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="联系人电子邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="联系人移动电话" prop="link_phone">
            <el-input v-model="formData.link_phone" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="通讯地址" prop="link_address">
            <el-input v-model="formData.link_address" placeholder="请输入" />
          </el-form-item>
          <p style="width:100%"></p>
          <el-form-item label="单位介绍" prop="mark">
            <el-input v-model="formData.mark" :rows="3" maxlength="200" show-word-limit type="textarea"
              placeholder="请输入内容限200字" />
          </el-form-item>
          <h3>法人信息</h3>
          <el-form-item label="法定代表人" prop="corporation">
            <el-input v-model="formData.corporation" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="代表人职务" prop="corporation_job">
            <el-input v-model="formData.corporation_job" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="代表人移动电话" prop="corporation_phone">
            <el-input v-model="formData.corporation_phone" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="开票名称" prop="invoice_name">
            <el-input v-model="formData.invoice_name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="纳税人识别号" prop="invoice_number">
            <el-input v-model="formData.invoice_number" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="接收票据邮箱" prop="invoice_email">
            <el-input v-model="formData.invoice_email" placeholder="请输入" />
          </el-form-item>
          <h3>资料</h3>
          <el-form-item label="早教托育协会单位会员申请表" prop="application">
            <div class="form-upload">
              <p v-if="formData.application">{{ formData.application.split('/').pop() }}<a tartget="_blank"
                  :href="formData.application">下载</a></p>
              <el-upload accept=".pdf" :auto-upload="false" ref="uploadRef" class="avatar-uploader" :limit="1"
                :on-change="(file) => imageUpload(file, 'application')"
                :on-exceed="(file) => imageUpload({ raw: file[0] }, 'application')" :show-file-list="false">
                <img src="@/assets/images/downLoad.png" />
                <p style="line-height: 2;font-size: 13px;">上传文件</p>
              </el-upload>
              <span>支持扩展名：.pdf，且仅支持一个文件</span>
            </div>
          </el-form-item>
          <el-form-item label="单位法定代表人备案表" prop="record">
            <div class="form-upload">
              <p v-if="formData.record">{{ formData.record.split('/').pop() }}<a tartget="_blank"
                  :href="formData.record">下载</a></p>
              <el-upload accept=".pdf" :auto-upload="false" ref="uploadRef" class="avatar-uploader" :limit="1"
                :on-change="(file) => imageUpload(file, 'record')"
                :on-exceed="(file) => imageUpload({ raw: file[0] }, 'record')" :show-file-list="false">
                <img src="@/assets/images/downLoad.png" />
                <p style="line-height: 2;font-size: 13px;">上传文件</p>
              </el-upload>
              <span>支持扩展名：.pdf，且仅支持一个文件</span>
            </div>
          </el-form-item>
          <el-form-item label="单位法人登记证书或统一社会信用代码证复印">
            <div class="form-upload">
              <p v-if="formData.code">{{ formData.code.split('/').pop() }}<a tartget="_blank"
                  :href="formData.code">下载</a>
              </p>
              <el-upload accept=".pdf" :auto-upload="false" ref="uploadRef" class="avatar-uploader" :limit="1"
                :on-change="(file) => imageUpload(file, 'code')"
                :on-exceed="(file) => imageUpload({ raw: file[0] }, 'code')" :show-file-list="false">
                <img src="@/assets/images/downLoad.png" />
                <p style="line-height: 2;font-size: 13px;">上传文件</p>
              </el-upload>
              <span>支持扩展名：.pdf，且仅支持一个文件</span>
            </div>
          </el-form-item>
          <template v-if="!formData.id">
            <h3>到期时间</h3>
            <el-form-item label="会员到期时间" prop="end_time">
              <el-date-picker :disabled="formData.id !== ''" :disabled-date="disabledDate" v-model="formData.end_time"
                type="date" value-format="YYYY-MM-DD" placeholder="会员到期时间" />
            </el-form-item>
          </template>

        </el-form>
        <div class="btn">
          <el-button type="primary" @click="submit(formEl, 'unit')">保存</el-button>
        </div>
      </div>
      <div class="edit margin-auto" style="padding-bottom: 100px;" v-if="activeName == 'person' || formDataP.id">
        <el-form :model="formDataP" :rules="formDataRulesP" label-width="auto" ref="formElP" label-position="left"
          class="edit-form">
          <h3>基础信息</h3>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formDataP.name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            <el-select v-model="formDataP.gender" placeholder="请选择">
              <el-option :value="1" label="男" />
              <el-option :value="2" label="女" />
            </el-select>
          </el-form-item>
          <el-form-item label="民族" prop="nationality">
            <el-input v-model="formDataP.nationality" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="身份证" prop="card">
            <el-input v-model="formDataP.card" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="籍贯" prop="place">
            <el-input v-model="formDataP.place" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="移动电话" prop="phone">
            <el-input v-model="formDataP.phone" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="电子邮箱" prop="email">
            <el-input v-model="formDataP.email" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="政治面貌" prop="political">
            <el-input v-model="formDataP.political" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="毕业院校" prop="school_name">
            <el-input v-model="formDataP.school_name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="学科" prop="subject">
            <el-input v-model="formDataP.subject" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="专业" prop="major">
            <el-input v-model="formDataP.major" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="最高学历" prop="qualification">
            <el-select v-model="formDataP.qualification" placeholder="请选择">
              <el-option :label="item.text" :value="item.id * 1" v-for="item in highEdu" />
            </el-select>
          </el-form-item>
          <h3>单位信息</h3>
          <el-form-item label="工作单位名称" prop="unit">
            <el-input v-model="formDataP.unit" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="单位地址" prop="unit_address">
            <el-input v-model="formDataP.unit_address" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="单位性质" prop="cate_id">
            <el-select v-model="formDataP.cate_id" placeholder="请选择">
              <el-option :label="item.name" :value="item.id" v-for="item in cateList" :key="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="单位联系人" prop="unit_name">
            <el-input v-model="formDataP.unit_name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="单位联系电话" prop="unit_mobile">
            <el-input v-model="formDataP.unit_mobile" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="职务职称" prop="job">
            <el-input v-model="formDataP.job" placeholder="请输入" />
          </el-form-item>
          <h3>资料/证明材料</h3>
          <el-form-item label="职称证明" prop="job_url">
            <div class="form-upload">
              <p v-if="formDataP.job_url">{{ formDataP.job_url.split('/').pop() }}<a tartget="_blank"
                  :href="formDataP.job_url">下载</a></p>
              <el-upload accept=".pdf" :auto-upload="false" ref="uploadRef" class="avatar-uploader" :limit="1"
                :on-change="(file) => imageUpload(file, 'job_url', 2)"
                :on-exceed="(file) => imageUpload({ raw: file[0] }, 'job_url', 2)" :show-file-list="false">
                <img src="@/assets/images/downLoad.png" />
                <p style="line-height: 2;font-size: 13px;">上传文件</p>
              </el-upload>
              <span>支持扩展名：.pdf，且仅支持一个文件</span>
            </div>
          </el-form-item>
          <el-form-item label="奖励或荣誉称号扫描件" prop="honor_url">
            <div class="form-upload">
              <p v-if="formDataP.honor_url">{{ formDataP.honor_url.split('/').pop() }}<a tartget="_blank"
                  :href="formDataP.honor_url">下载</a></p>
              <el-upload accept=".pdf" :auto-upload="false" ref="uploadRef" class="avatar-uploader" :limit="1"
                :on-change="(file) => imageUpload(file, 'honor_url', 2)"
                :on-exceed="(file) => imageUpload({ raw: file[0] }, 'honor_url', 2)" :show-file-list="false">
                <img src="@/assets/images/downLoad.png" />
                <p style="line-height: 2;font-size: 13px;">上传文件</p>
              </el-upload>
              <span>支持扩展名：.pdf，且仅支持一个文件</span>
            </div>
          </el-form-item>
          <el-form-item label="会员申请表" prop="application">
            <div class="form-upload">
              <p v-if="formDataP.application">{{ formDataP.application.split('/').pop() }}<a tartget="_blank"
                  :href="formDataP.application">下载</a></p>
              <el-upload accept=".pdf" :auto-upload="false" ref="uploadRef" class="avatar-uploader" :limit="1"
                :on-change="(file) => imageUpload(file, 'application', 2)"
                :on-exceed="(file) => imageUpload({ raw: file[0] }, 'application', 2)" :show-file-list="false">
                <img src="@/assets/images/downLoad.png" />
                <p style="line-height: 2;font-size: 13px;">上传文件</p>
              </el-upload>
              <span>支持扩展名：.pdf，且仅支持一个文件</span>
            </div>
          </el-form-item>
          <template v-if="!formDataP.id">
            <h3>到期时间</h3>
            <el-form-item label="会员到期时间" prop="end_time">
              <el-date-picker :disabled="formDataP.id !== ''" :disabled-date="disabledDate" v-model="formDataP.end_time"
                type="date" value-format="YYYY-MM-DD" placeholder="会员到期时间" />
            </el-form-item>
          </template>

        </el-form>
        <div class="btn">
          <el-button type="primary" @click="submit(formElP, 'person')">保存</el-button>
        </div>
      </div>
    </el-scrollbar>
    <el-scrollbar ref="scrollFormlEl1" class="page-form back-page" v-if="isDetail">
      <el-dialog class="websit-banner-dialog" v-model="dialogShow"
        :title="dialogType == 'date' || dialogType == 'pass' ? '会员到期时间' : '驳回意见'" width="532">
        <el-date-picker v-if="dialogType == 'date' || dialogType == 'pass'" v-model="matureDate" type="date"
          :disabled-date="disabledDate" value-format="YYYY-MM-DD" placeholder="会员到期时间" :size="size" />
        <el-input v-if="dialogType == 'noPass'" v-model="remark" style="width: 100%" :rows="2" maxlength="200"
          show-word-limit type="textarea" placeholder="请输入内容限200字" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogShow = false">取消</el-button>
            <el-button type="primary" @click="dialogConfirm(info)">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
      <el-page-header :icon="ArrowLeft" title="返回" @back="isDetail = false; breadStore.breadcrumbShow = true;">
        <template #content>
          <p class="b20 mb20 mt20">
            {{ '会员详情' }}
          </p>
        </template>
      </el-page-header>
      <div class="detail margin-auto" v-if="memberType == '单位会员'">
        <div class="edit-box flex margin-auto">
          <div class="intro">
            <p class='name'><span>{{ info.name.slice(0, 1) }}</span>{{ info.name }}</p>
            <ul>
              <li>
                <p><span>会员类别：</span>{{ memberType }}</p>
                <p><span>联系人姓名：</span>{{ info.link_name }}</p>
              </li>
              <li>
                <p><span>单位性质：</span>{{ info.type_name || info.cate_name }}</p>
                <p><span>联系人移动电话：</span>{{ info.link_phone }}</p>
              </li>
              <li>
                <p><span>所在市区：</span>{{ info.city_name + info.area_name + info.link_address }}</p>
              </li>
            </ul>
          </div>
          <div class="introBtn">
            <div class="btn" v-if="info.status != 2">
              <template v-if="info.now_status != 0">
                <el-button type="" @click="del(info)">移除</el-button>
                <el-button type="" @click="finish(info)" v-if="info.now_status == 1">终止</el-button>
                <el-button type="" @click="mature('date')">存续</el-button>
                <el-button type="primary" @click="edit(info)">编辑</el-button>
              </template>
              <template v-if="info.now_status === 0">
                <el-button type="" @click="mature('noPass')">驳回</el-button>
                <el-button type="primary" @click="mature('pass')">通过</el-button>
              </template>
            </div>
            <div class="num">
              <p v-if="info.now_status != 0 && info.status != 2">会员到期时间<span>{{ info.end_time ? info.end_time.slice(0,
                10)
                :
                '-' }}</span></p>
              <p v-if="info.status != 2">会员状态<span>{{ info.status_name || '待审批' }}</span></p>
              <p v-if="info.status == 2">会员状态<span>已驳回</span></p>
              <p>会员类别<span>{{ memberType }}</span></p>
            </div>
          </div>
        </div>
        <div class="info">
          <el-descriptions title="基础信息" class="infoItem" :column="3">
            <el-descriptions-item label="单位名称：">{{ info.name }}</el-descriptions-item>
            <el-descriptions-item label="单位性质：">{{ info.type_name || info.cate_name }}</el-descriptions-item>
            <el-descriptions-item label="所在市区：">{{ info.province_name + info.city_name + info.area_name
            }}</el-descriptions-item>
            <el-descriptions-item label="联系人姓名：">
              {{ info.link_name }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人职务：">
              {{ info.job }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人电子邮箱：">
              {{ info.email }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人移动电话：">
              {{ info.link_phone }}
            </el-descriptions-item>
            <el-descriptions-item label="通讯地址：" :span="2">
              {{ info.link_address }}
            </el-descriptions-item>
            <el-descriptions-item label="单位介绍：" :span="3">
              {{ info.mark }}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions title="法人信息" class="infoItem">
            <el-descriptions-item label="法定代表人：">{{ info.corporation }}</el-descriptions-item>
            <el-descriptions-item label="代表人职务：">{{ info.corporation_job }}</el-descriptions-item>
            <el-descriptions-item label="代表人移动电话：">{{ info.corporation_phone }}</el-descriptions-item>
            <el-descriptions-item label="开票名称：">{{ info.invoice_name }}</el-descriptions-item>
            <el-descriptions-item label="纳税人识别号：">{{ info.invoice_number }}</el-descriptions-item>
            <el-descriptions-item label="接收票据邮箱：">{{ info.invoice_email }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions title="资料/证明材料" class="infoItem" column="1">
            <el-descriptions-item label="南省早教托育协会单位会员申请表：">
              <p class="pdf" v-if="info.application">{{ info.application.split('/').pop() }}<span
                  @click="pdfView(info.application)">下载</span></p>
            </el-descriptions-item>
            <el-descriptions-item label="单位法定代表人备案表：">
              <p class="pdf" v-if="info.record">{{ info.record.split('/').pop() }}<span
                  @click="pdfView(info.record)">下载</span></p>
            </el-descriptions-item>
            <el-descriptions-item label="单位法人登记证书或统一社会信用代码证复印件：">
              <p class="pdf" v-if="info.code">{{ info.code.split('/').pop() }}<span
                  @click="pdfView(info.code)">下载</span>
              </p>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <template v-if="info.status == 1">
          <h3>会员关系变更记录</h3>
          <el-table :scrollbar-always-on="true" ref="table" :data="changeList">
            <el-table-column prop="type_name" fixed="left" label="变更类型" width="200">
            </el-table-column>
            <el-table-column prop="uid_name" label="操作人">
              <template #default="scope">
                {{ scope.row.uid_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="变更时间" prop="crea_time">
              <template #default="scope">
                {{ scope.row.crea_time || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status_name" label="结果" min-width="100">
            </el-table-column>
            <el-table-column prop="end_time" label="会员到期时间" min-width="160">
              <template #default="scope">
                {{ scope.row.end_time || '-' }}
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination :page-size="changePage.per_page" background layout="prev, pager, next , sizes, total"
              :total="changePage.total" v-model:current-page="changePage.page" v-model:page-size="changePage.per_page"
              :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange2"
              @current-change="handleCurrentChange2" />
          </div>
        </template>
      </div>
      <div class="detail margin-auto" v-if="memberType == '个人会员'">
        <div class="edit-box flex margin-auto">
          <div class="intro">
            <p class='name'><span>{{ info.name.slice(0, 1) }}</span>{{ info.name }}</p>
            <ul>
              <li>
                <p><span>性别：</span>{{ info.gender ? info.gender == 1 ? '男' : '女' : '-' }}</p>
                <p><span>会员类别：</span>{{ memberType }}</p>
              </li>
              <li>
                <p><span>工作单位：</span>{{ info.unit }}</p>
                <p><span>个人移动电话：</span>{{ info.phone }}</p>
              </li>
              <li>
                <p><span>单位地址：</span>{{ info.unit_address }}</p>
              </li>
            </ul>
          </div>
          <div class="introBtn">
            <div class="btn" v-if="info.status != 2">
              <template v-if="info.now_status != 0">
                <el-button type="" @click="del(info)">移除</el-button>
                <el-button type="" @click="finish(info)" v-if="info.now_status == 1">终止</el-button>
                <el-button type="" @click="mature('date')">存续</el-button>
                <el-button type="primary" @click="edit(info)">编辑</el-button>
              </template>
              <template v-if="info.now_status === 0">
                <el-button type="" @click="mature('noPass')">驳回</el-button>
                <el-button type="primary" @click="mature('pass')">通过</el-button>
              </template>

            </div>
            <div class="num">
              <p v-if="info.now_status != 0 && info.status != 2">会员到期时间<span>{{ info.end_time ? info.end_time.slice(0,
                10)
                :
                '-' }}</span></p>
              <p v-if="info.status != 2">会员状态<span>{{ info.status_name || '待审批' }}</span></p>
              <p v-if="info.status == 2">会员状态<span>已驳回</span></p>
              <p>会员类别<span>{{ memberType }}</span></p>
            </div>
          </div>
        </div>
        <div class="info">
          <el-descriptions title="基础信息" class="infoItem" :column="3">
            <el-descriptions-item label="姓名：">{{ info.name }}</el-descriptions-item>
            <el-descriptions-item label="民族：">{{ info.nationality }}</el-descriptions-item>
            <el-descriptions-item label="身份证：">{{ info.card }}</el-descriptions-item>
            <el-descriptions-item label="籍贯：">
              {{ info.place }}
            </el-descriptions-item>
            <el-descriptions-item label="移动电话：">
              {{ info.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="电子邮箱：">
              {{ info.email }}
            </el-descriptions-item>
            <el-descriptions-item label="政治面貌：">
              {{ info.political }}
            </el-descriptions-item>
            <el-descriptions-item label="毕业院校：">
              {{ info.school_name }}
            </el-descriptions-item>
            <el-descriptions-item label="最高学历：" :span="2">
              {{info.qualification ? highEdu.find(item => item.id == info.qualification).text : ''}}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions title="单位信息" class="infoItem">
            <el-descriptions-item label="工作单位名称：">{{ info.unit }}</el-descriptions-item>
            <el-descriptions-item label="单位地址：">{{ info.unit_address }}</el-descriptions-item>
            <el-descriptions-item label="单位性质：">{{ info.cate_name || info.type_name }}</el-descriptions-item>
            <el-descriptions-item label="单位联系人：">{{ info.unit_name }}</el-descriptions-item>
            <el-descriptions-item label="单位联系电话：">{{ info.unit_mobile }}</el-descriptions-item>
            <el-descriptions-item label="职务职称：">{{ info.job }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions title="资料/证明材料" class="infoItem" column="1">
            <el-descriptions-item label="职称证明：">
              <p class="pdf" v-if="info.job_url">{{ info.job_url.split('/').pop() }}<span
                  @click="pdfView(info.job_url)">下载</span></p>
            </el-descriptions-item>
            <el-descriptions-item label="奖励或荣誉称号扫描件：">
              <p class="pdf" v-if="info.honor_url">{{ info.honor_url.split('/').pop() }}<span
                  @click="pdfView(info.honor_url)">下载</span></p>
            </el-descriptions-item>
            <el-descriptions-item label="会员申请表：">
              <p class="pdf" v-if="info.application">{{ info.application.split('/').pop() }}<span
                  @click="pdfView(info.application)">下载</span></p>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <template v-if="info.status == 1">
          <h3>会员关系变更记录</h3>
          <el-table :scrollbar-always-on="true" ref="table" :data="changeList">
            <el-table-column prop="type_name" fixed="left" label="变更类型" width="200">
            </el-table-column>
            <el-table-column prop="uid_name" label="操作人">
              <template #default="scope">
                {{ scope.row.uid_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="变更时间" prop="crea_time">
              <template #default="scope">
                {{ scope.row.crea_time || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status_name" label="结果" min-width="100">
            </el-table-column>
            <el-table-column prop="end_time" label="会员到期时间" min-width="160">
              <template #default="scope">
                {{ scope.row.end_time || '-' }}
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination :page-size="changePage.per_page" background layout="prev, pager, next , sizes, total"
              :total="changePage.total" v-model:current-page="changePage.page" v-model:page-size="changePage.per_page"
              :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange2"
              @current-change="handleCurrentChange2" />
          </div>
        </template>

      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { dayjs, ElMessage } from 'element-plus'
import {
  Search,
  Plus,
  Headset,
  ArrowLeft,
  RefreshRight,
  CircleCloseFilled,
} from "@element-plus/icons-vue";
import { watch, onMounted, reactive, ref, nextTick } from "vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
import { useRoute } from "vue-router";
import { str2arr, id2name, port_ids } from "@/config/select";
import { upload } from "@/assets/utils/obs.js";
import { useBreadStore } from '@/stores/bread-store';
import Editor from '@/assets/utils/@tinymce/tinymce-vue'
import { EditorConfig } from "@/config/editor";
import { timeFun } from "@/assets/utils/index";

const tinymceScriptSrc = window.location.origin.indexOf('192.168') == -1 ? window.location.origin + '/cscpa/index.html/tinymce/tinymce.js' : '/tinymce/tinymce.js'
const route = useRoute();
const breadStore = useBreadStore();
const { organizations_id, identity_id } = useAuthStore().userInfo;
const { schoolId, isSuperAdmin } = useAuthStore();

import { useConfigStore } from "@/stores/config-store";
const { provinceList, getCityApi, getAreaApi, getStreetApi } = useConfigStore();
// 省市区
let cityList = ref([])
let areaList = ref([])
async function changeAdd(type) {
  if (type == 1) {
    cityList.value = []
    areaList.value = []
    cityList.value = await getCityApi(formData.province)
  } else if (type == 2) {
    areaList.value = []
    areaList.value = await getAreaApi(formData.city)
  }
}

let baseUrl = "";
let baseName = "会员";
let seachOpen = ref(false);
let activeName = ref('unit')
let baseUrlArr = {
  unit: '/api/back/govuntil',
  person: '/api/back/govuser',
  shenpi: '/api/back/govuser',
  shenpiRecord: '/api/back/govuser',
}
const handleClick = (val) => {
  baseUrl = baseUrlArr[val];
  if (val == 'shenpi' || val == 'shenpiRecord') {
    memberTypeCode.value = 'unit'
  } else {
    memberTypeCode.value = ''
    getSelect()
  }
  resetSearch();
}
let previewNewsShow = ref(false);
let isDetail = ref(false);
let isList = ref(true);
let pageLoading = ref(false);

let formEl = ref(null);
let scrollFormlEl = ref(null);
let scrollFormlEl1 = ref(null);
// 单位
let formData = reactive({
  id: 0,
  name: "",//单位名称
  type: "",//单位性质ID
  province: "",//省
  city: "",//市
  area: "",//区
  link_name: "",//联系人
  job: "",//联系人职务
  email: "",//邮箱
  link_phone: "",//联系人手机号
  link_address: "",//通讯地址
  mark: "",//单位介绍
  corporation: "",//法人
  corporation_job: "",//法人职务
  corporation_phone: "",//法人手机号
  invoice_name: "",//开票名称
  invoice_number: "",//识别号
  invoice_email: "",//票据接受邮箱
  application: "",//申请表连接
  record: "",//备案表连接
  code: "",//统一社会信用复印件链接
  memberType: ''
});
let formDataRules = {
  name: [
    { required: true, message: '请输入单位名称', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请输入单位性质', trigger: 'change' },
  ],
  province: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!formData.province) {
          callback(new Error("请选择省份"))
        } else if (!formData.city) {
          callback(new Error("请选择市"))
        } else if (!formData.area) {
          callback(new Error("请选择区县"))
        } else {
          callback()
        }
      },
      trigger: 'change'
    },
  ],
  link_name: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
  ],
  email: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入邮箱"))
        } else if (!checkValue(value, 'email')) {
          callback(new Error("请输入正确的邮箱"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  link_phone: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入手机号"))
        } else if (!checkValue(value, 'phone')) {
          callback(new Error("请输入正确的手机号"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  link_address: [
    { required: true, message: '请输入通讯地址', trigger: 'blur' },
  ],
  mark: [
    { required: true, message: '请输入单位介绍', trigger: 'blur' },
  ],
  corporation_job: [
    { required: true, message: '请输入法人职务', trigger: 'blur' },
  ],
  corporation: [
    { required: true, message: '请输入法人', trigger: 'blur' },
  ],
  corporation_phone: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入法人手机号"))
        } else if (!checkValue(value, 'phone')) {
          callback(new Error("请输入正确的手机号"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  invoice_name: [
    { required: true, message: '请输入开票名称', trigger: 'blur' },
  ],
  invoice_number: [
    { required: true, message: '请输入识别号', trigger: 'blur' },
  ],
  invoice_email: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入票据接受邮箱"))
        } else if (!checkValue(value, 'email')) {
          callback(new Error("请输入正确的邮箱"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  application: [
    {
      required: true,
      message: '请上传申请表',
      trigger: 'change'
    }
  ],
  record: [
    {
      required: true,
      message: '请上传备案表',
      trigger: 'change'
    }
  ],
  code: [
    {
      required: true,
      message: '请上传统一社会信用复印件',
      trigger: 'change'
    }
  ],
  end_time: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value && !formData.id) {
          callback(new Error("请选择到期时间"))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 个人
let formElP = ref(null)
let formDataP = reactive({
  id: 0,
  name: "",//姓名
  gender: '',//性别
  nationality: '',//民族
  card: '',//身份证号
  place: '',//籍贯
  phone: '',//手机号
  email: "",//邮箱
  political: '',//政治面貌
  school_name: '',//毕业院校
  subject: '',//学科
  major: '',//专业
  qualification: '',//1高中 2大专 3本科 4硕士研究生 5博士研究生
  unit: '',//单位名称
  unit_address: '',//单位地址
  cate_id: '',//单位性质ID
  job: '',//职务
  unit_name: '',//单位联系人
  unit_mobile: '',//单位电话
  job_url: '',//职称链接
  honor_url: '',//荣誉证书链接
  application: '',//申请表连接
});
let disabledDate = (date) => {
  return date.getTime() < new Date().getTime()
}
import { checkValue } from "@/assets/utils/index.js";
let formDataRulesP = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
  ],
  nationality: [
    { required: true, message: '请输入民族', trigger: 'change' },
  ],
  card: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入身份证号"))
        } else if (!checkValue(value, 'idcard')) {
          callback(new Error("请输入正确的身份证号"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  place: [
    { required: true, message: '请输入籍贯', trigger: 'blur' },
  ],
  email: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入邮箱"))
        } else if (!checkValue(value, 'email')) {
          callback(new Error("请输入正确的邮箱"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  phone: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入手机号"))
        } else if (!checkValue(value, 'phone')) {
          callback(new Error("请输入正确的手机号"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  political: [
    { required: true, message: '请输入政治面貌', trigger: 'blur' },
  ],
  school_name: [
    { required: true, message: '请输入毕业院校', trigger: 'blur' },
  ],
  subject: [
    { required: true, message: '请输入学科', trigger: 'blur' },
  ],
  major: [
    { required: true, message: '请输入专业', trigger: 'blur' },
  ],
  unit: [
    { required: true, message: '请输入单位名称', trigger: 'blur' },
  ],
  unit_address: [
    { required: true, message: '请输入单位地址', trigger: 'blur' },
  ],
  job: [
    { required: true, message: '请输入职务职称', trigger: 'blur' },
  ],
  unit_name: [
    { required: true, message: '请输入单位联系人', trigger: 'blur' },
  ],
  unit_mobile: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入联系人电话"))
        } else if (!checkValue(value, 'phone')) {
          callback(new Error("请输入正确的手机号"))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
  qualification: [
    {
      required: true,
      message: '请选择最高学历',
      trigger: 'change'
    }
  ],
  job_url: [
    {
      required: true,
      message: '请上传职称证明',
      trigger: 'change'
    }
  ],
  honor_url: [
    {
      required: true,
      message: '请上传荣誉证书',
      trigger: 'change'
    }
  ],
  application: [
    {
      required: true,
      message: '请上传申请表',
      trigger: 'change'
    }
  ],
  cate_id: [
    {
      required: true,
      message: '请选择单位性质',
      trigger: 'change'
    }
  ],
  end_time: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value && !formDataP.id) {
          callback(new Error("请输入票据接受邮箱"))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}


let highEdu = [{
  id: 1,
  text: '高中'
}, {
  id: 2,
  text: '大专'
}, {
  id: 3,
  text: '本科'
}, {
  id: 4,
  text: '硕士研究生'
}, {
  id: 5,
  text: '博士研究生'
},]
const setFormData = (data = {}) => {
  console.log(data.memberType, isList.value, isDetail.value);
  if (data.memberType == 1) {
    for (let k of Object.keys(formData)) {
      formData[k] = data[k] || ''
    }
    if (formData.province) {
      formData.province = formData.province * 1
      changeAdd(1)
    }
    if (formData.city) {
      formData.city = formData.city * 1
      changeAdd(2)
    }
    if (formData.area) {
      formData.area = formData.area * 1
    }
  } else {
    console.log(formDataP)
    for (let k of Object.keys(formDataP)) {
      formDataP[k] = data[k] || ''
    }
    if (formDataP.province) {
      formDataP.province = formDataP.province * 1
      changeAdd(1)
    }
    if (formDataP.city) {
      formDataP.city = formDataP.city * 1
      changeAdd(2)
    }
    if (formDataP.area) {
      formDataP.area = formDataP.area * 1
    }
  }
};

let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let apiLoading = ref(false);
let tableSearch = reactive({
  name: "",
  category_id: '',
  status: "",
});
let tableData = ref([]);

const search = async () => {
  tablePage.page = 1;
  list();
};
let pass = ref(1)
let passResult = ref(1)
const list = async (data) => {
  isList.value = true;
  pageLoading.value = true;
  tableData.value = [];
  let url = ''

  if (activeName.value == 'unit' || activeName.value == 'person') {
    url = `${baseUrlArr[activeName.value]}/list?category_id=${tableSearch.category_id}&name=${tableSearch.name}&status=${tableSearch.status}&per_page=${tablePage.per_page}&page=${tablePage.page}`
  } else {
    passResult.value = pass.value
    url = `/api/back/govuser/passlist?status=${activeName.value == 'shenpi' ? 0 : 1}&type=${memberTypeCode.value ? (memberTypeCode.value == 'unit' ? 1 : 2) : 0}&per_page=${tablePage.per_page}&page=${tablePage.page}&pass=${pass.value}`
  }
  await api({
    url
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
};

const add = async () => {
  console.log('add')
  setFormData();
  setTimeout(() => {
    if (activeName.value == 'unit') {
      formEl.value.clearValidate()
    } else {
      formElP.value.clearValidate()
    }
    isList.value = false;
  }, 0);
};

async function imageUpload(file, type, type2) {
  console.log(file)
  let rawFile = file.raw;
  pageLoading.value = true;
  let res = await upload(rawFile, `news/${formData.id}`);
  if (type2 == 2) {
    formDataP[type] = res
    console.log('22222', res)
  } else {
    formData[type] = res
  }
  pageLoading.value = false;
}

const resetSearch = async () => {
  tableSearch.name = "";
  tableSearch.category_id = '';
  tableSearch.status = "";
  tablePage.page = 1;

  memberTypeCode.value = (activeName.value == 'unit' || activeName.value == 'person') ? '' : 'unit';
  pass.value = '';
  list();
};

const submit = async (formEl, type) => {
  if (!formEl) return
  let valid = await formEl.validate((valid) => { })
  console.log(valid);
  if (!valid) {
    return false;
  }

  let data = type == 'person' ? { ...formDataP } : { ...formData };
  apiLoading.value = true;

  let apitype = 'add';
  if (data.id) {
    apitype = 'update'
  }

  await api({
    url: `${baseUrlArr[data.memberType == 1 ? 'unit' : 'person']}/${apitype}`,
    method: "POST",
    data
  }).then(async (res) => {
    if (res) {
      ElMessage.success(data.id ? "修改成功" : '新增成功');
      isList.value = true;
      await list();
    }
    apiLoading.value = false;
  }).catch((err) => {
    ElMessage.error("新增失败");
    apiLoading.value = false;
  });
};

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};
let memberType = ref('')
let memberTypeCode = ref('')
let info = ref({})
const see = async (data) => {
  if (!data.id) {
    return
  }
  if (data.memberType == '1') {
    memberType.value = '单位会员'
  }
  if (data.memberType == '2') {
    memberType.value = '个人会员'
  }
  console.log(data.memberType, memberType.value)
  setFormData(data)
  info.value = data
  isDetail.value = true;
  getChangeList()
};
// 会员变更
let changePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
})
const handleSizeChange2 = (val) => {
  changePage.per_page = val;
  getChangeList();
};
const handleCurrentChange2 = (val) => {
  changePage.page = val;
  getChangeList();
};
let changeList = ref([])
const getChangeList = async () => {
  let res = await api({
    method: 'get',
    url: `${baseUrl}/getchangelist?id=${info.value.id}&per_page=${tablePage.per_page}&page=${tablePage.page}`,
  });
  if (res) {
    changeList.value = res.data
    changePage.total = res.total
  }
}

const finish = async (data) => {
  if (!data.id) {
    return
  }
  let res = await api({
    method: 'post',
    url: `${baseUrl}/maturestop`,
    data: {
      id: data.id
    }
  });
  if (res) {
    ElMessage.success("终止成功");
    breadStore.breadcrumbShow = true;
    isList.value = true
    isDetail.value = false
    list()
  }
}

const edit = async (data) => {
  console.log(data);
  if (!data.id) {
    return
  }
  isDetail.value = false;
  isList.value = false;

  setFormData(data)
};

let dialogShow = ref(false)
let dialogType = ref('')
let matureDate = ref('')
let remark = ref('')
const mature = (t) => {
  dialogType.value = t
  matureDate.value = ''
  remark.value = ''
  dialogShow.value = true
}

const dialogConfirm = async (data) => {
  if (!matureDate.value && (dialogType.value == 'date' || dialogType.value == 'pass')) {
    ElMessage.error('请选择到期日期')
    return
  }
  if (!remark.value && dialogType.value == 'noPass') {
    ElMessage.error('请输入驳回意见')
    return
  }
  let myUrl = memberType.value == '单位会员' ? '/api/back/govuntil' : '/api/back/govuser'
  if (dialogType.value == 'date') {

    let res = await api({
      method: 'post',
      url: `${myUrl}/mature`,
      data: {
        id: data.id,
        end_time: matureDate.value + ' 23:59:59'
      }
    });
    if (res) {
      dialogShow.value = false
      ElMessage.success("续存成功");
      breadStore.breadcrumbShow = true;
      isList.value = true
      isDetail.value = false
      list();
    }
  } else if (dialogType.value == 'pass') {

    let res = await api({
      method: 'post',
      url: `${myUrl}/adopt`,
      data: {
        id: data.id,
        end_time: matureDate.value + ' 23:59:59'
      }
    });
    if (res) {
      dialogShow.value = false
      ElMessage.success("审批通过成功");
      breadStore.breadcrumbShow = true;
      isList.value = true
      isDetail.value = false
      list();
    }
  } else if (dialogType.value == 'noPass') {

    let res = await api({
      method: 'post',
      url: `${myUrl}/reject`,
      data: {
        id: data.id,
        remark: remark.value
      }
    });
    if (res) {
      dialogShow.value = false
      ElMessage.success("驳回成功");
      breadStore.breadcrumbShow = true;
      isList.value = true
      isDetail.value = false
      list();
    }
  }

};
const alertIcon = `<i style="margin: 2px 10px;font-size: 22px; color: #f0af41;" class="el-icon el-alert__icon is-big"><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
      <path fill="currentColor"
        d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z">
    </path>
  </svg></i>`

const del = async (data) => {
  let title = `<div class="flex">${alertIcon}<div><strong style="font-size: 16px;margin-bottom:10px; display:block">确认移除该${baseName}吗</strong></div></div>`;
  ElMessageBox.confirm(title, "", {
    dangerouslyUseHTMLString: true,
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      console.log(`output->data`, data);
      let ids = [data.id];
      let res = await api({
        method: 'post',
        url: `${baseUrlArr[data.memberType == 1 ? 'unit' : 'person']}/delete`,
        data: {
          id: ids,
        },
      });
      if (res) {
        ElMessage.success("移除成功");
        isList.value = true
        isDetail.value = false
        list();
      }
    })
    .catch(() => { });
};
let cateList = ref([])
let statusList = ref([])
const getSelect = async () => {
  statusList.value = []
  let res = await api({
    method: 'get',
    url: `${baseUrl}/cate`,
  });
  if (res) {
    cateList.value = res
  }
  let res2 = await api({
    method: 'get',
    url: `${baseUrl}/statulist`,
  });
  if (res2) {
    for (let [k, v] of Object.entries(res2)) {
      statusList.value.push({
        name: v,
        id: k
      })
    }

  }
}
watch(
  () => route,
  (newVal) => {
    baseUrl = baseUrlArr[activeName.value];
    list();
    getSelect()
  },
  { immediate: true, deep: true }
);

const pdfView = (url) => {
  window.open(url)
}
</script>
<style lang="scss">
:deep(.mce-content-body img) {
  max-width: 100% !important;
  height: auto !important;
}

.customClass {
  z-index: 1500;
}

.preview-dialog {
  padding: 0 !important;
  border-radius: 0 !important;

  .el-dialog__header {
    display: none !important;
  }

  .el-dialog__body {
    padding: 0 0 20px 0 !important;
    border-radius: 0 !important;
    margin: 0 !important;
    overflow: auto;

  }

  .title {
    width: 375px;
    height: 44px;
    background: #FFFFFF;
    text-align: center;
    line-height: 44px;
  }

  .detail-des {
    font-size: 15px;
    color: #787C8D;
    line-height: 22px;
    width: 345px;
    margin: 0 15px 20px;
  }

  .detail-content {
    font-size: 16px;
    color: #262937;
    line-height: 28px;
    margin-bottom: 20px;
    width: 345px;
    margin: 0 auto;

    img,
    video {
      max-width: 100% !important;
      width: 100% !important;
      height: auto !important;
      display: block;
    }

    video {
      margin: 8px auto;
    }
  }
}

.news-message {
  width: 306px;
  background: #FFFFFF;
  border-radius: 10px;
  border: 2px solid #eee;
  margin-left: 20px;
  padding: 24px;
  margin-bottom: 20px !important;

  .title .el-textarea__inner {
    border: none !important;
    box-shadow: none !important;
    font-size: 20px;
    font-weight: 500;
    padding: 0;
  }

  .description .el-textarea__inner {
    font-size: 14px !important;
    box-shadow: none !important;
    padding: 0;
  }
}
</style>
<style lang="scss" scoped>
.page-search {
  margin: 0 0 20px 0;
}

.table-title {
  height: auto;
  margin-bottom: 20px;
}

p.status {
  display: flex;
  align-items: center;

  span {
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
  }

  span.blue {
    background: #1890FF;
  }

  span.gray {
    background: rgba(0, 0, 0, 0.25);
  }
}

.pagination {
  padding-top: 20px;
}

::v-deep .websit-banner-dialog {
  .el-input__wrapper {
    display: flex;
  }
}

p.pdf {
  display: inline-flex;
  align-items: center;

  span {
    cursor: pointer;
    color: #1890FF;
    margin-left: 8px;
  }
}

.edit {
  border-top: 1px solid #F4F4F4;

  h3 {
    display: block;
    padding-top: 24px;
    padding-bottom: 8px;
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    width: 100%;
  }

  .edit-form {
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 100px;

    ::v-deep .el-form-item {
      max-width: 400px;
      width: 33%;
      flex-direction: column;
      padding-right: 20px;

      &:last-child {
        margin-bottom: 24px !important;
      }
    }

    .form-upload {
      display: block;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.65);

      >p {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;

        a {
          color: #1890FF;
          padding-left: 8px;
        }
      }

      span {
        display: block;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .btn {
    position: fixed;
    height: 56px;
    bottom: 0;
    border-top: 1px solid #E9E9E9;
    left: 256px;
    width: calc(100vw - 256px);
    display: flex;
    background-color: #fff;
    z-index: 2;
    align-items: center;
    justify-content: flex-end;
    padding-right: 32px;
  }
}

.detail {
  .edit-box {
    justify-content: space-between;
    padding-bottom: 24px;
    border-bottom: 1px solid #E8E8E8;

    .intro {
      >p {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        margin-bottom: 18px;

        span {
          width: 32px;
          height: 32px;
          background: #1890FF;
          border-radius: 16px;
          border-radius: 50%;
          text-align: center;
          line-height: 32px;
          margin-right: 12px;
          font-size: 16px;
          color: #fff;
        }
      }

      ul {
        overflow: hidden;
        padding-left: 44px;

        li {
          color: rgba(0, 0, 0, 0.65);
          line-height: 22px;
          margin-right: 18px;
          width: 230px;
          float: left;

          p {
            margin-bottom: 8px;
            display: flex;
          }

          span {
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }

    .introBtn {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .num {
        padding-top: 14px;
        display: flex;

        p {
          text-align: center;
          color: rgba(0, 0, 0, 0.45);
          margin-left: 40px;

          span {
            display: block;
            color: rgba(0, 0, 0, 0.85);
            font-size: 20px;
            padding-top: 4px;
            line-height: 28px;
          }
        }
      }
    }
  }

  .info {
    .infoItem {
      padding-top: 24px;
      padding-bottom: 32px;
      border-bottom: 1px solid #E8E8E8;
    }
  }

  h3 {
    display: block;
    padding-top: 32px;
    padding-bottom: 16px;
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
}

.websit-banner-dialog {
  // padding: 0 !important;
  border-radius: 16px !important;

  .el-dialog__header {
    padding: 16px 32px 40px !important;
  }

  .el-dialog__body {
    padding: 0 32px !important;
    border-radius: 0 !important;
    margin: 0 !important;
    overflow: auto;
  }

  .title {
    width: 375px;
    height: 44px;
    background: #FFFFFF;
    text-align: center;
    line-height: 44px;
  }

  .detail-time {
    font-size: 14px;
    color: #AEB0BC;
    line-height: 22px;
    width: 345px;
    margin: 0 15px 20px;
  }

  .detail-des {
    font-size: 15px;
    color: #787C8D;
    line-height: 22px;
    width: 345px;
    margin: 0 15px 20px;
  }

  .detail-content {
    font-size: 16px;
    color: #262937;
    line-height: 28px;
    margin-bottom: 20px;
    width: 345px;
    margin: 0 auto;

    img,
    video {
      max-width: 100% !important;
      width: 100% !important;
      height: auto !important;
      display: block;
    }

    video {
      margin: 8px auto;
    }
  }
}

.addList {
  padding: 20px 20px 10px;

  p {
    cursor: pointer;
    margin-bottom: 10px;
  }
}

.dialog-p {
  line-height: 20px;
  color: #262626;
  margin-bottom: 16px;

  span {
    color: rgba(0, 0, 0, 0.65);
  }
}

.avatar-uploader {
  ::v-deep .el-upload {
    width: 106px;
    height: 32px;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #D9D9D9;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;

    img {
      width: 14px;
      margin-right: 8px;
    }
  }
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader .avatar {
  width: 256px;
  height: 108px;
}

.el-icon.avatar-uploader-icon {
  font-size: 14px;
  font-style: normal;
  color: #8c939d;
  width: 256px;
  height: 108px;
  text-align: center;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
}

.music-uploader {
  height: 36px;
}

.music-uploader-icon {
  height: 36px;
  display: flex;
  align-items: center;
}

.remove {
  position: absolute;
  right: 6px;
  top: 6px;
  z-index: 10;
}


.news {
  padding: 24px;
}
</style>
