<template>
  <div class="page school-home gov-data-page" v-loading="pageLoading">
    <div class="gotop" v-if="gotopshow" @click="gotop(scrollEl)"><img src="@/assets/images/gotop.png" />返回顶部</div>
    <el-scrollbar ref="scrollEl" style="width: 100%;" @scroll="scroll" v-if="mapStore.level == 3">
      <div class="main-part">
        <div class="home-header">
          <div class="top">
            <div class="bread">
              <p class="b20">{{ mapStore.name }}</p>
            </div>
          </div>
          <div class="center" v-if="apiData1">
            <div class="msg">
              <div class="t14 mb16" style="line-height: 1.4">当前托育机构数<el-popover placement="top" effect="dark"
                  trigger="click">
                  <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                      <!-- <QuestionFilled /> -->
                    </el-icon></template>
                  <p class="popver-text"></p>
                </el-popover></div>
              <div class="b40 flex line-1" style="font-family: RousseauDeco;line-height: 32px;">
                {{ apiData1.school_num || '0' }}
              </div>
            </div>
            <div class="msg">
              <div class="t14 mb16" style="line-height: 1.4">新增托育机构数<el-popover placement="top" effect="dark"
                  trigger="click">
                  <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                      <!-- <QuestionFilled /> -->
                    </el-icon></template>
                  <p class="popver-text"></p>
                </el-popover></div>
              <div class="b40 flex line-1" style="font-family: RousseauDeco;line-height: 32px;">
                {{ apiData1.school_addnum || '0' }}
              </div>
            </div>
            <div class="msg">
              <div class="t14 mb16" style="line-height: 1.4">新增率<el-popover placement="top" effect="dark"
                  trigger="click">
                  <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                      <!-- <QuestionFilled /> -->
                    </el-icon></template>
                  <p class="popver-text"></p>
                </el-popover></div>
              <div class="b40 flex line-1" style="font-family: RousseauDeco;line-height: 32px;">
                {{ apiData1.add_rate || '0' }}%
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="bottom-header">
              <el-date-picker style="width: 250px !important;" @change="getTable1" value-format="YYYY-MM" v-model="Ym"
                type="month" :disabled-date="dd1" :clearable="false" placeholder="年月范围" />
            </div>
            <div class="bottom-chart flex">
              <div class="chart-item" style="flex: 0 0 calc(100% - 360px);">
                <span class="b20 mb20">各区县新增托育机构数</span>
                <div class="chart-bar" ref="barchart_a"></div>
              </div>
              <div class="chart-item" style="flex: 0 1 320px;">
                <span class="b20 mb20">各区县新增托育机构数占比<el-popover placement="top" effect="dark" trigger="click">
                    <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                        <!-- <QuestionFilled /> -->
                      </el-icon></template>
                    <p class="popver-text">
                    </p>
                  </el-popover></span>
                <div class="box-content flex" style="padding: 0 24px 0 0">
                  <div class="chart-bar" ref="piechart_a"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="potential-table">
          <div class="flex-bt" style="height: 48px;">
            <span class="b16">各区县建设情况</span>
          </div>
          <el-table :scrollbar-always-on="true" ref="table" :data="tableData1">
            <el-table-column fixed type="index" width="60" label="序号"></el-table-column>
            <el-table-column fixed prop="code_name" min-width="80" label="区县"></el-table-column>
            <el-table-column min-width="100" prop="school_num" label="现有托育机构数"></el-table-column>
            <el-table-column min-width="140" prop="school_addnum" label="新增托育机构数"></el-table-column>
            <el-table-column min-width="120" prop="add_rate"
              :formatter="(row, column, cellValue, index) => { return cellValue + '%' }" label="新增率"></el-table-column>
            <el-table-column width="100" fixed="right">
              <template #header>
                <span style="padding-left: 2px">操作</span>
              </template>
              <template #default="scope">
                <el-button type="primary" @click="mapStore.nextCity(scope.row.code, scope.row.code_name)"
                  link>查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-scrollbar>
    <el-scrollbar ref="scrollEl" style="width: 100%;" @scroll="scroll" v-if="mapStore.level == 4">
      <div class="main-part">
        <div class="home-header">
          <div class="top">
            <el-icon class="icon" size="30" @click="mapStore.backCity()" v-if="mapStore.parent_code.length">
              <Back />
            </el-icon>
            <div class="bread">
              <p class="b20">{{ mapStore.name }}</p>
            </div>
          </div>
          <div class="center" v-if="apiData1">
            <div class="msg">
              <div class="t14 mb16" style="line-height: 1.4">现有托育机构数<el-popover placement="top" effect="dark"
                  trigger="click">
                  <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                      <!-- <QuestionFilled /> -->
                    </el-icon></template>
                  <p class="popver-text">
                    0-3岁婴幼儿人数是指在特定区域内，年龄处于0岁（出生不满1年）至3岁（含3 岁）之间的婴幼儿的数量统计，旨在准确反映该区域内这一年龄段人口的规模，为相关政策制定、社会服务资源配置等提供数据支持。
                  </p>
                </el-popover></div>
              <div class="b40 flex line-1" style="font-family: RousseauDeco;line-height: 32px;">
                {{ apiData1.school_num || '0' }}
              </div>
            </div>
            <div class="msg">
              <div class="t14 mb16" style="line-height: 1.4">新增托育机构数<el-popover placement="top" effect="dark"
                  trigger="click">
                  <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                      <!-- <QuestionFilled /> -->
                    </el-icon></template>
                  <p class="popver-text">
                    指在区域范围内，实时统计系统中截至当前正在运营中的托育机构，录入的现有托位数总和。</p>
                </el-popover></div>
              <div class="b40 flex line-1" style="font-family: RousseauDeco;line-height: 32px;">
                {{ apiData1.school_addnum || '0' }}
              </div>
            </div>
            <div class="msg">
              <div class="t14 mb16" style="line-height: 1.4">新增率<el-popover placement="top" effect="dark"
                  trigger="click">
                  <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                      <!-- <QuestionFilled /> -->
                    </el-icon></template>
                  <p class="popver-text">
                    指在区域范围内，实时统计系统中截至当前正在运营中的普惠托育机构，录入的现有托位数总和。</p>
                </el-popover></div>
              <div class="b40 flex line-1" style="font-family: RousseauDeco;line-height: 32px;">
                {{ apiData1.add_rate || '0' }}%
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="bottom-header">
              <el-date-picker style="width: 250px !important;" @change="getTable2" value-format="YYYY" v-model="Y2"
                type="year" :disabled-date="dd1" :clearable="false" placeholder="请选择年份" />
            </div>
            <div class="bottom-chart flex">
              <div class="chart-item" style="flex: 0 0 calc(100% - 360px);">
                <span class="b20 mb20">新增托育机构数</span>
                <div class="chart-bar" ref="barchart_b"></div>
              </div>
              <div class="chart-item" style="flex: 0 1 320px;">
                <span class="b20 mb20">新增托育机构数分类<el-popover placement="top" effect="dark" trigger="click">
                    <template #reference><el-icon color="rgb(159.5, 206.5, 255)" size="16" class="popver-icon">
                        <!-- <QuestionFilled /> -->
                      </el-icon></template>
                    <p class="popver-text">
                    </p>
                  </el-popover></span>
                <div class="box-content flex" style="padding: 0 24px 0 0">
                  <div class="chart-bar" ref="piechart_b"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="potential-table">
          <el-tabs v-model="tableType" style="height: auto;">
            <el-tab-pane name="followTable">
              <template #label>
                <span class="flex-ct">各年新增托育机构数据</span>
              </template>
              <el-table :scrollbar-always-on="true" ref="table" :data="tableData2">
                <el-table-column fixed type="index" width="60" label="序号"></el-table-column>
                <el-table-column fixed prop="year" min-width="60" label="年份"></el-table-column>
                <el-table-column min-width="100" prop="school_num" label="现有托育机构数"></el-table-column>
                <el-table-column min-width="140" prop="school_addnum" label="新增托育机构数"></el-table-column>
                <el-table-column min-width="120" prop="add_rate"
                  :formatter="(row, column, cellValue, index) => { return cellValue + '%' }"
                  label="新增率"></el-table-column>
                <!-- <el-table-column width="100" fixed="right">
                  <template #header>
                    <span style="padding-left: 2px">操作</span>
                  </template>
                  <template #default="scope">
                    <el-button type="primary" link>查看详情</el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </el-tab-pane>
            <el-tab-pane name="sourceTable">
              <template #label>
                <span class="flex-ct">各机构托位数据</span>
              </template>
              <el-table :scrollbar-always-on="true" ref="table" :data="tableData3">
                <el-table-column type="index" width="60" fixed="left" label="序号" :index="indexSort"></el-table-column>
                <el-table-column fixed prop="name" min-width="180" label="托育机构"></el-table-column>
                <el-table-column label="所属街道" min-width="140">
                  <template #default="scope">
                    {{ scope.row.street_name || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="是否普惠" min-width="120">
                  <template #default="scope">
                    {{ scope.row.is_nature ? '是' : '否' }}
                  </template>
                </el-table-column>
                <el-table-column prop="build_children_num" label="已有托位数" min-width="120"></el-table-column>
                <!-- <el-table-column width="100" fixed="right">
                  <template #header>
                    <span style="padding-left: 2px">操作</span>
                  </template>
                  <template #default="scope">
                    <el-button type="primary" link>查看详情</el-button>
                  </template>
                </el-table-column> -->
              </el-table>
              <div class="pagination">
                <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next, total, sizes"
                  :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
                  :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange"
                  @current-change="handleCurrentChange" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import {
  ArrowLeftBold,
  Back,
  QuestionFilled
} from "@element-plus/icons-vue";
import * as echarts from "echarts";
import { nChartText } from "@/assets/utils/index";
import { watch, onMounted, onUnmounted, reactive, ref, computed, nextTick } from "vue";
import api from "@/axios.js";
import { dayjs } from 'element-plus';
import logo from "@/assets/images/login_logo.png";
import { useAuthStore } from "@/stores/auth-store";
import { useConfigStore } from "@/stores/config-store";
import { useMapStore } from "@/stores/map-store";
import { useBreadStore } from '@/stores/bread-store';
import { useRoute } from "vue-router";
import { chartOption } from '@/config/chartOption.js'

import { str2arr } from "@/config/select.js";
const route = useRoute();
let baseUrl = "/api/back/systemschooladdup";
const breadStore = useBreadStore();
let gotopshow = ref(false);
let scrollEl = ref(null);
const mapStore = useMapStore();
let pageLoading = ref(false);
let tableType = ref('followTable');
let barchart_a = ref(null);
let barchart_b = ref(null);
let piechart_a = ref(null);
let piechart_b = ref(null);

function dd1(date) { return (dayjs(date) > dayjs() || dayjs(date) < dayjs('2024-01-01')) }

let chartDom = reactive(['', '', '', '', '', ''])
let tableData1 = ref([]);
let tableData2 = ref([]);
let tableData3 = ref([]);
let apiData1 = ref('');
let Ym = ref(dayjs().format('YYYY-MM'))
let Y1 = ref(dayjs().format('YYYY'))
let Y2 = ref(dayjs().format('YYYY'))
let Y3 = ref(dayjs().format('YYYY'))
async function getApiData1() {
  let res = await api({
    url: `${baseUrl}/baseinfo`,
    params: {
      level: mapStore.level,
      code: mapStore.code,
    },
    method: "get",
  });
  if (res) {
    apiData1.value = res;
  }
}

let apiData2 = ref('');
async function getApiData2() {
  let res = await api({
    url: `${baseUrl}/areachart`,
    params: {
      level: mapStore.level,
      code: mapStore.code,
      year: Y1.value
    },
    method: "get",
  });
  if (res) {
    let month = [];
    for (const key in res.chart) {
      if (Object.prototype.hasOwnProperty.call(res.chart, key)) {
        const element = res.chart[key];
        month.push(element)
      }
    }
    let type = [];
    for (const key in res.school) {
      if (Object.prototype.hasOwnProperty.call(res.school, key)) {
        const element = res.school[key];
        if (key == 1) {
          type.push({ name: '托育机构', value: element })
        } else if (key == 7) {
          type.push({ name: '幼儿园托班', value: element })
        } else if (key == 8) {
          type.push({ name: '社区托育园（点）', value: element })
        } else if (key == 9) {
          type.push({ name: '家庭托育点', value: element })
        } else if (key == 10) {
          type.push({ name: '其他', value: element })
        }
      }
    }
    res.chart = month;
    res.school = type;
    apiData2.value = res;
    nextTick(() => {
      barChartBInit();
      pieChartBInit();
    })
  }
}

function pieChartAInit() {
  if (!piechart_a.value) {
    return
  }

  let arr = tableData1.value.map(e => { return { name: e.code_name, value: e.school_addnum } });;
  let chart = echarts.init(piechart_a.value);
  chart.setOption({
    ...chartOption['pie']['option'],
    legend: {
      type: 'scroll',
    },
    series: [
      {
        ...chartOption['pie']['series'],
        top: 30,
        data: arr
      }
    ]
  })
  chartDom[1] = chart;
}

function pieChartBInit() {
  if (!piechart_b.value) {
    return
  }

  let arr = apiData2.value.school;
  let chart = echarts.init(piechart_b.value);
  chart.setOption({
    ...chartOption['pie']['option'],
    legend: {
      type: 'scroll',
    },
    series: [
      {
        ...chartOption['pie']['series'],
        top: 30,
        data: arr
      }
    ]
  })
  chartDom[3] = chart;
}

async function getTable1() {
  await api({
    url: `${baseUrl}/citychart`,
    params: {
      level: mapStore.level,
      code: mapStore.code,
      Ym: Ym.value
    }
  }).then((res) => {
    if (res) {
      tableData1.value = res;
      nextTick(() => {
        barChartAInit();
        pieChartAInit();
      })
    }
  });
}

async function getTable2() {
  await api({
    url: `${baseUrl}/areadatalist`,
    params: {
      level: mapStore.level,
      code: mapStore.code,
      year: Y2.value
    }
  }).then((res) => {
    if (res) {
      tableData2.value = res.data;
    }
  });
}

let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  getTable3();
};

const handleCurrentChange = (val) => {
  tablePage.page = val;
  getTable3();
};

function indexSort(n) {
  let sort = tablePage.total - (((tablePage.page - 1) * (tablePage.per_page)) + n)
  if (sort > 0) {
    return sort
  }
}

async function getTable3() {
  tableData3.value = [];
  await api({
    url: `${baseUrl}/areaschoolsdata`,
    params: {
      level: mapStore.level,
      code: mapStore.code,
      year: Y3.value,
      page: tablePage.page,
      per_page: tablePage.per_page
    }
  }).then((res) => {
    if (res) {
      tableData3.value = res.data;
      tablePage.total = res.total;
    }
  });
}

function barChartAInit() {
  if (!barchart_a.value) {
    return
  }
  let chart = echarts.init(barchart_a.value);
  console.log(tableData1.value);
  let linename = tableData1.value.map(e => { return e.code_name });
  let linedata1 = tableData1.value.map(e => { return e.school_addnum });

  chart.setOption({
    ...chartOption['bar'],
    legend: {},
    grid: {
      top: 40,
      bottom: 20,
      left: 20,
      right: 20
    },
    xAxis: {
      type: 'category',
      data: linename,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        interval: 0,
        rotate: 0,
        hideOverlap: true,
        lineHeight: 14,
        formatter: function (value) {
          return nChartText(value, 7)
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        min: 0,
        alignTicks: true,
      },
      {
        type: 'value',
        min: 0,
        axisLabel: {
          formatter: '{value}%'
        },
        alignTicks: true,
      }],
    series: [
      {
        name: '新增机构数',
        data: linedata1,
        type: 'bar',
      },
    ]
  })
  chartDom[0] = chart;
}

function barChartBInit() {
  if (!barchart_b.value) {
    return
  }
  let chart = echarts.init(barchart_b.value);
  let linename = apiData2.value.chart.map(e => { return e.month });
  let linedata1 = apiData2.value.chart.map(e => { return e.school_addnum });
  chart.setOption({
    ...chartOption['bar'],
    xAxis: {
      type: 'category',
      data: linename,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        interval: 0,
        rotate: 0,
        hideOverlap: true,
        lineHeight: 14,
        formatter: function (value) {
          return nChartText(value, 7)
        }
      }
    },
    yAxis: [{
      type: 'value',
      min: 0,
      axisLabel: {
        formatter: '{value}'
      },
      alignTicks: true,
    }, {
      type: 'value',
      min: 0,
      axisLabel: {
        formatter: '{value}%'
      },
      alignTicks: true,
    },
    ],
    series: [
      {
        name: '新增托位数',
        data: linedata1,
        type: 'bar',
        barWidth: 60,
        tooltip: {
          valueFormatter: function (value) {
            return value + '';
          }
        },
      },
    ]
  })
  chartDom[2] = chart;
}

function scroll(e) {
  gotopshow.value = (e.scrollTop > 1000);
}

function gotop(el) {
  console.log(el);
  el.setScrollTop(0)
}

onMounted(() => {
});

watch(
  () => route,
  async (newVal) => {
  },
  { immediate: true, deep: true }
);
watch(
  () => mapStore.name,
  async (newVal) => {
    console.log(mapStore.name)
    if (newVal) {
      if (mapStore.level == 4) {
        getApiData1();
        getApiData2();
        getTable2();
        getTable3();
      } else {
        getApiData1();
        getTable1();
      }
    }
  },
  { immediate: true, deep: true }
);
onUnmounted(() => {
  //移除监听事件
  window.removeEventListener("resize", cancalDebounce);
});
const debounce = (fn, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn();
    }, delay);
  };
};
const cancalDebounce = debounce(pageWidthChange, 100);
function pageWidthChange() {
  for (const iterator of chartDom) {
    iterator && iterator.resize();
  }
}
window.addEventListener("resize", cancalDebounce);
</script>
<style lang="scss">
.tip-line {
  height: 32px;
  width: 140px;
  padding: 0 16px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  display: flex;
  align-items: center;
}

.tip-border {
  height: 34px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 4px;
}

.school-home {
  .el-col {
    margin-bottom: 0 !important;
  }
}

.drawer-right {
  .el-drawer__header {
    display: none !important;
  }

  .el-drawer__body {
    padding: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
.page {
  height: 100vh;
}

.school-home {
  display: flex;
  position: relative;
  justify-content: space-between;

  .gotop {
    width: 126px;
    height: 40px;
    background: rgba(0, 0, 0, 0.65);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    position: absolute;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    z-index: 99;

    img {
      width: 14px;
      height: 16px;
      margin-right: 14px;
    }
  }

  .main-part {
    width: 100%;
    overflow: hidden;
  }

  .b20 {
    font-size: 20px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .t14 {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
  }

  .b16 {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .t12 {
    font-size: 12px;
    color: #4B535E;
  }

  .b32 {
    font-size: 32px;
    color: rgba(0, 0, 0, 0.45);
  }

  .b24 {
    font-size: 32px;
    color: rgba(0, 0, 0, 0.85);
  }


  .right {
    flex: auto;
    border-left: 1px solid#f4f4f4;
    min-width: 320px;
    max-width: 400px;

    div,
    p,
    span,
    img {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  }

  .right-btn {
    flex: 0 0 65px;
    width: 24px;
    height: 80px;
    background: #FFFFFF;
    box-shadow: 0px 0px 24px -8px rgba(0, 0, 0, 0.5);
    border-radius: 100px 0px 0px 100px;
    border: 1px solid #F4F4F4;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    right: 0;
    top: calc(50% + $headerHeight / 2);
    transform: translateY(-50%);
    cursor: pointer;
  }
}

.file-list-drag-bar {
  height: 1px;
  width: 100%;
  cursor: row-resize;
  background-color: #f4f4f4;
  border-top: 1px solid white;
  border-bottom: 1px solid white;
  box-sizing: content-box;
  position: absolute;
  z-index: 99;
}
</style>
<style lang="scss">
.potential_pagination {
  margin-top: 20px;

  .el-pagination button,
  .el-pager li {
    background: transparent !important;
  }
}

.potential-table .el-tabs__nav-wrap::after {
  height: 0;
}
</style>

<style lang="scss" scoped>
.table-header {
  height: 50px;
  background: #fafafa;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E8E8E8;

  p {
    flex: 1;

  }

  p:first-of-type {
    width: 60px;
    text-align: center;
  }
}

.table-list {
  height: 50px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E8E8E8;

  p {
    flex: 1;

    span {
      display: block;
      color: #fff;
      border-radius: 2px;
      width: 60px;
      height: 30px;
      text-align: center;
      line-height: 30px;
    }
  }

  p:first-of-type {
    width: 60px;
    text-align: center;
  }
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-bottom: 100px;
  height: 120px;
  align-items: center;
}

.potential-table {
  width: calc(100% - 48px);
  margin: 16px auto 24px;
}

.potential_channel {
  width: calc(100% - 48px);
  margin: 16px auto 24px;
  position: relative;
  z-index: 1;

  .top {
    overflow: hidden;
    border-bottom: 1px solid #E8E8E8;

    .item {
      position: relative;
      flex: 1;
      width: calc(100% / 6);
      padding: 20px 24px;

      .key {
        flex: 0 0 24px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #5B8FF9;
        color: #fff;
        font-size: 14px;
        margin-right: 8px;
        font-weight: 500;
      }

      .text {
        flex: auto;
        width: calc(100% - 60px);
      }

      p {
        line-height: 24px;
        display: flex;
      }

      b {
        height: 32px;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 32px;
      }

      &.flex::before {
        content: '';
        width: 1px;
        height: 32px;
        background: #E8E8E8;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }

      &.flex:first-of-type::before {
        content: '';
        width: 0px;
      }
    }

    .item:first-child {
      background: #FAFAFA;
      border-radius: 8px;
    }
  }

  .bottom {
    .color-parent {
      margin-top: 32px;
    }

    .total {
      margin-top: 32px;
      color: rgba(0, 0, 0, 0.44);
    }

    .item {
      display: flex;
      align-items: center;
      margin-right: 24px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.44);
    }

    .color {
      width: 8px;
      height: 8px;
      margin-right: 6px;
    }
  }

  .bar-parent {
    width: 100%;
    border-radius: 4px;
    background: #eee;
    height: 32px;
    margin-top: 24px;
    overflow: hidden;

    .color {
      height: 100%;
      margin-right: 0px;
    }
  }
}

.type {
  span {
    font-size: 14px;
    margin-right: 24px;
    cursor: pointer;
  }

  .active {
    color: #1890FF;
  }
}

.b40 {
  font-weight: bold;
  font-size: 40px;
  height: 40px;
  color: rgba(0, 0, 0, 0.85);
  align-items: flex-end;

  .t14 {
    padding: 0 4px;
    line-height: 1.6;
  }

  .b24 {
    font-size: 24px;
    color: #1890FF;
    font-weight: bold;
    line-height: 1.2;
    cursor: pointer;
  }
}


.bar-group {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  flex-direction: column;

  .bar {
    width: 100%;
    height: 66px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px 0 24px;
    background: #2d99fa;
    margin-top: 24px;
    position: relative;

    span {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
    }

    &::before {
      content: '';
      position: absolute;
      top: -100%;
      left: -16px;
      border-top: 66px solid rgba(0, 0, 0, 0);
      border-right: 16px solid rgba(0, 0, 0, 0);
      border-bottom: 66px solid #fff;
      border-left: 16px solid rgba(0, 0, 0, 0);
    }
  }
}

.number-group {
  flex: 1;

  .number {
    width: 100%;
    height: 66px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    margin-top: 24px;
    position: relative;
    margin-left: 32px;

    span {
      font-size: 14px;
      color: #585858;
      line-height: 22px;
    }

    b {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 32px;
    }
  }

  .arrow::before {
    content: '';
    width: 8px;
    height: 70px;
    background: url('@/assets/images/potential/arrow.png') no-repeat center;
    background-size: 100%;
    position: absolute;
    top: -50px;
    left: -24px;
  }
}

.color-list {
  padding: 24px 24px;
  flex: 1;

  p {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span:last-of-type {
      font-weight: bold;
      font-size: 24px;
      flex: 0 0 40px;
      text-align: right;
    }

    span:nth-of-type(2) {
      flex: auto;
    }

    span:first-of-type {
      width: 12px;
      height: 12px;
      border: 2px solid #FFFFFF;
      opacity: 0.5;
      border-radius: 50%;
      flex: 0 0 12px;
      transform: translateX(-26px);
    }

    span:first-of-type::after {
      content: '';
      display: block;
      border-bottom: 1px dashed #979797;
      width: 20px;
      transform: translate(12px, 4px);
    }
  }

  .c1 {
    color: #3BA1FD
  }

  .c2 {
    color: #4DCA73
  }

  .c3 {
    color: #FAD541
  }

  .c4 {
    color: #F2657B
  }
}

.color-bar {
  height: 100%;
  width: 48px;
  background: linear-gradient(180deg, #3AA0FF 0%, #4DCB73 33%, #FAD541 66%, #F2637B 100%);
  border-radius: 5px;
}

.color-text {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
</style>