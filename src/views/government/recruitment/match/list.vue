<template>
  <div class="page" v-loading="pageLoading">
    <el-scrollbar v-if="detailId == ''" class="page-table">
      <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="托育机构：">
                  <el-input v-model="tableSearch.school_name" placeholder="请输入" clearable />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="所属区县：">
                  <el-select v-model="tableSearch.work_location_area_code" placeholder="全部">
                    <el-option label="全部" value="" />
                    <el-option v-for="area in selectOptions.areas" :key="area.code" :label="area.name"
                      :value="area.code" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="招聘职位：">
                  <el-input v-model="tableSearch.job_name" placeholder="请输入" clearable />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="岗位角色：">
                  <el-select v-model="tableSearch.roles_id" placeholder="全部">
                    <el-option label="全部" value="" />
                    <el-option v-for="role in selectOptions.roles" :key="role.id" :label="role.title"
                      :value="role.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="截止时间：">
                  <el-date-picker v-model="tableSearch.expiration_period" type="daterange" range-separator="—"
                    start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="用工形式：">
                  <el-select v-model="tableSearch.employment_form" placeholder="全部">
                    <el-option label="全部" value="" />
                    <el-option v-for="(label, value) in selectOptions.employment_form" :key="value" :label="label"
                      :value="value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
              <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt">
        <div class="left">
          <el-button type="primary" @click="add">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <Plus />
            </el-icon>新增</el-button>
          <!-- <el-button type="primary">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <DocumentAdd />
            </el-icon>批量导入</el-button> -->


        </div>
        <div class="right">
        </div>

      </div>

      <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
        <el-table-column prop="row_num" min-width="80" fixed="left" label="序号" />
        <el-table-column prop="name" fixed="left" label="求职者姓名" min-width="120"></el-table-column>
        <el-table-column prop="job_name" label="应聘岗位" min-width="100">
          <template #default="scope">
            {{ getJobName(scope.row.roles_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" min-width="80" align="center">
          <template #default="scope">
            {{ getGender(scope.row.gender) }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80">
          <template #default="scope">
            {{ calculateAge(scope.row.birth_date) }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="mobile" label="手机号" min-width="130"></el-table-column> -->
        <el-table-column prop="educational_require" label="学历" min-width="80">
          <template #default="scope">
            {{ getEducationRequire(scope.row.educational_require) }}
          </template>
        </el-table-column>
        <el-table-column prop="major_name" label="所学专业" min-width="80" align="center"></el-table-column>
        <el-table-column prop="major_name" label="期望薪资" align="center" min-width="150">
          <template #default="scope">
            {{ scope.row.salary_benefits_start }} - {{ scope.row.salary_benefits_end }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="在职状态" min-width="120">
          <template #default="scope">
            {{ getJobStatus(scope.row.status) }}
          </template>
        </el-table-column>

        <!-- <el-table-column prop="certification_requirements" label="持证要求" min-width="150">
          <template #default="scope">
            {{ getCertificationRequirements(scope.row.certification_requirements) }}
          </template>
        </el-table-column> -->
        <el-table-column prop="match_degree" label="匹配度" min-width="100" align="center">
          <template #default="scope">
            {{ scope.row.match_degree }}%
          </template>
        </el-table-column>

        <!-- <el-table-column prop="personal_advantages" label="个人优势" min-width="200"
          show-overflow-tooltip></el-table-column> -->
        <el-table-column min-width="240" fixed="right">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button type="primary" @click="viewDetail(scope.row)" link>查看</el-button>
            <el-button type="primary" @click="editDetail(scope.row)" link>编辑</el-button>
            <el-button type="danger" @click="deleteItem(scope.row)" link>删除</el-button>
            <el-button type="success" @click="pushResume(scope.row)" link>一键推送</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
          :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-scrollbar>


    <template v-else>
      <!-- TODO: 创建简历详情组件 -->
      <!-- <detail-page :detail-data="formData" :select-options="selectOptions" :key="formData.id" /> -->
      <handle-page v-if="detailId" :select-options="selectOptions" :detailId="detailId" />
    </template>

    <!-- 一键推送弹窗 -->
    <el-dialog v-model="pushDialogVisible" title="一键推送" width="500px" :before-close="cancelPush"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="pushForm" label-width="100px" class="push-form" label-position="top">
        <el-form-item label="推送区域" prop="area_code">
          <el-select v-model="pushForm.area_code" placeholder="全部" @change="onAreaChange" style="width: 100%;">
            <el-option v-for="item in areaOptions" :key="item.code" :label="item.text" :value="item.code" />
          </el-select>
        </el-form-item>

        <el-form-item label="选择机构" prop="organization_id">
          <el-select v-model="pushForm.organization_id" placeholder="请选择机构" filterable style="width: 100%;">
            <el-option v-for="item in organizationOptions" :key="item.id"
              :label="item.name + (item.job_name ? ` (${item.job_name})` : '')" :value="item.id" />
          </el-select>
        </el-form-item>

        <div class="job-info" v-if="pushForm.organization_id">
          <div class="job-label">应聘岗位：</div>
          <div class="job-value">{{ getSelectedOrganizationJobName() }}</div>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelPush">取消</el-button>
          <el-button type="primary" @click="confirmPush" :loading="pushFormLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, computed, watch } from "vue";
import api from "@/axios.js";
import { Plus, DocumentAdd } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from 'element-plus';
import { useAuthStore } from "@/stores/auth-store";
import { useBreadStore } from '@/stores/bread-store';
import { useRoute, useRouter } from "vue-router";
import DetailPage from './components/detail.vue';
import HandlePage from './components/handle.vue';


const breadStore = useBreadStore();

const route = useRoute();
const router = useRouter();
const baseUrl = "/api/back/recruitment/cv";
let baseName = "简历";

// 响应式状态和引用
const pageLoading = ref(true);
const seachOpen = ref(false);
const tableData = ref([]);
let detailId = ref('');

const tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});

const tableSearch = reactive({
  school_name: '',
  work_location_area_code: '',
  job_name: '',
  roles_id: '',
  expiration_period: '',
  employment_form: '',
});

const selectOptions = ref({
  status: {},
  gender: {},
  employment_form: {},
  educational_require: {},
  certification_requirements: {},
  roles: [],
  areas: []
});


const viewDetail = (data) => {
  router.push(`/government/recruitment/match/list?id=${data.id}&type=view`);
};

// 编辑简历
const editDetail = (data) => {
  router.push(`/government/recruitment/match/list?id=${data.id}`);
};

// 删除简历
const deleteItem = async (data) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除该条数据吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const res = await api({
      method: 'GET',
      url: `${baseUrl}/del`,
      params: { id: data.id }
    });

    if (res) {
      ElMessage.success('删除成功');
      list(); // 重新加载列表
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 一键推送简历
const pushResume = async (data) => {
  try {
    // 显示推送弹窗
    pushDialogVisible.value = true;
    selectedResume.value = data;

    // 重置弹窗表单
    resetPushForm();

    // 加载机构数据
    await loadOrganizations();
  } catch (error) {
    console.error('打开推送弹窗失败:', error);
    ElMessage.error('打开推送弹窗失败');
  }
};

// 推送弹窗相关状态
const pushDialogVisible = ref(false);
const selectedResume = ref(null);
const pushFormLoading = ref(false);

// 推送表单数据
const pushForm = reactive({
  area_code: '',
  organization_id: ''
});

// 选项数据
const areaOptions = ref([]);
const organizationOptions = ref([]);

// 重置推送表单
const resetPushForm = () => {
  Object.assign(pushForm, {
    area_code: '',
    organization_id: ''
  });
  organizationOptions.value = [];
};





// 加载机构数据
const loadOrganizations = async () => {
  try {
    // 获取用户所在的省市信息
    const { organizations } = useAuthStore().userInfo;

    const params = {
      work_location_province_code: organizations?.province || '',
      work_location_city_code: organizations?.city || '',
      work_location_area_code: pushForm.area_code || '',
      // roles_id: selectedResume.value?.job_id || ''
    };

    const res = await api({
      method: 'GET',
      url: '/api/back/recruitment/job/getSchools',
      params
    });

    if (res) {
      // 根据API返回格式处理数据
      const schools = res.data || [];

      // 转换数据格式以适配现有组件
      organizationOptions.value = schools.map(school => ({
        id: school.school_id,
        name: school.schoold_name, // 注意：API中是 schoold_name（可能是拼写错误）
        job_id: school.job_id,
        job_name: school.job_name
      }));
    }
  } catch (error) {
    console.error('加载机构数据失败:', error);
  }
};

// 区县选择变化
const onAreaChange = async (areaCode) => {
  pushForm.organization_id = '';
  await loadOrganizations();
};

// 获取选中机构的岗位名称
const getSelectedOrganizationJobName = () => {
  if (!pushForm.organization_id) return '';
  const selectedOrganization = organizationOptions.value.find(org => org.id === pushForm.organization_id);
  return selectedOrganization?.job_name || '';
};





// 确认推送
const confirmPush = async () => {
  try {
    pushFormLoading.value = true;

    // 验证表单
    if (!pushForm.organization_id) {
      ElMessage.warning('请选择机构');
      return;
    }

    // 从选中的机构中获取job_id和job_name
    const selectedOrganization = organizationOptions.value.find(org => org.id === pushForm.organization_id);

    const res = await api({
      method: 'POST',
      url: `${baseUrl}/push`,
      data: {
        cv_id: selectedResume.value.id,
        work_location_area_code: pushForm.area_code,
        job_id: selectedOrganization?.job_id,
        school_id: pushForm.organization_id
      }
    });

    if (res) {
      ElMessage.success('推送成功');
      pushDialogVisible.value = false;
      list(); // 重新加载列表
    }
  } catch (error) {
    console.error('推送失败:', error);
    ElMessage.error('推送失败');
  } finally {
    pushFormLoading.value = false;
  }
};

// 取消推送
const cancelPush = () => {
  pushDialogVisible.value = false;
  selectedResume.value = null;
  resetPushForm();
};



const getSelectOptions = async () => {
  try {
    // 获取下拉选项
    const res = await api({
      method: 'GET',
      url: '/api/back/recruitment/job/getSelect'
    });

    // 获取区县列表
    const { organizations } = useAuthStore().userInfo;
    const cityCode = organizations?.city;

    let areas = [];
    if (cityCode) {
      try {
        const areaRes = await api({
          method: 'GET',
          url: '/api/back/common/area',
          params: { city: cityCode }
        });
        if (areaRes && Array.isArray(areaRes)) {
          areas = areaRes.map(item => ({
            code: item.code,
            name: item.text
          }));
        }
      } catch (areaError) {
        console.error('获取区县列表失败:', areaError);
      }
    }

    if (res) {
      selectOptions.value = {
        ...res,
        areas: areas
      };

      // 同时更新推送弹窗的区县选项
      areaOptions.value = [
        { code: '', text: '全部' },
        ...areas.map(item => ({ code: item.code, text: item.name }))
      ];
    }
  } catch (error) {
    console.error('获取下拉选项失败:', error);
  }
};


const list = async () => {
  pageLoading.value = true;

  // 构建请求参数，过滤空值
  const params = {
    push_type: 2,
    per_page: tablePage.per_page,
    page: tablePage.page,
    ...tableSearch,
    ...(tableSearch.expiration_period && tableSearch.expiration_period.length === 2 && {
      start_time: tableSearch.expiration_period[0],
      end_time: tableSearch.expiration_period[1]
    })
  };
  delete params.expiration_period;
  
  // 过滤空值参数
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });

  try {
    const res = await api({
      method: 'GET',
      url: `${baseUrl}/filterTypeList`,
      params
    });
    if (res) {
      tableData.value = res.data || [];
      tablePage.total = res.total || 0;
      tablePage.page = res.current_page || 1;
      tablePage.per_page = Number(res.per_page) || 10;
    }
  } catch (error) {
    console.error("Failed to fetch list", error);
    tableData.value = [];
    tablePage.total = 0;
  } finally {
    pageLoading.value = false;
  }
};

const search = () => {
  tablePage.page = 1;
  list();
};

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};

const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};



const resetSearch = () => {
  Object.assign(tableSearch, {
    school_name: '',
    work_location_area_code: '',
    job_name: '',
    roles_id: '',
    expiration_period: '',
    employment_form: '',
  });
  tablePage.page = 1;
  list();
};

const genderMap = computed(() => selectOptions.value.gender || {});
const statusMap = {
  1: '离职-随时到岗',
  2: '在职-月内到岗',
  3: '在职-考虑机会',
};
const educationRequireMap = computed(() => selectOptions.value.educational_require || {});
const certificationRequirementsMap = computed(() => selectOptions.value.certification_requirements || {});

const getGender = (gender) => genderMap.value[gender] || '未知';

const getJobStatus = (status) => statusMap[status] || '未知';

const getEducationRequire = (education) => educationRequireMap.value[education] || '未知';
getJobName
const calculateAge = (birthDate) => {
  if (!birthDate) return '-'
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

const getCertificationRequirements = (certification) => {
  if (!certification) return '无要求';
  const certIds = String(certification).split(',').map(id => id.trim());
  return certIds.map(id => certificationRequirementsMap.value[id] || `未知证书`).join('、');
};


const add = () => {
  router.push(`/government/recruitment/match/list?id=0`);
};

// 路由监听
watch(
  () => route,
  (newVal) => {
    detailId.value = newVal.query.id || '';
    const type = newVal.query.type || '';
    if (detailId.value == '') {
      getSelectOptions();
      list();
      breadStore.breadcrumbBack = false;
      breadStore.breadcrumbTitle = '';
      breadStore.breadcrumbBackTip = false;
    } else {
      getSelectOptions();
      pageLoading.value = false;
      breadStore.breadcrumbBack = true;
      if (type == 'view') {
        breadStore.breadcrumbTitle = '查看' + baseName;
      } else {
        breadStore.breadcrumbTitle = `${detailId.value == 0 ? '新增' : '编辑'}${baseName}`;
      }
      breadStore.breadcrumbBackTip = true;
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.status-active {
  color: #409eff;
}

.status-ended {
  color: #909399;
}

.dialog-p {
  line-height: 20px;
  color: #262626;
  margin-bottom: 16px;

  span {
    color: rgba(0, 0, 0, 0.65);
  }
}

.push-form {

  .job-info {
    margin-top: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 4px;
    display: flex;
    align-items: center;

    .job-label {
      font-size: 14px;
      color: #666;
      margin-right: 8px;
    }

    .job-value {
      font-size: 14px;
      color: #262626;
      font-weight: 500;
    }
  }
}
</style>