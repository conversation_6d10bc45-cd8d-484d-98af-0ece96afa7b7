<template>
  <div class="page" v-loading="pageLoading">
    <el-scrollbar class="page-table">
      <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="16" :md="16" :lg="16">
                <el-form-item label="申请日期：">
                  <el-date-picker value-format="YYYY-MM-DD" format="YYYY-MM-DD" v-model="startendtime" type="daterange"
                    start-placeholder="请选择" end-placeholder="请选择" />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="婴幼儿姓名：">
                  <el-input v-model="tableSearch.name" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="性别：">
                  <el-select v-model="tableSearch.sex" placeholder="请选择">
                    <el-option label="男" :value="1"></el-option>
                    <el-option label="女" :value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="家长手机号：">
                  <el-input v-model="tableSearch.phone" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="所在班级：">
                  <el-select v-model="tableSearch.class_id" placeholder="请选择">
                    <el-option v-for="item in userClass" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
              <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt">
        <div class="left">
        </div>
        <div class="right">
          <el-radio-group v-model="tableSearch.status" @click="statusSearch">
            <el-radio-button v-for="(item, index) in status_arr" :key="index" :label="item.id">{{ item.name
            }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
        <el-table-column type="index" min-width="60" fixed="left" label="序号" :index="indexSort" />
        <el-table-column prop="name" fixed="left" label="姓名" min-width="100" />
        <el-table-column prop="sex" label="性别" min-width="80">
          <template #default="scope">
            {{ id2name(scope.row.sex, sex_arr) }}
          </template>
        </el-table-column>
        <el-table-column min-width="120" prop="birth" label="出生日期" />
        <el-table-column prop="class_id" label="所在班级" min-width="150">
          <template #default="scope">
            {{ id2name(scope.row.class_id, userClass, 'title') }}
          </template>
        </el-table-column>
        <el-table-column min-width="120" prop="phone" label="家长信息">
          <template #default="scope">
            <hideNumber :number="scope.row.phone" />
          </template>
        </el-table-column>
        <el-table-column min-width="140" prop="days" label="在托时长（天）" />
        <el-table-column min-width="120" prop="leave_time" label="离园时间" />
        <el-table-column min-width="180" prop="leave_reason" label="离园原因" />
        <el-table-column min-width="180" prop="number" label="申请编号" />
        <el-table-column min-width="200" fixed="right">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button type="primary" @click="edit(scope.row)" link>查看</el-button>
            <el-button type="primary" v-if="scope.row.status == 0" @click="confirm(scope.row)" link>同意离园</el-button>
            <el-button type="danger" v-if="scope.row.status == 0" @click="cancel(scope.row)" link>未通过</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
          :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
      <el-dialog v-model="confirmShow" :title="(formData.status == 1 ? '同意' : '未通过') + baseName" width="522px">
        <el-form :model="formData" label-position="top" label-width="200px" ref="formEl" :rules="rules"
          v-if="confirmShow">
          <p style="color: #666; margin-bottom: 24px;" v-if="formData.status == 1">是否同意当前婴幼儿的离园申请？离园完成后可在【已通过】列表中查看</p>
          <p style="color: #666; margin-bottom: 24px;" v-if="formData.status == 2">是否拒绝当前婴幼儿的离园申请？确定后可在【未通过】列表中查看</p>
          <el-form-item label="离园时间：" prop="leave_time" v-if="formData.status == 1">
            <el-date-picker :disabled-date="dd2" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
              v-model="formData.leave_time" type="date" placeholder="请选择" />
          </el-form-item>
          <el-form-item label="离园说明：" prop="leave_tips" v-if="formData.status == 1">
            <el-input v-model="formData.leave_tips" placeholder="请输入" type="textarea" :row="3" />
          </el-form-item>
          <el-form-item label="未通过原因：" prop="refuse_reason" v-if="formData.status == 2">
            <el-input v-model="formData.refuse_reason" placeholder="请输入" type="textarea" :row="3" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="confirmShow = false">取消</el-button>
            <el-button type="primary" @click="onSubmit(formEl)" :loading="apiLoading">确定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-drawer class="dialog-attendance" v-model="dialogShow" direction="rtl" size="794px"
        style="background: #fff">
        <template #header>
          <h4>查看详情</h4>
          <el-button v-if="tableSearch.status == 0" type="primary" @click="confirm(detailData)" round>同意离园</el-button>
          <el-button v-if="tableSearch.status == 0" type="danger" style="margin:0 24px;" @click="cancel(detailData)"
            round>未通过</el-button>
          <el-button v-if="tableSearch.status == 1" type="primary" @click="printPoster" round>下载报告</el-button>
        </template>
        <template #default>
          <div ref="posterEl">
            <h3 style="margin: 12px 0 24px;">基础信息</h3>
            <el-descriptions size="large" title="" :column="2" v-if="detailData.base">
              <el-descriptions-item width="120px" label-align="right" label="婴幼儿姓名：">{{ detailData.base.name || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="婴幼儿性别：">{{ detailData.base.sex || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="出生年月：">{{ detailData.base.birth || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="所在班级：">{{ detailData.base.class_name || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="申请日期：">{{ detailData.base.leave_time || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="离园说明：">{{ detailData.base.leave_reason ||
                '-'
              }}</el-descriptions-item>
            </el-descriptions>
            <h3 style="margin: 12px 0 24px;">体检信息</h3>
            <el-descriptions size="large" title="" :column="2" v-if="detailData.check">
              <el-descriptions-item width="120px" label-align="right" label="身高：">{{ detailData.check.height || '-'
              }}cm</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="头围：">{{ detailData.check.weight || '-'
              }}cm</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="体重：">{{ detailData.check.head_circum || '-'
              }}kg</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="BMI值：">{{ detailData.check.bmi || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="视力（左眼）：">{{ detailData.check.left_vision ||
                '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="视力（右眼）：">{{ detailData.check.right_vision
                ||
                '-'
              }}</el-descriptions-item>
            </el-descriptions>
            <h3 style="margin: 12px 0 24px;">疫苗接种信息</h3>
            <el-descriptions size="large" title="" :column="2" v-if="detailData.check">
              <el-descriptions-item width="120px" label-align="right" label="最新接种疫苗：">{{ detailData.vaccine_name || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="接种日期：">{{ detailData.vaccine_date || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="接种机构：">{{ detailData.hospital_name || '-'
              }}</el-descriptions-item>
            </el-descriptions>
            <h3 style="margin: 12px 0 24px;">在园信息</h3>
            <el-descriptions size="large" title="" :column="2" v-if="detailData.record">
              <el-descriptions-item width="120px" label-align="right" label="原托育机构名称：">{{ detailData.base.school_name ||
                '0'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="生活记录条数：">{{ detailData.record.life || '0'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="晨午晚检条数：">{{ detailData.record.checkup ||
                '0'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="成长时光条数：">{{ detailData.record.growth || '0'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="在托天数：">{{ detailData.record.days || '0'
              }}</el-descriptions-item>
            </el-descriptions>
            <h3 style="margin: 12px 0 24px;">通过离园审核</h3>
            <el-descriptions size="large" title="" :column="2" v-if="detailData.record">
              <el-descriptions-item width="120px" label-align="right" label="时间：">{{ detailData.check_pass_time || '-'
              }}</el-descriptions-item>
              <el-descriptions-item width="120px" label-align="right" label="审核人：">{{ detailData.check_user_name || '-'
              }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
      </el-drawer>
    </el-scrollbar>
  </div>
</template>

<script setup>
import {
  Plus,
  ArrowDown,
  RefreshRight,
  CircleCloseFilled,
  View,
  Hide
} from "@element-plus/icons-vue";
import { watch, onMounted, reactive, ref, provide, inject, nextTick, computed } from "vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
import { useRoute } from "vue-router";
import { str2arr, id2name, sex_arr, education, relationship } from "@/config/select";
import { upload } from "@/assets/utils/obs.js";
import { useConfigStore } from "@/stores/config-store";
import { dayjs } from 'element-plus';
import { useBreadStore } from '@/stores/bread-store';
import hideNumber from "@/views/components/hideNumber.vue";
import html2canvas from 'html2canvas';
let posterEl = ref(null);
import jsPDF from 'jspdf';
const { userClass, schoolId } = useAuthStore();
const breadStore = useBreadStore();
const detailData = ref('');
const { organizations } = useAuthStore().userInfo;
const { level } = organizations;
console.log('政府等级', level);
const route = useRoute();
let baseUrl = "/api/back/schoolleave";
let baseName = "离园申请";
let dialogShow = ref(false);
let pageLoading = ref(true);
let status_arr = [
  {
    name: "未审核",
    id: 0
  },
  {
    name: "已通过",
    id: 1
  },
  {
    name: "未通过",
    id: 2
  },
];
let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let formEl = ref(null);
let formData = reactive({
  id: '',
  status: '',
  refuse_reason: '',
  leave_tips: '',
  leave_time: '',
});
let apiLoading = ref(false);
let tableSearch = reactive({
  class_id: '',
  start_time: '',
  end_time: '',
  name: '',
  sex: '',
  phone: '',
  status: 0,
});
let seachOpen = ref(false);
let tableData = ref([]);
let startendtime = computed({
  get() {
    return [tableSearch.start_time, tableSearch.end_time];
  },
  set(newValue) {
    [tableSearch.start_time, tableSearch.end_time] = [...newValue];
  },
});
function dd1(date) { return dayjs(date) >= dayjs(formData.end_date) }
function dd2(date) { return dayjs(date) < dayjs(formData.start_date) }
let rules = {
  leave_time: [
    { required: true, message: '请选择离园时间', trigger: 'change' },
  ],
  refuse_reason: [
    { required: true, message: '请输入未通过原因', trigger: 'change' },
  ],
}

function printPoster() {
  setTimeout(() => {
    const ref = posterEl.value; // 截图区域
    console.log(ref);
    if (ref) {
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '保存中...'
      })
      html2canvas(ref, {
        dpi: 96,
        width: 794,
        x: -25,
        y: -25,
        height: 1123,
        useCORS: true,
        backgroundColor: '#fff',
      }).then(async (canvas) => {
        const dataURL = canvas.toDataURL('image/png');
        // window.open(dataURL)
        var doc = new jsPDF();
        doc.addImage(dataURL, 'JPEG', 0, 0, 794 * 74 / 96 / 3, 1123 * 74 / 96 / 3);
        doc.save('离园报告.pdf');
        loadingInstance.close();
        return
        const creatDom = document.createElement('a');
        document.body.appendChild(creatDom);
        creatDom.href = dataURL;
        creatDom.download = '离园报告';
        creatDom.click();
      })
    }
  }, 0);
}
let confirmShow = ref(false);
const add = async (formEL) => {
  setFormData();
  dialogShow.value = true;
};

const statusSearch = () => {
  setTimeout(() => {
    list()
  }, 0);
}

const confirm = async (data) => {
  setFormData(data);
  formData.status = 1;
  confirmShow.value = true;
}

const cancel = async (data) => {
  setFormData(data);
  formData.status = 2;
  confirmShow.value = true;
}

const edit = async (data) => {
  await api({
    url: `${baseUrl}/detail?id=${data.id}&school_id=${schoolId}`,
  }).then((res) => {
    if (res) {
      detailData.value = res;
      dialogShow.value = true;
    }
  });
};


const setFormData = async (data = {}) => {
  formData.id = data.id || 0;
  formData.leave_tips = data.leave_tips || '';
  formData.leave_reason = data.leave_reason || '';
  formData.leave_time = data.leave_time || '';
  formData.refuse_reason = data.refuse_reason || '';
}

function indexSort(n) {
  let sort = tablePage.total - (((tablePage.page - 1) * (tablePage.per_page)) + n)
  if (sort > 0) {
    return sort
  }
}

function stock(data) {
  setFormData(data);
  stockNum.value = data.stock;
  stockShow.value = true;
}

const list = async (data) => {
  pageLoading.value = true;
  let params = '';
  for (const key in tableSearch) {
    const element = tableSearch[key];
    if (element !== '') {
      params += `&${key}=${element}`
    }
  }
  await api({
    url: `${baseUrl}/list?per_page=${tablePage.per_page}&page=${tablePage.page}${params}&school_id=${schoolId}`,
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
};

const refresh = async () => {
  tablePage.page = 1;
  list();
};
const search = async () => {
  tablePage.page = 1;
  list();
};

const handleClose = (done) => {
  ElMessageBox.confirm('尚有编辑的内容未保存，是否退出？', '提示')
    .then(() => {
      done()
    })
    .catch(() => {
    })
}

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};

const resetSearch = async () => {
  tableSearch.status = 0;
  tableSearch.class_id = '';
  tableSearch.start_time = '';
  tableSearch.end_time = '';
  tableSearch.name = '';
  tableSearch.sex = '';
  tableSearch.phone = '';
  tablePage.page = 1;
  list();
};

const onSubmit = async (formEl) => {
  if (!formEl) return
  let valid = await formEl.validate((valid) => { })
  if (!valid) {
    return false;
  }

  let data = {};
  for (const key in formData) {
    data[key] = formData[key];
  }

  apiLoading.value = true;

  if (data.id === 0) {

  } else {
    let res = await api({
      url: `${baseUrl}/check`,
      method: "post",
      data,
    });
    if (res) {
      ElMessage.success("审核成功");
      await list();
      confirmShow.value = false;
    }
  }

  apiLoading.value = false;
};


const push = async (data) => {
  ElMessageBox.confirm(`上报信息提交后不可修改，确认上报吗`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        url: `${baseUrl}/datareport`,
        method: 'GET',
        params: {
          id: data.id
        },
      });
      if (res) {
        ElMessage.success("上报成功");
        list();
      }
    })
    .catch(() => { });
};
const del = async (data) => {
  ElMessageBox.confirm(`确认删除该${baseName}吗`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        url: `${baseUrl}/delplan`,
        method: 'POST',
        data: {
          id: data.id,
        },
      });
      if (res) {
        ElMessage.success("删除成功");
        list();
      }
    })
    .catch(() => { });
};

onMounted(() => {
  list();
})

watch(
  () => route,
  (newVal) => {
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.dialog-p {
  line-height: 20px;
  color: #262626;
  margin-bottom: 16px;

  span {
    color: rgba(0, 0, 0, 0.65);
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  margin: 0;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader img.avatar {
  width: 280px;
  height: 158px;
}

.avatar-uploader video.avatar {
  width: 280px;
  height: 158px;
}

.el-icon.avatar-uploader-icon {
  font-size: 14px;
  font-style: normal;
  color: #8c939d;
  width: 280px;
  height: 158px;
  text-align: center;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
}

.remove {
  position: absolute;
  right: 6px;
  top: 6px;
  z-index: 10;
}
</style>
