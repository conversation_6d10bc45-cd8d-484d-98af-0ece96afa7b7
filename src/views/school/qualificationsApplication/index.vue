<template>
  <div
    class="page-container page"
    v-loading="pageLoading"
    :class="{ 'page-nobread': !breadStore.breadcrumbShow }"
  >
    <el-scrollbar class="page-table back-page" v-if="isApplyFor">
      <div>
        <el-tabs v-model="activeTab" class="application-tabs" @tab-change="changeTabs">
          <el-tab-pane label="线上申请" name="online"></el-tab-pane>
          <el-tab-pane label="申请记录" name="record"></el-tab-pane>
          <el-tab-pane label="草稿箱" name="draft"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="cards-container" v-loading="pageLoading" v-if="activeTab === 'online'">
        <el-row :gutter="20">
          <el-col :xs="8" :sm="8" :lg="8" v-for="(item, index) in onlineData" :key="item.id">
            <div
              class="card-item"
              :style="{ backgroundImage: cardGradients[index % cardGradients.length] }"
            >
              <div class="card-title">{{ item.name }}</div>
              <el-button
                type="primary"
                class="apply-button"
                @click="handleApply(item.id, item.name)"
                :style="{ color: cardGradient[index % cardGradient.length] }"
              >
                立即申请
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 申请记录 -->
      <el-table
        ref="table"
        :data="recordData"
        v-loading="pageLoading"
        v-if="activeTab === 'record'"
      >
        <el-table-column
          min-width="60"
          fixed="left"
          label="序号"
          align="center"
          type="index"
        ></el-table-column>
        <el-table-column label="申请资质名称" prop="type_name" width="180"></el-table-column>
        <el-table-column label="申请人" prop="contact_name" width="120"></el-table-column>
        <el-table-column label="申请时间" prop="created_at" width="180">
          <template #default="scope">
            {{ new Date(scope.row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="申请状态" prop="status_name"></el-table-column>
        <el-table-column width="180" fixed="right" align="center">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button type="primary" @click="review(scope.row)" link>查看</el-button>
            <el-button v-if="scope.row.status === 1" type="primary" @click="downloadCertificate(scope.row.annexs)" link>
              下载证书  
            </el-button>
            <el-button v-if="scope.row.status === 0" type="primary" @click="draftSubmit(scope.row, 2)" link>
              撤回
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 草稿 -->
      <el-table ref="table" :data="draftData" v-loading="pageLoading" v-if="activeTab === 'draft'">
        <el-table-column
          min-width="60"
          fixed="left"
          label="序号"
          align="center"
          type="index"
        ></el-table-column>
        <el-table-column label="申请人" prop="contact_name"></el-table-column>
        <el-table-column label="创建时间" prop="created_at">
          <template #default="scope">
            {{ new Date(scope.row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column width="180" fixed="right" align="center">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button type="primary" @click="draftEdit(scope.row)" link>编辑</el-button>
            <el-button type="primary" @click="draftSubmit(scope.row, 1)" link>提交</el-button>
            <el-button type="primary" @click="draftDelete(scope.row.id)" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :page-size="tablePage.per_page"
          background
          layout="prev, pager, next , sizes, total"
          :total="tablePage.total"
          v-model:current-page="tablePage.page"
          v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-scrollbar>
    <el-scrollbar class="page-table back-page" v-if="!isApplyFor">
      <el-page-header
        :icon="ArrowLeft"
        title="返回"
        @back="
          isApplyFor = true;
          breadStore.breadcrumbShow = true;
        "
      >
        <template #content>
          <p class="b20 mb20 mt20">《{{ formTitle }}》线上申请表</p>
        </template>
      </el-page-header>
      <el-form
        :disabled="activeTab === 'record'"
        :model="formData"
        style="overflow: hidden"
        :rules="rules"
        label-position="top"
        ref="formRef"
      >
        <el-row :gutter="20">
          <!-- 第一行 -->
          <el-col :span="12">
            <el-form-item label="机构注册登记名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入机构注册登记名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact_name">
              <el-input v-model="formData.contact_name" placeholder="请输入联系人姓名"></el-input>
            </el-form-item>
          </el-col>
          <!-- 第二行 -->
          <el-col :span="12">
            <el-form-item label="机构所属街道" prop="street">
              <el-cascader
                :options="cityList"
                v-model="city_code"
                :props="props"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人手机号" prop="contact_phone">
              <el-input
                v-model.number="formData.contact_phone"
                placeholder="请输入联系人手机号"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 第三行 -->
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="formData.address" placeholder="请输入详细地址"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入联系人邮箱地址"></el-input>
            </el-form-item>
          </el-col>
          <!-- 第四行 -->
          <el-col :span="12">
            <el-form-item label="机构类型" prop="quality">
              <el-select v-model="formData.quality" placeholder="请选择机构类型">
                <el-option
                  v-for="item in qualitylist"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构性质" prop="type">
              <el-select v-model="formData.type" placeholder="请选择机构性质">
                <el-option
                  v-for="item in typelist"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 第五行  -->
          <el-col :span="24">
            <el-form-item label="上传附件" prop="annex">
              <el-upload
                :limit="5"
                ref="uploadRef"
                accept="image/*"
                :on-change="uploadChange"
                :on-remove="handleFileRemove"
                v-model:file-list="flie_arr"
                :auto-upload="false"
              >
                <template #trigger>
                  <el-button type="primary">选择图片</el-button>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          style="margin: 20px 0; display: flex; justify-content: center; gap: 10px"
          v-if="activeTab !== 'record'"
        >
          <el-button type="primary" @click="onSubmit(1)" :loading="apiLoading">提交申请</el-button>
          <el-button @click="onSubmit(2)" :loading="apiLoading">保存至草稿箱</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-row>
        <div v-else>
          <el-divider style="margin-top: 0" />
          <el-timeline style="height: 100px">
            <el-timeline-item color="#4095E5">
              <div>申请提交时间：{{ new Date(recordDetail.created_at).toLocaleString() }}</div>
            </el-timeline-item>
            <el-timeline-item
              :color="
                recordDetail.status === 0
                  ? '#bebebe'
                  : recordDetail.status === 1
                  ? '#4095E5'
                  : '#ed6a7c'
              "
            >
              <div
                :style="{
                  color:
                    recordDetail.status === 0
                      ? '#bebebe'
                      : recordDetail.status === 1
                      ? '#4095E5'
                      : '#ed6a7c',
                }"
              >
                {{
                  recordDetail.status === 0 ? "待审核" : recordDetail.status === 1 ? "通过" : "驳回"
                }}
              </div>
              <div style="margin: 10px 0" v-if="recordDetail.status !== 0">
                审核意见：{{ recordDetail.review_opinions || "无" }}
              </div>
              <div v-if="recordDetail.status !== 0">
                审核时间：{{ new Date(recordDetail.review_time).toLocaleString() || "未审核" }}
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-form>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
import { ArrowLeft, UploadFilled, Upload } from "@element-plus/icons-vue";
import { watch, onMounted, reactive, ref, nextTick, computed, onBeforeUnmount } from "vue";
import { ElMessage, genFileId } from "element-plus";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
const { isSuperAdmin, schoolId, userClass } = useAuthStore();
import { useBreadStore } from "@/stores/bread-store";
const breadStore = useBreadStore();
import { upload } from "@/assets/utils/obs.js";
import { useConfigStore } from "@/stores/config-store";
const { organizations_id, identity_id, organizations } = useAuthStore().userInfo;
const { level, province, city, area } = organizations;
const { provinceList, getCityApi, getAreaApi, getStreetApi } = useConfigStore();
import { checkValue } from "@/assets/utils/index.js";
// import { usePlatformStore } from "@/stores/platform-store";
// const { h5_url } = usePlatformStore().platformData;
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
let pageLoading = ref(false);
let baseUrl = "/api/back/aptitude";
// applyFor
let isApplyFor = ref(true); //显示 首页/申请表
let apiLoading = ref(false);
const tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
// 状态管理
const activeTab = ref("online");
const cardGradients = [
  "linear-gradient(to bottom, #54BDEE, #4095E5)",
  "linear-gradient(to bottom, #FDB545, #FE984B)",
  "linear-gradient(to bottom, #519BF2, #547EF5)",
  "linear-gradient(to bottom, #FC7776, #FB5B8A)",
  "linear-gradient(to bottom, #3ECAA9, #36A9C6)",
  "linear-gradient(to bottom, #EF4D8E, #ED6A7C)",
];
const cardGradient = ["#4095E5", "#FE984B", "#547EF5", "#FB5B8A", "#36A9C6", "#ED6A7C"];
// 资质类型列表
const onlineData = ref([]);
const recordData = ref([]);
const draftData = ref([]);
const changeTabs = (val) => {
  activeTab.value = val;
  tablePage.page = 1;
  getListData();
}
const getListData = async () => {
  pageLoading.value = true;
  try {
    const tabConfig = {
      online: {
        url: `${baseUrl}/typelist?page=${tablePage.page}&per_page=${tablePage.per_page}`,
        dataKey: "onlineData",
      },
      record: {
        url: `${baseUrl}/list?school_id=${schoolId}&type=1&page=${tablePage.page}&per_page=${tablePage.per_page}`,
        dataKey: "recordData",
      },
      draft: {
        url: `${baseUrl}/list?school_id=${schoolId}&type=2&page=${tablePage.page}&per_page=${tablePage.per_page}`,
        dataKey: "draftData",
      },
    };
    const config = tabConfig[activeTab.value];
    if (!config) return;

    const res = await api({ url: config.url });
    if (res) {
      const dataRefs = { onlineData, recordData, draftData };
      // 根据不同数据类型使用不同的响应数据结构
      const value = config.dataKey === "onlineData" ? res : res.data;
      dataRefs[config.dataKey].value = value;
      tablePage.total = res.total;
    }
  } catch (error) {
    console.error("Failed to fetch data:", error);
  } finally {
    pageLoading.value = false;
  }
};
getListData();

const recordDetail = reactive({});
const review = async (info) => {
  formTitle.value = info.type_name;
  let res = await api({
    url: `${baseUrl}/detail?id=${info.id}&school_id=${schoolId}`,
  });
  if (res) {
    Object.assign(recordDetail, res);
    setFormData(res);
    isApplyFor.value = false;
  }
};
const downloadCertificate = (info) => {
  // 遍历所有图片URL并下载
  info.forEach((element, index) => {
    // 添加延迟避免浏览器拦截多个下载请求
    setTimeout(() => {
      const link = document.createElement("a");
      // 移除URL中的空格并解码
      const url = element.url.trim();
      link.href = url;
      // 从URL提取文件名
      const fileName = url.split(/[\\/]/).pop();
      link.download = fileName;
      // 添加到DOM并触发下载
      document.body.appendChild(link);
      link.click();
      // 清理DOM元素
      document.body.removeChild(link);
    }, index * 300); // 每个下载间隔300ms
  });
};

const qualitylist = [
  { id: 1, name: "营利性" },
  { id: 2, name: "非营利性" },
];
const typelist = [
  { id: 1, name: "公办" },
  { id: 2, name: "民办" },
  { id: 3, name: "民办公助" },
  { id: 4, name: "公建民营" },
];
const uploadRef = ref(null);
const flie_arr = ref([]);
let file = "";
const handleFileRemove = (file, fileList) => {
  flie_arr.value = fileList;
  console.log("handleFileRemove", flie_arr.value);
};
function uploadChange(e) {
  file = e.raw;
  console.log("uploadChange", file);
}

const props = {
  checkStrictly: true,
  label: "text",
  value: "code",
  lazy: true,
  async lazyLoad(node, resolve) {
    console.log(node);
    if (node.data.code) {
      let res = await getStreetApi(node.data.code);
      res.forEach((element) => {
        element.leaf = node.level + level >= 4;
      });
      resolve(res);
    } else {
      resolve([]);
    }
  },
};
let city_code = computed({
  get() {
    return [formData.area, formData.street];
  },
  set(newValue) {
    [formData.area, formData.street] = [...newValue];
  },
});
let cityList = ref([]);
async function getCity() {
  cityList.value = await getAreaApi(city);
}

const formRef = ref(null);
let formTitle = ref("");
let formData = reactive({
  type_id: "",
  school_id: schoolId,
  name: "",
  area: "",
  street: "",
  address: "",
  quality: "",
  type: "",
  contact_name: "",
  contact_phone: "",
  email: "",
  annex: "",
  if_formal: "", //1:正式 2:草稿箱
});

const rules = reactive({
  name: [{ required: true, message: "请输入机构注册名称", trigger: "blur" }],
  area: [{ required: true, message: "请选择街道地址", trigger: "change" }],
  street: [{ required: true, message: "请选择街道地址", trigger: "change" }],
  address: [{ required: true, message: "请输入详细地址", trigger: "blur" }],
  quality: [{ required: true, message: "请选择资质类型", trigger: "change" }],
  type: [{ required: true, message: "请选择机构类型", trigger: "change" }],
  contact_name: [{ required: true, message: "请输入联系人姓名", trigger: "blur" }],
  contact_phone: [
    { required: true, message: "请填写手机号", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value && !checkValue(value, "phone")) {
          callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  email: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入邮箱"));
        } else if (!checkValue(value, "email")) {
          callback(new Error("请输入正确的邮箱"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  annex: [{ required: true, message: "请上传附件", trigger: ["change", "blur"] }],
});

function setFormData(data = {}) {
  formData.id = data.id || "";
  formData.type_id = data.type_id || "";
  formData.name = data.name || "";
  formData.area = data.area || "";
  formData.street = data.street || "";
  formData.address = data.address || "";
  formData.quality = data.quality || "";
  formData.type = data.type || "";
  formData.contact_name = data.contact_name || "";
  formData.contact_phone = data.contact_phone || "";
  formData.email = data.email || "";
  formData.annex = Array.isArray(data.annexs) ? data.annexs : [];
  if (data.annexs && Array.isArray(data.annexs)) {
    flie_arr.value = data.annexs.map((item) => ({
      name: item.url?.split(/[\/]/).pop() || "未命名文件",
      url: item.url,
    }));
  } else {
    flie_arr.value = [];
  }
  // 反显
  if(data.area){
    getCity();
  }
}
const isdraft = ref(true);
// 申请处理函数
const handleApply = (id, name) => {
  getCity();
  formTitle.value = name;
  formData.type_id = id;
  isdraft.value = true;
  isApplyFor.value = false;
};

const onSubmit = async (type) => {
  formData.if_formal = type;
  const formEl = formRef.value;
  if (!formEl) {
    console.error("Form reference not initialized");
    return;
  }
  console.log("文件上传", flie_arr.value);
  // 先处理文件上传 筛选出需要上传的本地文件
  const filesToUpload = flie_arr.value.filter((file) => file.raw !== undefined);
  try {
    if (filesToUpload.length > 0) {
      const uploadPromises = filesToUpload.map((file) => upload(file.raw, `guidance/notice`));
      const uploadResults = await Promise.all(uploadPromises);
      // 过滤掉上传失败的结果
      const successfulUploads = uploadResults.filter((result) => result);
      if (successfulUploads.length === 0) {
        ElMessage.error("所有文件上传失败，请重试");
        return;
      }
      // 更新文件列表中的URL
      filesToUpload.forEach((file, index) => {
        const result = successfulUploads[index];
        if (result) {
          const fileIndex = flie_arr.value.findIndex((f) => f.uid === file.uid);
          if (fileIndex !== -1) {
            flie_arr.value[fileIndex].url = result;
          }
        }
      });
    }
    // 从flie_arr中提取所有文件的URL
    formData.annex = flie_arr.value.map((file) => file.url).filter(Boolean);
    await nextTick();
    formEl.validateField("annex");
  } catch (error) {
    ElMessage.error(`上传失败: ${error.message}`);
    return;
  }
  // 使用Promise方式进行表单验证
  // console.log("validate", formEl.validate());
  console.log("formData.annex", formData.annex);
  try {
    await formEl.validate();
  } catch (error) {
    // 验证失败时终止提交
    return;
  }
  // 验证通过，继续提交逻辑
  let data = {};
  for (const key in formData) {
    data[key] = formData[key];
  }
  addData(data);
};
const addData = async (data) => {
  apiLoading.value = true;
  let annexString = "";
  data.annex.forEach((element) => {
    if (typeof element === "string") {
      annexString += element + ";";
    } else {
      annexString += element.url + ";";
    }
  });
  data.annex = annexString;
  let url = isdraft.value ? `${baseUrl}/add` : `${baseUrl}/update`;
  let res = await api({
    url: url,
    method: "post",
    data: data,
  });
  if (res) {
    ElMessage.success("保存成功");
    handleCancel();
    getListData();
  } else {
    handleCancel();
    ElMessage.error("保存失败");
  }
  apiLoading.value = false;
};

const draftEdit = (row) => {
  getCity();
  setFormData(row);
  console.log("draftEdit", formData);
  isdraft.value = false;
  formTitle.value = row?.name || "";
  isApplyFor.value = false;
};
const draftSubmit = async (data, type) => {
  isdraft.value = false;
  let draftSubmitData = reactive({
    id: data.id,
    type_id: data.type_id,
    school_id: schoolId,
    name: data.name,
    area: data.area,
    street: data.street,
    address: data.address,
    quality: data.quality,
    type: data.type,
    contact_name: data.contact_name,
    contact_phone: data.contact_phone,
    email: data.email,
    annex: data.annexs,
    if_formal: type,
  });
  addData(draftSubmitData);
};

const draftDelete = async (id) => {
  let res = await api({
    url: `${baseUrl}/del?id=${id}&school_id=${schoolId}`,
  });
  if (res) {
    ElMessage.success("删除成功");
    getListData();
  } else {
    ElMessage.error("删除失败");
  }
};

const handleCancel = () => {
  isApplyFor.value = true;
  apiLoading.value = false;
  setFormData();
};

const handleSizeChange = () => {
  getListData();
};
const handleCurrentChange = () => {
  getListData();
};

onBeforeUnmount(() => {
  breadStore.breadcrumbBack = false;
  breadStore.breadcrumbTitle = "";
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 0 20px 20px;
}

.cards-container {
  padding: 20px 0;
}

.card-item {
  height: 180px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.card-item:hover {
  transform: translateY(-5px);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  text-align: center;
}

.apply-button {
  background: white;
  border-radius: 20px;
  border: none;
}
.record-title {
  font-weight: bold;
  font-size: 16px;
  margin: 20px 0;
}
</style>
