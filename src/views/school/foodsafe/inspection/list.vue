<template>
  <div class="page" v-loading="pageLoading">
    <el-scrollbar class="page-table" v-loading="pageLoading">
      <div class="page-search page-search-close">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="12" :md="12" :lg="12">
                <el-form-item label="检查时间：">
                  <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" v-model="startendtime" type="datetimerange"
                    start-placeholder="请选择" end-placeholder="请选择" />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="上传情况：">
                  <el-select v-model="tableSearch.status" placeholder="请选择">
                    <el-option v-for="(item, index) in uploadOptions" :key="index" :label="item.name"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt">
        <div class="left">
          <el-button type="primary" @click="edit()">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <Plus />
            </el-icon>新增食品检查信息</el-button>
        </div>
      </div>
      <el-table ref="table" :data="tableData">
        <el-table-column type="index" min-width="60" fixed="left" label="序号"></el-table-column>
        <el-table-column min-width="100" label="供应单位名称" align="center">
          <template #default="scope">
            {{ scope.row.food_supply.unit_name }}
          </template>
        </el-table-column>
        <el-table-column prop="food_type" min-width="100" label="接收种类" align="center"></el-table-column>
        <el-table-column prop="food_num" min-width="100" label="接收数量" align="center">
          <template #default="scope">
            {{ scope.row.scope || "~" }}份
          </template>
        </el-table-column>
        <el-table-column prop="receive_time" min-width="100" label="接收时间" align="center"></el-table-column>
        <el-table-column min-width="100" label="上传情况" align="center">
          <template #default="scope">
            {{ scope.row.status === 0 ? '未上传' : '已上传' }}
          </template>
        </el-table-column>
        <el-table-column width="200" fixed="right" align="center">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button v-if="scope.row.status === 0" type="primary" @click="handleUpload(scope.row)" link>上传</el-button>
            <el-button type="primary" @click="edit(scope.row)" link>修改</el-button>
            <el-button type="primary" @click="edit(scope.row, 'view')" link>查看</el-button>
            <el-button type="primary" @click="del(scope.row)" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
          :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-scrollbar>
  </div>
  <el-dialog v-model="dialogShow" style="margin-bottom: 300px" :title="dialogTitle" width="500px">
    <el-form :model="formData" :rules="rules" label-position="top" ref="formEl">
      <el-form-item label="接收时间：" prop="receive_time">
        <el-date-picker v-model="formData.receive_time" value-format="YYYY-MM-DD HH:mm:ss" :disabled="isView" type="datetime"
          placeholder="请选择" />
      </el-form-item>
      <el-form-item label="供应商名称：" prop="supply_id">
        <el-select v-model="formData.supply_id" :disabled="isView"  placeholder="请选择">
          <el-option v-for="item in supplyList" :key="item.id" :label="item.unit_name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="接收种类：" prop="food_type">
        <el-input v-model="formData.food_type" :disabled="isView" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="接收数量：" prop="food_num">
        <el-input v-model.number="formData.food_num" :disabled="isView" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="相关照片：" prop="receive_img">
        <Upload v-if="!isView" :limit="6" v-model="formData.receive_img" />
        <template v-else>
          <div class="img-wrapper" v-for="(item, index) in imgsList" :key="index">
            <img object-fit="cover" :src="item" />
          </div>
        </template>
      </el-form-item>
      <el-divider />
      <el-form-item label="检查人员：" prop="check_user">
        <el-input v-model="formData.check_user" :disabled="isView" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="检查备注：" prop="check_remark">
        <el-input v-model="formData.check_remark" maxlength="200" placeholder="请输入" :disabled="isView" show-word-limit type="textarea"
          rows="4" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogShow = false">取消</el-button>
        <el-button type="primary" @click="onSubmit(formEl)" :loading="apiLoading">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { Plus } from "@element-plus/icons-vue";
import { onMounted, ref, reactive, computed, watch } from "vue"
import Upload from "@/views/components/widgets/Upload.vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
const { schoolId } = useAuthStore();
import { useRoute } from "vue-router";
const route = useRoute();
onMounted(() => { })
let baseUrl = '/api/back/schoolfoodsupply/receive'
let pageLoading = ref(false)
let dialogShow = ref(false)
let apiLoading = ref(false);
let dialogTitle = ref(false);
let formEl = ref(null)
let tableData = ref([])
let imgsList = ref([])
let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let tableSearch = reactive({
  start_date: '', //接收开始时间
  end_date: '', //接收结束时间
  status: '',
});
let uploadOptions = ref([
  {
    id: '0',
    name: '全部'
  },
  {
    id: '1',
    name: '已上传'
  },
  {
    id: '2',
    name: '未上传'
  },
])
let startendtime = computed({
  get() {
    return [tableSearch.start_date, tableSearch.end_date];
  },
  set(newValue) {
    [tableSearch.start_date, tableSearch.end_date] = [...newValue];
  },
});
const search = async () => {
  tablePage.page = 1;
  list();
}
const resetSearch = async () => {
  tableSearch.start_date = ''
  tableSearch.end_date = ''
  tableSearch.status = ''
  list();
}
const list = async () => {
  pageLoading.value = true;
  tableData.value = [];
  let param = ''
  for (let [k, v] of Object.entries(tableSearch)) {
    if (v !== '') {
      param += `${k}=${v}&`
    }
  }
  await api({
    url: `${baseUrl}list?${param}school_id=${schoolId}&per_page=${tablePage.per_page}&page=${tablePage.page}`,
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
}
const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};
let isView = ref(false)
const setForm = (info={}) => {
  for(let k of Object.keys(formData)){
    formData[k] = info[k] || ''
  }
}
const edit = async (info = {}, type) => {
  dialogTitle.value = type == 'view' ? '查看' : info?.id ? '修改食品检查信息' : '新增食品检查信息';
  isView.value = type == 'view' ? true : false;
  if (isView.value) {
    imgsList.value = info.receive_img.split(",");
  }
  setForm(info)
  dialogShow.value = true
  setTimeout(() => {
    formEl.value.clearValidate()
  }, 100)
}
const del = async (data) => {
  ElMessageBox.confirm(`确认删除该条记录吗？`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        method: "post",
        url: `${baseUrl}del`,
        data: {
          id: data.id
        },
      });
      if (res) {
        ElMessage.success("删除成功");
        list();
      }
    })
    .catch(() => { });
};
const handleUpload = async (row) => {
  let res = await api({
    url: `${baseUrl}upload`,
    data: {
      id: row.id,
      status: row.status
    },
    method: "post",
  })
  if (res) {
    list()
  }
};

let formData = reactive({
  id: "",
  supply_id: "",
  receive_time: "",
  food_type: "",
  food_num: null,
  receive_img: "",
  check_user: "",
  check_remark: "",
  check_img: "",
  status: null
})
let rules = reactive({
  receive_time: [{ required: true, message: '请选择接收时间', trigger: 'change' }],
  supply_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  food_type: [{ required: true, message: '请输入接收种类', trigger: 'blur' }],
  receive_img: [{ required: true, message: '请上传接收相关照片', trigger: 'change' }],
  check_user: [{ required: true, message: '请输入检查人员姓名', trigger: 'blur' }],
  check_img: [{ required: true, message: '请上传检查相关照片', trigger: 'change' }],
})
let supplyList = ref([])
const getSupplyList = async () => {
  let res = await api({ url: "/api/back/schoolfoodsupply/select/list?school_id=" + schoolId })
  if (res) {
    supplyList.value = res || [];
  }
}
const onSubmit = async (formEl) => {
  if (!formEl) return
  let valid = await formEl.validate((valid) => { })
  if (!valid) {
    return false;
  }

  let data = {};
  for (const key in formData) {
    data[key] = formData[key];
  }
  data.school_id = schoolId;
  apiLoading.value = true;
  let res = await api({
    url: `${baseUrl}${data.id ? 'edit' : 'add'}`,
    method: "post",
    data,
  });
  if (res) {
    ElMessage.success(data.id ? "修改成功" : "新增成功");
    await list();
    dialogShow.value = false;
  }
  dialogShow.value = false;
}
watch(
  () => route,
  (newVal) => {
    list()
    getSupplyList()
  },
  { immediate: true, deep: true }
);
</script>
<style lang="scss" scoped>
.img-wrapper {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  width: 100px;
  height: 100px;
  overflow: hidden;
  position: relative;
  margin-left: 10px;
  img {
    width: 100px;
    height: 100px;
  }
}
.img-wrapper:first-child {
  margin-left: 0;
}
</style>