<template>
  <div class="page" v-loading="pageLoading">
    <el-scrollbar class="page-table" v-loading="pageLoading">
      <div
        class="page-search"
        :class="seachOpen ? 'page-search-open' : 'page-search-close'"
      >
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="12" :md="12" :lg="12">
                <el-form-item label="生成时间：">
                  <el-date-picker
                    value-format="YYYY-MM-DD HH:mm:ss"
                    format="YYYY-MM-DD HH:mm:ss"
                    v-model="startendtime"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="请选择"
                    end-placeholder="请选择"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="合格情况：">
                  <el-select
                    v-model="tableSearch.check_status"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in qualifiedList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="整改情况">
                  <el-select
                    v-model="tableSearch.correction_status"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in correctionStatusList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="上传情况：">
                  <el-select
                    v-model="tableSearch.status"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in uploadStatusList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button
                type="primary"
                @click="seachOpen = true"
                link
                v-if="!seachOpen"
                >展开</el-button
              >
              <el-button
                type="primary"
                @click="seachOpen = false"
                link
                v-if="seachOpen"
                >收起</el-button
              >
            </el-form-item>
          </div>
        </el-form>
      </div>
      <el-table ref="table" :data="tableData">
        <el-table-column
          type="index"
          min-width="60"
          fixed="left"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="report_time"
          min-width="100"
          label="报告生成时间"
          align="center"
        ></el-table-column>

        <el-table-column min-width="100" label="上传情况" align="center">
          <template #default="scope">
            {{ scope.row.status === 0 ? "未上传" : "已上传" }}
          </template>
        </el-table-column>
        <el-table-column min-width="100" label="合格情况" align="center">
          <template #default="scope">
            {{
              scope.row.check_status === 0
                ? "不合格"
                : scope.row.check_status === 1
                ? "合格"
                : "—"
            }}
          </template>
        </el-table-column>
        <el-table-column min-width="100" label="整改情况" align="center">
          <template #default="scope">
            {{
              scope.row.correction_status === 0
                ? "未整改"
                : scope.row.correction_status === 1
                ? "已整改"
                : "—"
            }}
          </template>
        </el-table-column>
        <el-table-column width="200" fixed="right" align="center">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 0"
              type="primary"
              @click="edit(scope.row)"
              link
              >上传食品报告</el-button
            >
            <el-button
              v-else
              type="primary"
              @click="edit(scope.row, 'view')"
              link
              >查看食品报告</el-button
            >
            <el-button :disabled="scope.row.correction_status === 1" type="primary" @click="feedback(scope.row)" link
              >整改反馈</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :page-size="tablePage.per_page"
          background
          layout="prev, pager, next , sizes, total"
          :total="tablePage.total"
          v-model:current-page="tablePage.page"
          v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-scrollbar>
    <!-- 上传/查看食品报告 -->
    <el-dialog
      v-model="dialogShow"
      style="margin-bottom: 300px"
      :title="`${isView ? '查看' : '上传'}食品报告`"
      width="600px"
    >
      <el-form
        :model="formData"
        :rules="rules"
        label-position="top"
        ref="formEl"
      >
        <el-row :gutter="20">
          <el-col :sm="8" :md="8" :lg="8">
            <el-form-item label="报告生成时间：" prop="report_time">
              <el-date-picker
                v-model="formData.report_time"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="isView"
                type="datetime"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="20">
          <el-col>
            <h4>母乳喂养：</h4>
          </el-col>
          <el-col
            :sm="12"
            :md="12"
            :lg="12"
            v-for="(item, index) in formData.feedList"
            :key="index + 1"
          >
            <el-form-item
              label="婴幼儿姓名："
              :prop="`feedList.${index}.student_name`"
              :rules="rules.student_name"
            >
              <el-input
                v-model="item.student_name"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item
              label="哺乳时间："
              :prop="`feedList.${index}.feed_time`"
              :rules="rules.feed_time"
            >
              <el-date-picker
                v-model="item.feed_time"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="isView"
                type="datetime"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item
              label="检哺乳地点："
              :prop="`feedList.${index}.address`"
              :rules="rules.address"
            >
              <el-input
                v-model="item.address"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-divider></el-divider>
        </el-row>
        <el-row :gutter="20">
          <el-col>
            <h4>食谱信息：</h4>
          </el-col>
          <el-col :sm="12" :md="12" :lg="12">
            <el-form-item label="早餐：" prop="recipesList.breakfast">
              <el-input
                v-model="formData.recipesList.breakfast"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="12">
            <el-form-item label="中餐：" prop="recipesList.lunch">
              <el-input
                v-model="formData.recipesList.lunch"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="12">
            <el-form-item label="晚餐：" prop="recipesList.dinner">
              <el-input
                v-model="formData.recipesList.dinner"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="20">
          <el-col>
            <h4>自带食物信息：</h4>
          </el-col>
          <el-col
            :sm="12"
            :md="12"
            :lg="12"
            v-for="(item, index) in formData.selfList"
            :key="index + 1"
          >
            <el-form-item
              label="家长姓名："
              :prop="`selfList.${index}.parent_name`"
            >
              <el-input
                v-model="item.parent_name"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item
              label="婴幼儿姓名："
              :prop="`selfList.${index}.parent_name`"
            >
              <el-input
                v-model="item.student_name"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item
              label="班级："
              :prop="`selfList.${index}.parent_name`"
            >
              <el-input
                v-model="item.class_name"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item
              label="录入时间："
              :prop="`selfList.${index}.use_time`"
            >
              <el-input
                v-model="item.use_time"
                :disabled="isView"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item label="照片附件：">
              <div
                class="img-wrapper"
                v-for="item1 in item.img_url
                  ?.split(',')
                  .map((url) => url.trim())"
                :key="item1"
              >
                <img object-fit="cover" :src="item1" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button
            v-if="!isView"
            type="primary"
            @click="onSubmit(formEl)"
            :loading="apiLoading"
            >上报</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 整改反馈 -->
    <el-dialog
      v-model="feedbackDialogShow"
      style="margin-bottom: 300px"
      title="整改反馈"
      width="500px"
    >
      <el-form
        :model="feedbackFormData"
        :rules="feedbackRules"
        label-position="top"
        ref="feedbackFormEl"
      >
        <el-form-item label="是否合格">
          {{ feedbackFormData.check_status === 0 ? '不合格' : "合格" }}
        </el-form-item>
        <el-form-item label="限时整改">
          {{ feedbackFormData.correction_time || '无' }}
        </el-form-item>
        <el-form-item label="指导意见">
          {{ feedbackFormData.check_content || '无' }}
        </el-form-item>
        <el-form-item label="整改反馈" prop="correction_content">
          <el-input
            v-model="feedbackFormData.correction_content"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
            type="textarea"
            rows="4"
          />
        </el-form-item>
        <el-form-item label="相关照片" prop="correction_images">
          <Upload :limit="6" v-model="feedbackFormData.correction_images" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="feedbackDialogShow = false">取消</el-button>
          <el-button
            type="primary"
            @click="feedbackSubmit(feedbackFormEl)"
            :loading="apiLoading"
            >提交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch } from "vue"
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/auth-store";
import Upload from "@/views/components/widgets/Upload.vue";
import api from "@/axios.js";
const route = useRoute();
const { schoolId } = useAuthStore();
let baseUrl = '/api/back/foodreport'
let pageLoading = ref(false)
let seachOpen = ref(false);
let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let tableSearch = reactive({
  start_date: '', //接收开始时间
  end_date: '', //接收结束时间
  status: '',
  check_status: '',
  correction_status: '',
});
let startendtime = computed({
  get() {
    return [tableSearch.start_date, tableSearch.end_date];
  },
  set(newValue) {
    [tableSearch.start_date, tableSearch.end_date] = [...newValue];
  },
});
const qualifiedList = ref([
  {
    id: 0,
    name: '不合格'
  },
  {
    id: 1,
    name: '合格'
  }
])
const correctionStatusList = ref([
  {
    id: 0,
    name: '未整改'
  },
  {
    id: 1,
    name: '已整改'
  }
])
const uploadStatusList = ref([
  {
    id: 0,
    name: '未上传'
  },
  {
    id: 1,
    name: '已上传'
  }
])
let tableData = ref([])
const search = async () => {
  tablePage.page = 1;
  list();
};
const resetSearch = async () => {
  tableSearch.start_date = ''
  tableSearch.end_date = ''
  tableSearch.status = ''
  tableSearch.check_status = ''
  tableSearch.correction_status = ''
  tablePage.page = 1;
  list();
};
const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};
let dialogShow = ref(false);
let feedbackDialogShow = ref(false)
let apiLoading = ref(false);
let formEl = ref(null)
let feedbackFormEl = ref(null)
let formData = reactive({
  id: '',
  status: 0,
  report_time: "", // 报告时间
  feedList: [], // 喂养列表
  recipesList: [], // 食谱列表
  selfList: [], // 自带列表
})
let feedbackFormData = reactive({
  id: "",
  check_status: null,
  correction_time: "",
  check_content: "",
  correction_content: "",
  correction_images: ""
})
const rules = reactive({
  student_name: [
    { required: true, message: '请输入婴幼儿姓名', trigger: 'blur' }
  ],
  feed_time: [
    { required: true, message: '请选择哺乳时间', trigger: 'change' }
  ],
  address: [
    { required: true, message: '请输入哺乳地点', trigger: 'blur' }
  ]
})
const feedbackRules = reactive({
  correction_content: [
    { required: true, message: '请输入反馈内容', trigger: 'blur' }
  ]
})
const list = async () => {
  pageLoading.value = true;
  tableData.value = [];
  let param = ''
  for (let [k, v] of Object.entries(tableSearch)) {
    if (v !== '') {
      param += `${k}=${v}&`
    }
  }
  await api({
    url: `${baseUrl}/list?${param}school_id=${schoolId}&per_page=${tablePage.per_page}&page=${tablePage.page}`,
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
}
let isView = ref(false)
const setForm = (info = {}) => {
  for (let k of Object.keys(formData)) {
    formData[k] = info[k] || ''
  }
}
const edit = async (info, type) => {
  dialogShow.value = true
  isView.value = type == 'view' ? true : false
  let res = await api({
    url: `${baseUrl}/detail?id=${info.id}`,
  })
  if (res) {
    setForm(res)
    dialogShow.value = true
    setTimeout(() => {
      formEl.value.clearValidate()
    }, 100)
  }

}
const feedback = (info) => {
  feedbackDialogShow.value = true
  for (let k of Object.keys(feedbackFormData)) {
    feedbackFormData[k] = info[k] || ''
  }
}
const onSubmit = async (formEl) => {
  if (!formEl) return
  let valid = await formEl.validate((valid) => { })
  if (!valid) {
    return false;
  }

  let data = {
    id: formData.id,
    status: 1,
    // school_id: schoolId
  };
  apiLoading.value = true;
  let res = await api({
    url: `${baseUrl}/upload`,
    method: "post",
    data,
  });
  if (res) {
    ElMessage.success("上传成功");
    dialogShow.value = false;
    await list();
  }
  apiLoading.value = false;
}
const feedbackSubmit = async (feedbackFormEl) => {
  if (!feedbackFormEl) return
  let valid = await feedbackFormEl.validate((valid) => { })
  if (!valid) {
    return false;
  }
  let data = {
    id: feedbackFormData.id,
    correction_images: feedbackFormData.correction_images,
    correction_content: feedbackFormData.correction_content
  }
  apiLoading.value = true;
  let res = await api({
    url: `${baseUrl}/correction`,
    method: "post",
    data,
  });
  if (res) {
    ElMessage.success("反馈成功");
    await list();
    feedbackDialogShow.value = false;
  }
  apiLoading.value = false;
}
watch(
  () => route,
  (newVal) => {
    list()
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.img-wrapper {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  width: 100px;
  height: 100px;
  overflow: hidden;
  position: relative;
  margin-left: 10px;
  img {
    width: 100px;
    height: 100px;
  }
}
.img-wrapper:first-child {
  margin-left: 0;
}
</style>