<template>
  <div class="page" :class="{ 'tabs-page': !isSingleService, 'page-nobread': !breadStore.breadcrumbShow }"
    v-loading="pageLoading">
    <el-tabs v-show="isList" v-if="!isSingleService" v-model="schoolServiceId" style="height: auto;"
      @tab-click="onTabClick">
      <el-tab-pane :name="item.id" v-for="(item, index) in userServiceIds" :key="index">
        <template #label>
          <span class="flex-ct">
            <span>{{ item.name }}</span>
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-scrollbar class="page-table" v-show="isList">
      <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="职工名称：">
                  <el-input v-model="tableSearch.name" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="职工手机号：">
                  <el-input v-model="tableSearch.phone" maxlength="11" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="岗位名称：">
                  <el-select v-model="tableSearch.job_id" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option v-show="item.service_id === schoolServiceId" v-for="item in job_arr" :key="item.id"
                      :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="负责班级：">
                  <el-select v-model="tableSearch.class_id" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option v-show="item.service_id === schoolServiceId" v-for="item in class_arr" :key="item.id"
                      :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="16">
                <el-form-item label="入职时间：">
                  <el-date-picker value-format="YYYY-MM-DD" v-model="startendtime" type="daterange" range-separator="-"
                    start-placeholder="开始时间" end-placeholder="结束时间" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="12" :lg="8">
                <el-form-item label="职工状态：">
                  <el-select v-model="tableSearch.dimission_status" placeholder="请选择">
                    <el-option label="全部" value="" />
                    <el-option v-for="item in dimission_status" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
            <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt">
        <div class="left">
          <el-button type="primary" @click="add()">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <Plus />
            </el-icon>新增
          </el-button>
          <el-button type="primary" @click="importExcel">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <DocumentAdd />
            </el-icon>导入数据</el-button>

        </div>
        <div class="right">
          <el-icon @click="refresh" style="margin-right: 14px" class="click" size="18px">
            <RefreshRight />
          </el-icon>
        </div>
      </div>
      <el-table :scrollbar-always-on="true" table-layou="auto" ref="table" @sort-change="sortChange" :data="tableData">
        <!-- <el-table-column type="selection" width="40" /> -->
        <el-table-column prop="id" fixed="left" label="员工ID" min-width="80" />
        <el-table-column prop="name" fixed="left" label="姓名" min-width="100" />
        <el-table-column prop="job_id" label="岗位" min-width="120">
          <template #default="scope">
            {{ id2name(scope.row.job_id, job_arr, 'title') }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="所在园所">
          <template #default="scope">
            {{ schoolName }}
          </template>
        </el-table-column> -->
        <el-table-column label="负责班级" width="200">
          <template #default="scope">
            <el-popover placement="top" :width="150" trigger="hover">
              <template #reference>
                <p class="line-1">{{String(str2arr(scope.row.classids).map(e => {
                  return id2name(e, class_arr, 'title')
                }))}}</p>
              </template>
              <div style="padding: 10px;">{{String(str2arr(scope.row.classids).map(e => {
                return id2name(e, class_arr,
                  'title')
              }))}}</div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="join_date" sortable label="入职时间" min-width="110" />
        <el-table-column prop="phone" label="手机号" min-width="110">
          <template #default="scope">
            <hideNumber :number="scope.row.phone" />
          </template>
        </el-table-column>
        <el-table-column sortable prop="status" min-width="110" label="账号状态">
          <template #default="scope">
            {{ scope.row.status ? '开启' : '禁用' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" sortable min-width="110" label="职工状态">
          <template #default="scope">
            {{ id2name(scope.row.dimission_status, dimission_status) }}
            <el-popover trigger="hover" v-if="scope.row.dimission_status != 1">
              <el-descriptions :title="(scope.row.dimission_status == 0) ? '离职信息' : '试用信息'" :column="1"
                style="margin: 20px;">
                <el-descriptions-item v-if="scope.row.dimission_status == 0" label="离职时间：" label-align="left"
                  align="left" width="200px">{{ scope.row.dimission_time
                  }}</el-descriptions-item>
                <el-descriptions-item v-if="scope.row.dimission_status == 0" label="离职原因：" label-align="left"
                  align="left" width="200px">{{ scope.row.dimission_reason
                  }}</el-descriptions-item>
                <el-descriptions-item v-if="scope.row.dimission_status == 2" label="转正时间：" label-align="left"
                  align="left" width="200px">{{ scope.row.formal_date
                  }}</el-descriptions-item>
              </el-descriptions>
              <template #reference>
                <el-icon size="14" color="#409eff" style="transform: translateY(2px);">
                  <WarningFilled />
                </el-icon>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column width="200" fixed="right">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button type="primary" @click="edit(scope.row)" link>编辑</el-button>
            <el-button type="primary" @click="check(scope.row, 'detail')" link>查看</el-button>
            <el-dropdown>
              <el-button type="primary" link>更多<el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <el-button type="primary" @click="check(scope.row, 'contract')" link>合同</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.dimission_status == 1 || scope.row.dimission_status == 2">
                    <el-button type="primary" @click="quit(scope.row)" link>离职</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.dimission_status == 2">
                    <el-button type="primary" @click="formal(scope.row)" link>转正</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.dimission_status == 0">
                    <el-button type="primary" @click="entry(scope.row)" link>重新入职</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="danger" @click="del(scope.row)" link>删除</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
          :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-scrollbar>
    <el-scrollbar ref="scrollFormlEl" class="page-form back-page" v-show="!isList && !readonly">
      <el-page-header :icon="ArrowLeft" title="返回" @back="isList = true; breadStore.breadcrumbShow = true;">
        <template #content>
          <p class="b20 mb20 mt20">
            {{ formData.id == 0 ? "新增" : "编辑" }}{{ baseName }}
          </p>
        </template>
      </el-page-header>
      <el-form :model="formData" :rules="formDataRules" ref="formEl" :scroll-to-error="true" label-position="top">
        <p class="b16 mb20 mt20">基础信息</p>
        <el-row :gutter="20">
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="职工名称：" prop="name">
              <el-input v-model="formData.name" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="性别：" prop="sex">
              <el-radio-group v-model="formData.sex" size="large">
                <el-radio v-for="(item, index) in sex_arr" :key="index" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="出生日期：" prop="birth">
              <el-date-picker :disabled-date="disabledDate" value-format="YYYY-MM-DD" v-model="formData.birth"
                type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="身份证号：" prop="id_code">
              <el-input v-model="formData.id_code" maxlength="18" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="籍贯：" prop="birth_place">
              <el-input v-model="formData.birth_place" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="婚姻状况：" prop="marriage">
              <el-radio-group v-model="formData.marriage" size="large">
                <el-radio v-for="(item, index) in marriage" :key="index" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="毕业院校：" prop="graduate_school">
              <el-input v-model="formData.graduate_school" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="学历：" prop="education">
              <el-select v-model="formData.education" placeholder="请选择">
                <el-option v-for="item in education" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="所学专业：" prop="major">
              <el-input v-model="formData.major" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="从业开始时间：" prop="job_start">
              <el-date-picker value-format="YYYY-MM-DD" v-model="formData.job_start" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="头像：" prop="img_url">
              <el-upload accept="image/*" @click="uploadKey = 'img_url'" :auto-upload="false" class="avatar-uploader"
                :limit="1" :on-exceed="exceed" :on-change="imageUpload" :show-file-list="false">
                <img v-if="formData.img_url" object-fit="contain" :src="formData.img_url" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon">
                  <Plus />
                </el-icon>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <p class="b16 mb20 mt20">任职信息</p>
        <el-row :gutter="20">
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="入职园所：" prop="school_id">
              <el-input disabled v-model="schoolName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8" v-if="!isSingleService">
            <el-form-item label="业务范围：" prop="service_ids">
              <!-- 如果之前是双业务，需要展示 -->
              <el-select :disabled="isSingleService" v-model="formData.service_ids" multiple placeholder="请选择"
                @change="formData.job_id = ''; formData.role_ids = []; formData.classids = [];">
                <el-option :disabled="!userServiceIds.filter(e => { return e.id == item.id }).length"
                  v-for="item in schoolServiceIds" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="职工岗位：" prop="job_id">
              <el-select v-model="formData.job_id" placeholder="请选择">
                <el-option
                  v-for="item in (job_arr.filter(e => { return formData.service_ids.indexOf(e.service_id) != -1 }))"
                  :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="职工角色：" prop="role_ids">
              <el-select v-model="formData.role_ids" multiple placeholder="请选择">
                <el-option
                  v-for="item in (role_arr.filter(e => { return formData.service_ids.indexOf(e.service_id) != -1 }))"
                  :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="负责班级：" prop="classids">
              <el-select v-model="formData.classids" multiple placeholder="请选择">
                <el-option
                  v-for="item in class_arr.filter(e => { return (formData.service_ids.indexOf(e.service_id) != -1 && e.status == 1) })"
                  :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="教师简介" prop="remark">
              <el-input v-model="formData.remark" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="是否有试用期：" prop="is_trial">
              <el-radio-group v-model="formData.is_trial" size="large">
                <el-radio v-for="(item, index) in have_arr" :key="index" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="入职时间：" prop="join_date" width="">
              <el-date-picker value-format="YYYY-MM-DD" v-model="formData.join_date" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8" v-if="formData.is_trial == 1">
            <el-form-item label="试用期转正时间：" prop="formal_date">
              <el-date-picker value-format="YYYY-MM-DD" v-model="formData.formal_date" type="date" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="是否参保：" prop="is_security">
              <el-radio-group v-model="formData.is_security" size="large">
                <el-radio v-for="(item, index) in true_arr" :key="index" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8" v-show="formData.is_security == 1">
            <el-form-item label="参保时间：" prop="security_date">
              <el-date-picker value-format="YYYY-MM-DD" v-model="formData.security_date" type="date"
                placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8" v-show="formData.is_security == 0">
            <el-form-item label="未参保原因：" prop="security_reason">
              <el-input v-model="formData.security_reason" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <!-- <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="账号状态：" prop="status">
              <el-switch v-model="formData.status" inline-prompt size="large" active-text="开启" inactive-text="禁用"
                :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col> -->
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="手机号（账号）：" prop="phone">
              <el-input v-model.number="formData.phone" maxlength="11" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="密码：" prop="password" v-if="formData.id == 0">
              <el-input v-model="formData.password" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <p class="b16 mb20 mt20">联系信息</p>
        <el-row :gutter="20">
          <el-col :sm="8" :md="8" :lg="8">
            <el-form-item label="省份" prop="province">
              <el-select @change="getCity" :popper-append-to-body="false" v-model="formData.province"
                placeholder="请选择省份">
                <el-option v-for="(item, index) in provinceList" :key="index" :label="item.text"
                  :value="item.code * 1">{{
                    item.text }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="8" :md="8" :lg="8">
            <el-form-item label="城市" prop="city">
              <el-select @change="getArea" :popper-append-to-body="false" v-model="formData.city" placeholder="请选择城市">
                <el-option v-for="(item, index) in cityList" :key="index" :label="item.text" :value="item.code * 1">{{
                  item.text }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="8" :md="8" :lg="8">
            <el-form-item label="区/县" prop="area">
              <el-select :popper-append-to-body="false" v-model="formData.area" placeholder="请选择区/县">
                <el-option v-for="(item, index) in areaList" :key="index" :label="item.text" :value="item.code * 1">{{
                  item.text }}</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="家庭住址：" prop="address">
              <el-input v-model="formData.address" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="紧急联系人：" prop="emergency_contact">
              <el-input v-model="formData.emergency_contact" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="紧急联系人电话：" prop="emergency_tel">
              <el-input v-model="formData.emergency_tel" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="邮箱：" prop="email">
              <el-input v-model="formData.email" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="微信：" prop="weixin">
              <el-input v-model="formData.weixin" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="QQ：" prop="qq">
              <el-input v-model="formData.qq" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <p class="b16 mb20 mt20">资格证书</p>
        <el-row :gutter="20">
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="教师资格证：" prop="ntcec">
              <el-radio-group v-model="formData.ntcec" size="large">
                <el-radio v-for="(item, index) in have_arr" :key="index" :label="item.id">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :sm="24" :md="24" :lg="12" v-show="formData.ntcec == 1">
            <el-form-item label="教师资格证上传：" prop="ntcec_url">
              <el-upload @click="uploadKey = 'ntcec_url'" :limit="1" :on-exceed="exceed" :on-change="imageUpload"
                ref="ntcec_url" :auto-upload="false" :show-file-list="false">
                <template #trigger>
                  <el-button>上传文件</el-button>
                </template>
                <div v-if="formData.ntcec_url" class="flex-bt" style="margin: 12px 0 0">
                  <FileBox name="教师资格证" :url="formData.ntcec_url" />
                  <el-icon style="cursor: pointer" @click="formData.ntcec_url = ''">
                    <Close />
                  </el-icon>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    建议上传文件格式：jpeg、jpg、png 、pdf
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="24" :lg="12">
            <el-form-item label="毕业证上传：" prop="education_url">
              <el-upload @click="uploadKey = 'education_url'" :limit="1" :on-exceed="exceed" :on-change="imageUpload"
                ref="ntcec_url" :auto-upload="false" :show-file-list="false">
                <template #trigger>
                  <el-button>上传文件</el-button>
                </template>
                <div v-if="formData.education_url" class="flex-bt" style="margin: 12px 0 0">
                  <FileBox name="毕业证" :url="formData.education_url" />
                  <el-icon style="cursor: pointer" @click="formData.education_url = ''">
                    <Close />
                  </el-icon>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    建议上传文件格式：jpeg、jpg、png 、pdf
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="24" :lg="12">
            <el-form-item label="健康证上传：" prop="healthy_url">
              <el-upload @click="uploadKey = 'healthy_url'" :limit="1" :on-exceed="exceed" :on-change="imageUpload"
                ref="ntcec_url" :auto-upload="false" :show-file-list="false">
                <template #trigger>
                  <el-button>上传文件</el-button>
                </template>
                <div v-if="formData.healthy_url" class="flex-bt" style="margin: 12px 0 0">
                  <FileBox name="健康证" :url="formData.healthy_url" />
                  <el-icon style="cursor: pointer" @click="formData.healthy_url = ''">
                    <Close />
                  </el-icon>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    建议上传文件格式：jpeg、jpg、png 、pdf
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <p class="b16 mb20 mt20">所获得证书情况</p>
        <el-row :gutter="20" style="padding-bottom: 100px;">
          <el-col :sm="6" :md="6" :lg="6">
            <el-form-item label="是否持有保育师/员证书：" prop="is_nurse_auth">
              <el-select v-model="formData.is_nurse_auth" style="height:34px" placeholder="请选择"
                @change="formData.nurse_auth_num = ''">
                <el-option v-for="item in certOption" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否持有职业等级证书：" prop="is_cpq_auth">
              <el-select v-model="formData.is_cpq_auth" style="height:34px" placeholder="请选择"
                @change="formData.cpq_auth_num = ''">
                <el-option v-for="item in certOption" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="是否持有机构负责人证书：" prop="is_superintendent_auth">
              <el-select v-model="formData.is_superintendent_auth" style="height:34px" placeholder="请选择"
                @change="formData.superintendent_auth_num = ''">
                <el-option v-for="item in certOption" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="是否持有育婴员资格证书：" prop="is_cettic_auth">
              <el-select v-model="formData.is_cettic_auth" style="height:34px" placeholder="请选择"
                @change="formData.cettic_auth_num = ''">
                <el-option v-for="item in certOption" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否持有保安员证书：" prop="is_security_auth">
              <el-select v-model="formData.is_security_auth" style="height:34px" placeholder="请选择"
                @change="formData.security_auth_num = ''">
                <el-option v-for="item in certOption" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="6" :md="6" :lg="6">
            <el-form-item label="保育师/员证书编号：" prop="nurse_auth_num">
              <el-input v-model="formData.nurse_auth_num" placeholder="请输入">
              </el-input>
            </el-form-item>
            <el-form-item label="职业等级证书编号：" prop="cpq_auth_num">
              <el-input v-model="formData.cpq_auth_num" placeholder="请输入">
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="机构负责人证书编号：" prop="superintendent_auth_num">
              <el-input v-model="formData.superintendent_auth_num" placeholder="请输入">
              </el-input>
            </el-form-item> -->
            <el-form-item label="育婴员资格证书编号：" prop="cettic_auth_num">
              <el-input v-model="formData.cettic_auth_num" placeholder="请输入">
              </el-input>
            </el-form-item>
            <el-form-item label="保安员证书编号：" prop="security_auth_num">
              <el-input v-model="formData.security_auth_num" placeholder="请输入">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="6" :md="6" :lg="6">
            <el-form-item label="保育师/员证书获取时间：" prop="nurse_date">
              <el-date-picker v-model="formData.nurse_date" placeholder="请选择" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="职业等级证书获取时间：" prop="cpq_date">
              <el-date-picker v-model="formData.cpq_date" placeholder="请选择" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD">
              </el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="机构负责人证书获取时间：" prop="superintendent_date">
              <el-date-picker v-model="formData.superintendent_date" placeholder="请选择" format="YYYY-MM-DD" value-format="YYYY-MM-DD">
              </el-date-picker>
            </el-form-item> -->
            <el-form-item label="育婴员资格证书获取时间：" prop="cettic_date">
              <el-date-picker v-model="formData.cettic_date" placeholder="请选择" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="保安员证书获取时间：" prop="security_adddate">
              <el-date-picker v-model="formData.security_adddate" placeholder="请选择" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :sm="6" :md="6" :lg="6">
            <el-form-item label="保育师/员证书等级：" prop="nurse_grade">
              <el-select v-model="formData.nurse_grade" placeholder="请输入">
                <el-option :value="1" label="初"></el-option>
                <el-option :value="2" label="中"></el-option>
                <el-option :value="3" label="高"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="职业等级证书等级：" prop="cpq_grade">
              <el-select v-model="formData.cpq_grade" placeholder="请输入">
                <el-option :value="1" label="初"></el-option>
                <el-option :value="2" label="中"></el-option>
                <el-option :value="3" label="高"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="机构负责人证书等级：" prop="superintendent_grade">
              <el-select v-model="formData.superintendent_grade" placeholder="请输入">
                <el-option :value="1" label="初"></el-option>
                <el-option :value="2" label="中"></el-option>
                <el-option :value="3" label="高"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="育婴员资格证书等级：" prop="cettic_grade">
              <el-select v-model="formData.cettic_grade" placeholder="请输入">
                <el-option :value="1" label="初"></el-option>
                <el-option :value="2" label="中"></el-option>
                <el-option :value="3" label="高"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="保安员证书等级：" prop="security_grade">
              <el-select v-model="formData.security_grade" placeholder="请输入">
                <el-option :value="1" label="初"></el-option>
                <el-option :value="2" label="中"></el-option>
                <el-option :value="3" label="高"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <p class="b16 mb20 mt20">其他信息</p>
        <el-row :gutter="20" style="padding-bottom: 100px;">
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="其他备注：" prop="other_info">
              <el-input :rows="3" type="textarea" v-model="formData.other_info" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="12" :lg="8">
            <el-form-item label="考勤卡号：" prop="cards">
              <el-input style="margin-bottom: 12px;" v-for="(item, index) in cards" :key="index" v-model="item.number"
                placeholder="请输入">
                <!-- <template #append>
                  <el-button v-if="index == 0" :icon="Plus" @click="cards.push({ 'number': '', 'id': 0 });">新增</el-button>
                  <el-button v-else :icon="Close"
                    @click="cards.splice(index, 1); cards_delids.push(item.id)">删除</el-button>
                </template> -->
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="fixed-btn">
        <el-button type="primary" @click="onSubmit(formEl)" :loading="apiLoading">保存</el-button>
      </div>
    </el-scrollbar>
    <template v-if="readonly && !isList">
      <el-page-header style="padding: 0 24px" :icon="ArrowLeft" title="返回"
        @back="isList = true; breadStore.breadcrumbShow = true;">
        <template #content>
          <p class="b20 mb20 mt20">教职工详情</p>
        </template>
      </el-page-header>
      <div class="user-main">
        <div class="avatar">
          <img style="object-fit: cover;" v-if="formData.img_url" :src="formData.img_url" />
          <div v-else class="img">{{ formData.name ? formData.name.substr(0, 1) : '无' }}</div>
        </div>
        <div class="user-content">
          <h2>{{ formData.name }}</h2>
          <el-row :gutter="20">
            <el-col class="line-1" :sm="6" :md="6" :lg="6">
              <span class="label">园所信息：</span>
              <span class="value">{{ schoolName }}</span>
            </el-col>
            <el-col class="line-1" :sm="6" :md="6" :lg="6">
              <span class="label">业务范围：</span>
              <span class="value">{{String(str2arr(formData.service_ids).map(e => {
                return id2name(e, userServiceIds)
              }))
              }}</span>
            </el-col>
            <el-col class="line-1" :sm="6" :md="6" :lg="6">
              <span class="label">负责班级：</span>
              <span class="value">
                <el-popover placement="top-start" :width="200" trigger="hover">
                  <template #reference>
                    {{String(str2arr(formData.classids).map(e => {
                      return id2name(e, class_arr,
                        'title')
                    }))}}
                  </template>
                  <div style="padding: 10px;">{{String(str2arr(formData.classids).map(e => {
                    return id2name(e, class_arr,
                      'title')
                  }))}}</div>
                </el-popover>
              </span>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col class="line-1" :sm="6" :md="6" :lg="6">
              <span class="label">入职时间：</span>
              <span class="value">{{ formData.join_date || '-' }}</span>
            </el-col>
            <el-col class="line-1" :sm="6" :md="6" :lg="6">
              <span class="label">转正日期：</span>
              <span class="value">{{ formData.formal_date || '-' }}</span>
            </el-col>
            <el-col class="line-1" :sm="6" :md="6" :lg="6" v-if="formData.dimission_status == 0">
              <span class="label">离职日期：</span>
              <span class="value">{{ formData.dimission_time || '-' }}
                <el-popover trigger="hover" v-if="formData.dimission_status == 0">
                  <el-descriptions :column="1" style="margin: 20px;">
                    <el-descriptions-item label="离职原因：" label-align="left" align="left" width="200px">{{
                      formData.dimission_reason
                    }}</el-descriptions-item>
                  </el-descriptions>
                  <template #reference>
                    <el-button type="primary" link>原因</el-button>
                  </template>
                </el-popover>
              </span>
            </el-col>
            <el-col class="line-1" :sm="6" :md="6" :lg="6">
              <span class="label">参保状态：</span>
              <span class="value">{{ formData.is_security ? formData.security_date : '未参保' }}</span>
            </el-col>
          </el-row>
        </div>
        <div class="user-handle">
          <div class="button">
            <el-button v-if="formData.dimission_status != 0" @click="quit(formData)">离职</el-button>
            <el-button v-if="formData.dimission_status == 0" @click="entry(formData)">入职</el-button>
            <el-button v-if="formData.dimission_status == 2" @click="formal(formData)">转正</el-button>
            <el-button type="primary" @click="readonly = false">编辑</el-button>
          </div>
          <div class="status">
            <div class="item">
              <p>账号状态</p>
              <b>{{ id2name(formData.dimission_status, dimission_status) }}</b>
            </div>
            <div class="item">
              <p>状态</p>
              <b>{{ formData.status ? '开启' : '禁用' }}</b>
            </div>
            <div class="item">
              <p>职工岗位</p>
              <b>{{ id2name(formData.job_id, job_arr, 'title') }}</b>
            </div>
          </div>
        </div>
      </div>
      <div class="tabs-page">
        <el-tabs class="check-content" v-model="checkTabVal" style="height: auto;">
          <el-tab-pane label="详细情况" name="detail">
            <Detail :fulladdress="fulladdress" :formData="formData" :cards="cards"></Detail>
          </el-tab-pane>
          <el-tab-pane label="合同信息" name="contract">
            <Contract :id="formData.id"></Contract>
          </el-tab-pane>
          <el-tab-pane label="培训记录" name="train">
            <Train :id="formData.id" :phone="formData.phone"></Train>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
    <el-dialog v-model="leaveVisible" title="员工离职" width="500px">
      <el-form :model="formData" :rules="formDataRules" ref="lform" label-position="top">
        <p class="dialog-p">园所名称：<span>{{ schoolName }}</span></p>
        <p class="dialog-p">员工姓名：<span>{{ formData.name }}</span></p>
        <p class="dialog-p">岗位名称：<span>{{ id2name(formData.job_id, job_arr, 'title') }}</span></p>
        <p class="dialog-p">入职时间：<span>{{ formData.join_date }}</span></p>
        <el-form-item label="离职时间：" prop="dimission_time">
          <el-date-picker value-format="YYYY-MM-DD" v-model="formData.dimission_time" type="date" placeholder="请选择" />
        </el-form-item>
        <el-form-item label="离职原因：" prop="dimission_reason">
          <el-input :rows="3" type="textarea" v-model="formData.dimission_reason" placeholder="请输入" />
        </el-form-item>
        <!-- <el-form-item label="账号状态：" prop="status">
          <el-switch v-model="formData.status" inline-prompt size="large" active-text="开启" inactive-text="禁用"
            :active-value="1" :inactive-value="0" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="leaveVisible = 0">取消</el-button>
          <el-button type="primary" @click="leaveSubmit(lform)" :loading="apiLoading">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="entryVisible" :title="!formalVisible ? '员工入职' : '员工转正'" width="500px">
      <el-form :model="formData" label-position="top">
        <el-form-item label="员工姓名：">
          <el-input disabled v-model="formData.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="!formalVisible" label="是否有试用期：" prop="is_trial">
          <el-radio-group v-model="formData.is_trial" size="large">
            <el-radio v-for="(item, index) in have_arr" :key="index" :label="item.id">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!formalVisible" label="入职时间：" prop="join_date">
          <el-date-picker value-format="YYYY-MM-DD" v-model="formData.join_date" type="date" placeholder="请选择" />
        </el-form-item>
        <el-form-item v-if="formData.is_trial == 1" label="试用期转正时间：" prop="formal_date">
          <el-date-picker value-format="YYYY-MM-DD" v-model="formData.formal_date" type="date" placeholder="请选择" />
        </el-form-item>
        <el-form-item label="业务范围：" prop="service_ids">
          <el-select v-model="formData.service_ids" multiple placeholder="请选择"
            @change="formData.job_id = ''; formData.role_ids = [];">
            <el-option v-for="item in userServiceIds" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="职工岗位：" prop="job_id">
          <el-select v-model="formData.job_id" placeholder="请选择">
            <el-option v-show="formData.service_ids.indexOf(item.service_id) != -1" v-for="item in job_arr"
              :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="职工角色：" prop="role_ids">
          <el-select v-model="formData.role_ids" multiple placeholder="请选择">
            <el-option v-show="formData.service_ids.indexOf(item.service_id) != -1" v-for="item in role_arr"
              :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责班级：" prop="classids">
          <el-select v-model="formData.classids" multiple placeholder="请选择">
            <el-option
              v-for="item in class_arr.filter(e => { return (formData.service_ids.indexOf(e.service_id) != -1 && e.status == 1) })"
              :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!formalVisible" label="考勤卡号：" prop="cards">
          <el-input style="margin-bottom: 12px;" v-for="(item, index) in cards" :key="index" v-model="item.number"
            placeholder="请输入">
            <!-- <template #append>
              <el-button v-if="index == 0" :icon="Plus" @click="cards.push({ 'number': '', 'id': 0 });">新增</el-button>
              <el-button v-else :icon="Close" @click="cards.splice(index, 1); cards_delids.push(item.id)">删除</el-button>
            </template> -->
          </el-input>
        </el-form-item>
        <!-- <el-form-item v-if="!formalVisible" label="账号状态：" prop="status">
          <el-switch v-model="formData.status" inline-prompt size="large" active-text="开启" inactive-text="禁用"
            :active-value="1" :inactive-value="0" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="entryVisible = false">取消</el-button>
          <el-button type="primary" @click="formalVisible ? formalSubmit() : entrySubmit()"
            :loading="apiLoading">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="importVisible" title="导入数据" width="500px">
      <div class="importcontent">
        <div class="down">
          <div class="t1">下载导入模板，根据模板提示完善内容</div>
          <el-button @click="downModel"><el-icon>
              <Download />
            </el-icon>下载模板</el-button>
        </div>
        <div class="down up">
          <div class="t1">上传完善好的内容，支持上传文件为xls,xlsx</div>
          <el-upload v-model:file-list="excel_arr" :limit="1" v-if="importVisible" :on-exceed="uploadExceed"
            :on-change="uploadChange" :auto-upload="false" class="upload-demo" drag accept=".xlsx, .xls">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖动文件或<em>点击上传</em>
            </div>
          </el-upload>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadSubmit" :loading="apiLoading">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  Plus,
  ArrowDown,
  RefreshRight,
  ArrowLeft,
  WarningFilled,
  Close,
  Download,
  DocumentAdd,
  UploadFilled,
} from "@element-plus/icons-vue";
import jsFileDownload from "js-file-download";
import { dayjs } from 'element-plus'
import { watch, onMounted, reactive, ref, nextTick, computed, toRaw } from "vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
import { useConfigStore } from "@/stores/config-store";
import { useBreadStore } from '@/stores/bread-store';
import { useRoute } from "vue-router";
import { str2arr, class_status, id2name, dimission_status, sex_arr, education, marriage, have_arr, true_arr, account_status } from "@/config/select";
import FileBox from '@/views/components/uploadfile.vue';
import { checkValue } from "@/assets/utils/index.js";
import { upload } from "@/assets/utils/obs.js";
import Detail from './components/detail.vue';
import Contract from './components/contract.vue';
import Train from './components/train.vue';
import logo from "@/assets/images/login_logo.png";
import hideNumber from "@/views/components/hideNumber.vue";

const breadStore = useBreadStore();
const { provinceList, getCityApi, getAreaApi } = useConfigStore();
const route = useRoute();
const { organizations_id, identity_id } = useAuthStore().userInfo;
const { isSuperAdmin, schoolId, schoolName, userServiceIds, schoolServiceIds, isSingleService } = useAuthStore();
const schoolData = useAuthStore().userSchool;

let certOption = [{
  label: '是',
  value: 1
}, {
  label: '无',
  value: 0
}]

let baseUrl = "";
let baseName = "教职工";
let scrollFormlEl = ref(null);
let formEl = ref(null);
let checkTabVal = ref('detail');
let schoolServiceId = ref(userServiceIds.length ? userServiceIds[0].id : 1);
let seachOpen = ref(false);
let isList = ref(true);
let pageLoading = ref(true);
let apiLoading = ref(false);
let readonly = ref(false);
let areaList = ref([]);
let cityList = ref([]);
let class_arr = ref([]);
let job_arr = ref([]);
let role_arr = ref([]);
let uploadKey = ref('');
let cards = ref([]); // 考勤机字段特殊处理
let cards_delids = ref([]); // 考勤机字段特殊处理
let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let lform = ref('');
let formData = reactive({
  school_id: schoolData.id,
  id: 0,
  user_id: 0,
  name: "",
  phone: "",
  sex: "",
  img_url: "",
  classids: [],
  job_id: "",
  role_ids: [],
  service_ids: [],
  remark: '',
  status: 1,
  dimission_status: 1, //职工状态0离职 1在职 2试用
  dimission_time: '', //离职时间
  dimission_reason: '',
  is_trial: 0,//是否有试用期0无1有
  join_date: '',//入职时间/试用开始时间 Y-m-d
  formal_date: '',//试用到期时间/转正时间 Y-m-d
  birth: '',//
  province: '',
  city: '',
  area: '',
  address: '',
  id_code: '',//身份证号
  birth_place: '',//籍贯
  marriage: '',//婚姻状况0未婚 1已婚
  education: '',//学历0默认 1小学 2初中 3高中 4大专5本科 6硕士7硕士以上
  major: '', //专业
  graduate_school: '',//毕业学校 
  job_start: '',//从业开始时间 Y-m-d
  education_info: '',//更多学历信息
  qq: '', //qq
  weixin: '', //微信号
  email: '',//
  emergency_contact: '',//紧急联系人
  emergency_tel: '',//紧急联系人电话
  ntcec: 0,//是否有教师资格证0无 1有 默认0
  ntcec_url: '', //教师资格证url
  education_url: '', //教师资格证url
  healthy_url: '', //健康证url
  other_info: '',//其他备注
  is_security: 0,//是否参保0未1
  security_date: '',//参保日期 Y-m-d
  security_reason: '',//未参保原因
  password: '',

  is_nurse_auth: '',//是否持有保育师/员证书
  nurse_auth_num: '',//保育师/员证书编号
  is_cpq_auth: '', //是否持有职业等级证书
  cpq_auth_num: '',//职业等级证书编号
  is_superintendent_auth: '',//是否持有机构负责人证书
  superintendent_auth_num: '',//机构负责人证书编号
  is_cettic_auth: '',//是否持有育婴员资格证书
  cettic_auth_num: '',//育婴员资格证书编号
  is_security_auth: '',//是否持有保安员证书
  security_auth_num: '',//保安员证书编号

  nurse_date: '',
  cpq_date: '',
  superintendent_date: '',
  cettic_date: '',
  security_adddate: '',

  nurse_grade: '',
  cpq_grade: '',
  superintendent_grade: '',
  cettic_grade: '',
  security_grade: '',
});
let formDataModel = JSON.parse(JSON.stringify(formData));
let leaveVisible = ref(false);
let entryVisible = ref(false);
let formalVisible = ref(false);
let formDataRules = {
  password: [
    { required: true, message: '请填写密码', trigger: 'blur' },
  ],
  id_code: [
    {
      validator: (rule, value, callback) => {
        if (value && !checkValue(value, 'idcard')) {
          callback(new Error('请输入正确的身份证号'))
        } else {
          callback()
        }
      }, trigger: 'blur'
    },
  ],
  phone: [
    { required: true, message: '请填写手机号', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && !checkValue(value, 'phone')) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      }, trigger: 'blur'
    },
  ],
  province: [
    { required: true, message: '请选择省', trigger: 'change' },
  ],
  city: [
    { required: true, message: '请选择市', trigger: 'change' },
  ],
  area: [
    { required: true, message: '请选择区', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请填写教师名称', trigger: 'blur' },
  ],
  birth: [
    { required: true, message: '请选择出生日期', trigger: 'blur' },
  ],
  marriage: [
    { required: true, message: '请选择婚姻状况', trigger: 'change' },
  ],
  sex: [
    { required: true, message: '请选择性别', trigger: 'change' },
  ],
  job_id: [
    { required: true, message: '请选择岗位', trigger: 'change' },
  ],
  role_ids: [
    { type: 'array', required: true, message: '请选择角色', trigger: 'change' },
  ],
  service_ids: [
    { type: 'array', required: true, message: '请选择服务业务', trigger: 'change' },
  ],
  is_trial: [
    { type: 'number', required: true, message: '请选择是否有试用期', trigger: 'change' }
  ],
  join_date: [
    { type: 'date', required: true, message: '请选择入职时间', trigger: 'change' }
  ],
  formal_date: [
    { type: 'date', required: true, message: '请选择试用期转正时间', trigger: 'change' }
  ],
  is_security: [
    { type: 'number', required: true, message: '请选择是否参保', trigger: 'change' }
  ],
  education: [
    { required: true, message: '请选择学历', trigger: 'change' }
  ],
  ntcec: [
    { required: true, message: '请选择是否有教师资格证', trigger: 'change' }
  ],

  is_nurse_auth: [
    { required: true, message: '请选择是否持有保育师/员证书', trigger: 'change' }
  ],
  nurse_auth_num: [
    {
      validator: (rule, value, callback) => {
        if (!value && formData.is_nurse_auth) {
          callback(new Error('请输入保育师/员证书编号'))
        } else {
          callback()
        }
      }, trigger: 'change'
    },
  ],
  is_cpq_auth: [
    { required: true, message: '请选择是否持有职业等级证书', trigger: 'change' }
  ],
  cpq_auth_num: [
    {
      validator: (rule, value, callback) => {
        if (!value && formData.is_cpq_auth) {
          callback(new Error('请输入职业等级证书编号'))
        } else {
          callback()
        }
      }, trigger: 'change'
    },
  ],
  is_superintendent_auth: [
    { required: true, message: '请选择是否持有机构负责人证书', trigger: 'change' }
  ],
  superintendent_auth_num: [
    {
      validator: (rule, value, callback) => {
        if (!value && formData.is_superintendent_auth) {
          callback(new Error('请输入机构负责人证书编号'))
        } else {
          callback()
        }
      }, trigger: 'change'
    },
  ],
  is_cettic_auth: [
    { required: true, message: '请选择是否持有育婴员资格证书', trigger: 'change' }
  ],
  cettic_auth_num: [
    {
      validator: (rule, value, callback) => {
        if (!value && formData.is_cettic_auth) {
          callback(new Error('请输入育婴员资格证书编号'))
        } else {
          callback()
        }
      }, trigger: 'change'
    },
  ],
  is_security_auth: [
    { required: true, message: '请选择是否持有保安员证书', trigger: 'change' }
  ],
  security_auth_num: [
    {
      validator: (rule, value, callback) => {
        if (!value && formData.is_security_auth) {
          callback(new Error('请输入保安员证书编号'))
        } else {
          callback()
        }
      }, trigger: 'change'
    },
  ],
  dimission_reason: [
    { required: true, message: '请输入离职原因', trigger: 'change' }
  ],
  dimission_time: [
    { required: true, message: '请选择离职时间', trigger: 'change' }
  ],
}
let tableSearch = reactive({
  school_id: "",
  name: "",
  phone: '',
  dimission_status: '',
  job_id: '',
  status: "",
  join_start: "",
  join_end: "",
  class_id: "",
  orderkey: "",
  ordersort: ""
});
let fulladdress = ref('');
let tableData = ref([]);
let startendtime = computed({
  get() {
    return [tableSearch.start_date, tableSearch.end_date];
  },
  set(newValue) {
    if (newValue === null) {
      [tableSearch.start_date, tableSearch.end_date] = ['', ''];
    } else {
      [tableSearch.start_date, tableSearch.end_date] = [...newValue];
    }
  },
});

function exceed(files) {
  imageUpload({ raw: files[0] });
}

async function imageUpload(file) {
  let rawFile = file.raw;
  if (uploadKey.value == 'img_url' && rawFile.type !== "image/jpeg" && rawFile.type !== "image/jpg" &&
    rawFile.type !== "image/png") {
    ElMessage.error("文件格式必须为：jpeg、jpg、png");
    return false;
  } else if (uploadKey.value != 'img_url' && rawFile.type !== "image/jpeg" && rawFile.type !== "image/jpg" &&
    rawFile.type !== "image/png" && rawFile.type !== "application/pdf") {
    ElMessage.error("文件格式必须为：jpeg、jpg、png 、pdf");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error("文件大小不可超过10M");
    return false;
  }
  pageLoading.value = true;
  formData[uploadKey.value] = await upload(rawFile, `staff/${formData.id}`);
  pageLoading.value = false;
}

const setFormData = async (data = {}) => {
  formData.id = data.id || 0;
  formData.user_id = data.user_id || 0;
  formData.name = data.name || "";
  formData.phone = data.phone || "";
  formData.sex = data.sex || "";
  formData.img_url = data.img_url || "";
  formData.classids = str2arr(data.classids, true) || [];
  formData.job_id = data.job_id || "";
  formData.role_ids = str2arr(data.role_ids, true) || [];
  formData.service_ids = str2arr(data.service_ids, true).length ? str2arr(data.service_ids, true) : (isSingleService ? [userServiceIds[0].id] : []);
  formData.remark = data.remark || '';
  formData.status = data.status || 1;
  formData.dimission_status = data.dimission_status === '' ? '' : data.dimission_status; //职工状态0离职 1在职 2试用
  formData.dimission_time = data.dimission_time || ''; //离职时间
  formData.dimission_reason = data.dimission_reason || '';
  formData.is_trial = data.is_trial || 0;//是否有试用期0无1有
  formData.join_date = data.join_date || '';//入职时间/试用开始时间 Y-m-d
  formData.formal_date = data.formal_date || '';//试用到期时间/转正时间 Y-m-d
  formData.birth = data.birth || '';//
  formData.province = data.province || '';
  formData.city = data.city || '';
  formData.area = data.area || '';
  formData.address = data.address || '';
  formData.id_code = data.id_code || '';//身份证号
  formData.birth_place = data.birth_place || '';//籍贯
  formData.marriage = data.marriage || 0;//婚姻状况0未婚 1已婚
  formData.education = data.education || '';//学历0默认 1小学 2初中 3高中 4大专5本科 6硕士7硕士以上
  formData.major = data.major || ''; //专业
  formData.graduate_school = data.graduate_school || '';//毕业学校 
  formData.job_start = data.job_start || '';//从业开始时间 Y-m-d
  formData.education_info = data.education_info || '';//更多学历信息
  formData.qq = data.qq || ''; //qq
  formData.weixin = data.weixin || ''; //微信号
  formData.email = data.email || '';//
  formData.emergency_contact = data.emergency_contact || '';//紧急联系人
  formData.emergency_tel = data.emergency_tel || '';//紧急联系人电话
  formData.ntcec = data.ntcec || 0;//是否有教师资格证0无 1有 默认0
  formData.ntcec_url = data.ntcec_url || ''; //教师资格证url
  formData.education_url = data.education_url || ''; //教师资格证url
  formData.healthy_url = data.healthy_url || ''; //健康证url


  formData.is_nurse_auth = data.is_nurse_auth || data.is_nurse_auth == 0 ? data.is_nurse_auth : ''//是否持有保育师/员证书
  formData.nurse_auth_num = data.nurse_auth_num || ''//保育师/员证书编号
  formData.is_cpq_auth = data.is_cpq_auth || data.is_cpq_auth == 0 ? data.is_cpq_auth : '' //是否持有职业等级证书
  formData.cpq_auth_num = data.cpq_auth_num || ''//职业等级证书编号
  formData.is_superintendent_auth = data.is_superintendent_auth || data.is_superintendent_auth == 0 ? data.is_superintendent_auth : ''//是否持有机构负责人证书
  formData.superintendent_auth_num = data.superintendent_auth_num || ''//机构负责人证书编号
  formData.is_cettic_auth = data.is_cettic_auth || data.is_cettic_auth == 0 ? data.is_cettic_auth : ''//是否持有育婴员资格证书
  formData.cettic_auth_num = data.cettic_auth_num || ''//育婴员资格证书编号
  formData.is_security_auth = data.is_security_auth || data.is_security_auth == 0 ? data.is_security_auth : ''//是否持有保安员证书
  formData.security_auth_num = data.security_auth_num || ''//保安员证书编号

  let today = dayjs().format('YYYY-MM-DD')
  formData.nurse_date = data.nurse_date || today;
  formData.cpq_date = data.cpq_date || today;
  formData.superintendent_date = data.superintendent_date || today;
  formData.cettic_date = data.cettic_date || today;
  formData.security_adddate = data.security_adddate || today;

  formData.nurse_grade = data.nurse_grade || '';
  formData.cpq_grade = data.cpq_grade || '';
  formData.superintendent_grade = data.superintendent_grade || '';
  formData.cettic_grade = data.cettic_grade || '';
  formData.security_grade = data.security_grade || '';

  formData.other_info = data.other_info || '';//其他备注
  formData.is_security = data.is_security || 0;//是否参保0未1
  formData.security_date = data.security_date || '';//参保日期 Y-m-d
  formData.security_reason = data.security_reason || '';//未参保原因
  formData.password = data.password || '';
  cards.value = data.cards && data.cards.length ? data.cards : [{ "number": '', "id": 0 }];
  formData.id = data.id || 0;
  formData.school_id = schoolData.id;
  if (formData.province) {
    await getCity();
  }
  formData.city = data.city ? data.city * 1 : "";
  if (formData.city) {
    await getArea();
  }
  formData.area = data.area ? data.area * 1 : "";

  let p = provinceList.filter(e => { return e.code == formData.province });
  let c = cityList.value.filter(e => { return e.code == formData.city });
  let a = areaList.value.filter(e => { return e.code == formData.area });
  if (p.length && c.length && a.length) {
    fulladdress.value = p[0].text + c[0].text + a[0].text + formData.address;
  }

  console.log('fulladdress', formData);
};

async function getCity() {
  formData.city = "";
  formData.area = "";
  cityList.value = await getCityApi(formData.province);
}
async function getArea() {
  formData.area = "";
  areaList.value = await getAreaApi(formData.city);
}

const list = async (data) => {
  pageLoading.value = true;
  breadStore.breadcrumbShow = true;
  tableData.value = [];
  let params = '';
  for (const key in tableSearch) {
    const element = tableSearch[key];
    if (element !== '') {
      params += `&${key}=${element}`
    }
  }
  await api({
    url: `${baseUrl}/list?service_id=${schoolServiceId.value}&school_id=${schoolData.id}&per_page=${tablePage.per_page}&page=${tablePage.page}${params}`,
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
};

function handleExceed(files) {
  console.log(files);
}

const add = async () => {
  setFormData();
  breadStore.breadcrumbShow = false;
  isList.value = false;
  readonly.value = false;
  setTimeout(() => {
    scrollFormlEl.value.setScrollTop(0);
    console.log(scrollFormlEl.value.setScrollTop);
    formEl.value.clearValidate()
  }, 0);
};

const refresh = async () => {
  tablePage.page = 1;
  list();
};
const search = async () => {
  tablePage.page = 1;
  list();
};

const resetSearch = async () => {
  tableSearch.name = "";
  tableSearch.phone = "";
  tableSearch.dimission_status = '';
  tableSearch.job_id = '';
  tableSearch.status = "";
  tableSearch.join_start = "";
  tableSearch.join_end = "";
  tableSearch.start_date = "";
  tableSearch.end_date = "";
  tableSearch.class_id = "";
  tableSearch.orderkey = "";
  tableSearch.ordersort = "";
  tablePage.page = 1;
  list();
};
const entrySubmit = async () => {
  apiLoading.value = true;
  let data = {};
  data.id = formData.id;
  data.user_id = formData.user_id;
  data.school_id = schoolId;
  data.is_trial = formData.is_trial;
  data.join_date = formData.join_date;
  data.formal_date = formData.formal_date;
  data.status = formData.status;
  data.job_id = formData.job_id;
  data.role_ids = String(formData.role_ids);
  data.classids = String(formData.classids);
  data.service_ids = String(formData.service_ids);
  let cardsarr = (cards.value.filter(e => { return e.number != '' }));
  data.cards = cardsarr.length ? JSON.stringify(cardsarr) : ''
  data.cards_delids = cards_delids.value;

  let res = await api({
    url: `${baseUrl}/entry`,
    method: "post",
    data,
  });
  apiLoading.value = false;
  if (res) {
    ElMessage.success("操作成功");
    await list();
    formalVisible.value = false;
    leaveVisible.value = false;
    entryVisible.value = false;
    isList.value = true;
  }
}
const formalSubmit = async () => {
  apiLoading.value = true;
  let data = {};
  data.id = formData.id;
  data.user_id = formData.user_id;
  data.school_id = schoolId;
  data.formal_date = formData.formal_date;
  data.job_id = formData.job_id;
  data.role_ids = String(formData.role_ids);
  data.classids = String(formData.classids);
  data.service_ids = String(formData.service_ids);
  let cardsarr = (cards.value.filter(e => { return e.number != '' }));
  data.cards = cardsarr.length ? JSON.stringify(cardsarr) : ''
  data.cards_delids = cards_delids.value;

  let res = await api({
    url: `${baseUrl}/formal`,
    method: "post",
    data,
  });
  apiLoading.value = false;
  if (res) {
    ElMessage.success("操作成功");
    await list();
    formalVisible.value = false;
    leaveVisible.value = false;
    entryVisible.value = false;
    isList.value = true;
  }
}
const leaveSubmit = async (formEl1) => {
  if (!formEl1) return
  let valid = await formEl1.validate((valid) => { })
  if (!valid) {
    return false;
  }
  apiLoading.value = true;
  let data = {};
  data.id = formData.id;
  data.school_id = schoolId;
  data.reason = formData.dimission_reason;
  data.status = formData.status;
  data.change_time = formData.dimission_time;

  ElMessageBox.confirm(`离职后用户绑定的手机号无法继续登录使用，您确定要将${((formData.phone + '').substring(0, 3) + '****' +
    (formData.phone + '').substring(7, 11))}的用户设为离职吗？`, '提示')
    .then(async () => {
      let res = await api({
        url: `${baseUrl}/quit`,
        method: "post",
        data,
      });
      apiLoading.value = false;
      if (res) {
        ElMessage.success("操作成功");
        await list();
        formalVisible.value = false;
        leaveVisible.value = false;
        entryVisible.value = false;
        isList.value = true;
      }
    })
    .catch(() => {
      apiLoading.value = false;
    })
}

function sortChange(e) {
  let { column, prop, order } = e;
  console.log(column, prop, order);
  if (order) {
    tableSearch.orderkey = prop;
    tableSearch.ordersort = (order == 'ascending') ? 1 : 0;
  } else {
    tableSearch.orderkey = '';
    tableSearch.ordersort = '';
  }
  refresh();
}
const onSubmit = async (formEl1) => {
  if (!formEl1) return
  let valid = await formEl1.validate((valid) => { })
  if (!valid) {
    return false;
  }

  let data = {};
  for (const key in formData) {
    if (Object.hasOwnProperty.call(formData, key)) {
      const element = formData[key];
      if (Array.isArray(element)) {
        data[key] = String(element)
      } else {
        data[key] = element;
      }
      // }
    }
  }
  if (data.id && isSingleService) {
    data.service_ids = String([userServiceIds[0].id])
  }
  if (data.id == 0) {
    data.dimission_status = (data.is_trial == true) ? 2 : 1;
  }
  let cardsarr = (cards.value.filter(e => { return e.number != '' }));
  data.cards = cardsarr.length ? JSON.stringify(cardsarr) : ''
  data.cards_delids = cards_delids.value;
  console.log(data);
  apiLoading.value = true;
  if (data.id === 0) {
    let res = await api({
      url: `${baseUrl}/add`,
      method: "post",
      data,
    });
    if (res) {
      ElMessage.success("新增成功");
      await list();
      isList.value = true;
    }
  } else {
    let res = await api({
      url: `${baseUrl}/update`,
      method: "post",
      data,
    });
    if (res || res === 0) {
      ElMessage.success("修改成功");
      await list();
      isList.value = true;
    }
  }
  apiLoading.value = false;
};

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};

const form = async (id = 0) => {
  await api({
    method: "post",
    url: `${baseUrl}/form?id=${id}`,
  }).then((res) => {
    if (res) {
      setFormData(res)
    }
  });
};

function disabledDate(date) {
  let now = new Date().getTime();
  return new Date(date).getTime() >= now;
}

const config = async (id = 0) => {
  await api({
    method: "post",
    url: `${baseUrl}/select?school_id=${schoolId}`,
  }).then((res) => {
    if (res) {
      console.log(`output->res`, res);
      class_arr.value = res.class_arr;
      education.value = res.education;
      job_arr.value = res.job_arr;
      role_arr.value = res.role_arr;
    }
  });
};

const edit = async (data) => {
  setFormData(data);
  breadStore.breadcrumbShow = false;
  setTimeout(() => {
    console.log(scrollFormlEl.value);
    scrollFormlEl.value.setScrollTop(0);
    formEl.value.clearValidate()
    isList.value = false;
    readonly.value = false;
  }, 0);
};

const check = async (data, type) => {
  setFormData(data);
  breadStore.breadcrumbShow = false;
  checkTabVal.value = type;
  isList.value = false;
  readonly.value = true;
};

function status(data, status) {
  let title = ``;
  if (status == 3) {
    title = `<strong style="font-size: 16px;margin-bottom:10px">确认将${data.name}设置为已毕业吗？</strong><p>毕业后后，班级中的婴幼儿信息将在“毕业”列表中继续留存。</p>`;
  }
  if (status == 1) {
    title = `<strong style="font-size: 16px;margin-bottom:10px">确认将${data.name}设置为开启状态吗？</strong><p>开启后将延续之前的班级设置</p>`;
  }
  if (status == 0) {
    title = `<strong style="font-size: 16px;margin-bottom:10px">确认将${data.name}设置为关闭状态吗？</strong><p>关闭后后，班级将不在小程序中显示。</p>`;
  }
  ElMessageBox.confirm(title, '', {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    dangerouslyUseHTMLString: true,
  })
    .then(async () => {
      console.log(`output->data`, data);
      let res = await api({
        method: "post",
        url: `${baseUrl}/upclass`,
        data: {
          id: data.id,
          status
        },
      });
      if (res) {
        ElMessage("修改成功");
        list();
      }
    })
    .catch(() => { });
}

function onTabClick(e) {
  nextTick(() => {
    resetSearch();
    refresh();
  })
}

const quit = (data) => {
  data.dimission_time = dayjs(new Date()).format('YYYY-MM-DD');
  setFormData(data);
  formData.status = false;
  leaveVisible.value = true;
}

const entry = (data) => {
  data.join_date = dayjs(new Date()).format('YYYY-MM-DD');
  setFormData(data);
  entryVisible.value = true;
}

const formal = (data) => {
  data.formal_date = dayjs(new Date()).format('YYYY-MM-DD');
  setFormData(data);
  entryVisible.value = true;
  formalVisible.value = true;
}

const del = async (data) => {
  ElMessageBox.confirm(`确认删除该${baseName}信息吗`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      console.log(`output->data`, data);
      let ids = [data.id];
      let res = await api({
        method: "post",
        url: `${baseUrl}/delete`,
        data: {
          ids,
        },
      });
      if (res) {
        ElMessage.success("删除成功");
        list();
      }
    })
    .catch(() => { });
};

watch(
  () => route,
  (newVal) => {
    console.log(`output->`, newVal.path);
    baseUrl = "/api/back/schoolteachers";
    config();
    list();
  },
  { immediate: true, deep: true }
);
const importVisible = ref(false);
function importExcel() {
  importVisible.value = true;
  excel_arr.value = [];
  excel_file = '';
}
let excel_arr = ref([]);
let excel_file = '';
function uploadChange(e) {
  excel_file = e.raw;
}
function uploadExceed(files) {
  excel_file = files[0];
  excel_arr.value = files;
}
function uploadSubmit() {
  apiLoading.value = true;
  let formData = new FormData();
  formData.append('school_id', schoolId)
  formData.append('service_id', schoolServiceId.value)
  formData.append('excel_file', excel_file)
  api({
    method: "post",
    url: `/api/back/schoolteachers/import`,
    data: formData,
  }).then((res) => {
    if (res) {
      refresh();
      importVisible.value = false;
      ElMessage.success('添加成功')
    }
    apiLoading.value = false;
  });
}
function downModel() {
  const loadingInstance = ElLoading.service({ fullscreen: true })
  api({
    method: "get",
    url: `/api/back/schoolteachers/exportmodel`,
    params: {
      school_id: schoolId,
      service_id: schoolServiceId.value,
    },
    responseType: 'blob',
  }).then((res) => {
    loadingInstance.close();
    jsFileDownload(res.data, `教师数据模板_${dayjs().format("YYYY-MM-DD")}.xlsx`)
    ElMessage.success('导出成功')
  });
}
</script>

<style lang="scss" scoped>
img.avatar {
  width: 120px;
  height: 120px;
}

.file-bar {
  width: 100%;
  height: 50px;
  line-height: 50px;
  padding: 0 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .name {
    width: 70%;
  }

}

.el-icon.avatar-uploader-icon {
  font-size: 14px;
  font-style: normal;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
}

.dialog-p {
  line-height: 20px;
  color: #262626;
  margin-bottom: 16px;

  span {
    color: rgba(0, 0, 0, 0.65);
  }
}

.user-main {
  height: 120px;
  display: flex;
  padding: 0 24px;

  .avatar {
    flex: 0 0 32px;
    width: 32px;
    height: 32px;
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    .img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1890FF;
      font-size: 16px;
      font-weight: 400;
      color: #fff;
    }
  }

  .user-content {
    flex: auto;

    h2 {
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      margin: 6px 0 28px;
    }

    .label {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }

    .value {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      overflow: hidden;
    }
  }

  .user-handle {
    flex: 0 0 300px;
    width: 300px;

    .button {
      margin-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }

    .status {
      display: flex;
      justify-content: flex-end;

      .item {
        flex: 0 0 auto;
        width: auto;
        margin: 0 8px;
      }

      p {
        margin-bottom: 20px;
        text-align: right;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }

      b {
        font-size: 20px;
        text-align: right;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

}
</style>
