<template>
  <div class="page" v-loading="pageLoading">
    <el-scrollbar class="page-table">
      <div class="page-search" :class="seachOpen ? 'page-search-open' : 'page-search-close'">
        <el-form inline>
          <div class="left">
            <el-row :gutter="20">
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="上传时间：">
                  <el-date-picker value-format="YYYY-MM-DD" v-model="tableSearch.created_at" type="date"
                    placeholder="请选择" />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="所属班级：" prop="year">
                  <el-select v-model="tableSearch.class_id" placeholder="请选择">
                    <el-option v-for="item in userClass" :key="item.id" :label="item.title" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="婴幼儿姓名：">
                  <el-input v-model="tableSearch.student_name" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :sm="8" :md="8" :lg="8">
                <el-form-item label="状态：">
                  <el-select v-model="tableSearch.submit_status" placeholder="请选择">
                    <el-option v-for="(item, index) in status_arr" :key="index" :label="item.name"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="right">
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="primary" @click="seachOpen = true" link v-if="!seachOpen">展开</el-button>
              <el-button type="primary" @click="seachOpen = false" link v-if="seachOpen">收起</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="table-title flex-bt">
        <div class="left">
          <el-button type="primary" @click="add">
            <el-icon color="#FFFFFF" style="margin-right: 4px">
              <Plus />
            </el-icon>新增</el-button>
        </div>
        <div class="right">
        </div>
      </div>
      <el-table :scrollbar-always-on="true" ref="table" :data="tableData">
        <el-table-column type="index" min-width="80" fixed="left" label="序号" :index="indexSort" />
        <el-table-column prop="student.name" fixed="left" label="婴幼儿姓名" min-width="100"></el-table-column>
        <el-table-column prop="class.name" fixed="left" label="所属班级" min-width="100"></el-table-column>
        <el-table-column prop="created_at" label="上传日期" min-width="120"></el-table-column>
        <el-table-column prop="user.name" label="上传人" min-width="160"></el-table-column>
        <el-table-column prop="price" label="发票金额" min-width="170"></el-table-column>
        <el-table-column prop="spec" label="发布状态" min-width="120">
          <template #default="scope">
            <el-button link>{{ id2name(scope.row.submit_status, status_arr) }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="user.name" label="发票说明" min-width="120"></el-table-column>
        <el-table-column min-width="200" fixed="right">
          <template #header>
            <span style="padding-left: 2px">操作</span>
          </template>
          <template #default="scope">
            <el-button type="primary" @click="see(scope.row)" link>查看</el-button>
            <el-button :disabled="scope.row.submit_status == 1" type="primary" @click="edit(scope.row)"
              link>编辑</el-button>
            <el-button :disabled="scope.row.submit_status == 1" type="primary" @click="push(scope.row)"
              link>发布</el-button>
            <el-button :disabled="scope.row.submit_status == 1" type="danger" @click="del(scope.row)"
              link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :page-size="tablePage.per_page" background layout="prev, pager, next , sizes, total"
          :total="tablePage.total" v-model:current-page="tablePage.page" v-model:page-size="tablePage.per_page"
          :page-sizes="[10, 20, 30, 40, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
      <el-dialog v-model="dialogShow" :title="(formData.id ? '修改' : '新增') + '发票'" width="522px">
        <el-form :model="formData" label-position="top" label-width="200px" ref="formEl" :rules="rules"
          v-if="dialogShow">
          <el-form-item label="所属班级：" prop="class_id">
            <el-select v-model="formData.class_id" placeholder="请选择" @change="getStudent(formData.class_id)">
              <el-option v-for="item in userClass" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="婴幼儿：" prop="student_id">
            <el-select v-model="formData.student_id" placeholder="请选择">
              <el-option v-for="item in student_arr" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="发票金额：" prop="price">
            <el-input maxlength="10" v-model="formData.price"
              @input="(value) => { formData.price = value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, '$1') }"
              placeholder="请输入"><template #append>元</template></el-input>
          </el-form-item>
          <el-form-item label="发票图片：" prop="img_url">
            <el-upload accept="image/*" :limit="1" :on-exceed="exceed" :on-change="imageUpload" ref="agreement_url" :auto-upload="false"
              :show-file-list="false">
              <template #trigger>
                <el-button>上传文件</el-button>
              </template>
              <div v-if="formData.img_url" class="flex-bt" style="margin: 12px 0 0">
                <FileBox :url="formData.img_url" />
                <el-icon style="cursor: pointer" @click="formData.img_url = ''">
                  <Close />
                </el-icon>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  建议上传文件格式：.png，.jpg
                </div>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input type="textarea" :rows="3" maxlength="300" show-word-limit v-model="formData.remark"
              placeholder="请输入" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogShow = false">取消</el-button>
            <el-button type="primary" @click="onSubmit(formEl)" :loading="apiLoading">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog v-model="seeShow" :title="'查看发票'" width="522px">
        <el-descriptions size="large" title="" :column="1">
          <el-descriptions-item width="120px" label-align="right" label="婴幼儿：">{{ formData.student_name
          }}</el-descriptions-item>
          <el-descriptions-item width="120px" label-align="right" label="所属班级：">{{ formData.class_name || '-'
          }}</el-descriptions-item>
          <el-descriptions-item width="120px" label-align="right" label="发票金额：">{{ formData.price || '-'
          }}元</el-descriptions-item>
          <el-descriptions-item width="120px" label-align="right" label="发票图片：">
            <FileBox :url="formData.img_url" />
          </el-descriptions-item>
          <el-descriptions-item width="120px" label-align="right" label="发布状态：">{{ id2name(formData.submit_status, status_arr) || '-'
          }}</el-descriptions-item>
        </el-descriptions>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="seeShow = false">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </el-scrollbar>
  </div>
</template>

<script setup>
import {
  Plus,
  ArrowDown,
  RefreshRight,
  CircleCloseFilled,
  View,
  Hide
} from "@element-plus/icons-vue";
import { watch, onMounted, reactive, ref, provide, inject, nextTick, computed } from "vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
import { useRoute } from "vue-router";
import { str2arr, id2name, hospital_business_type, account_status } from "@/config/select";
import { upload } from "@/assets/utils/obs.js";
import { useConfigStore } from "@/stores/config-store";
import { dayjs } from 'element-plus';
import { useBreadStore } from '@/stores/bread-store';
const { provinceList, getCityApi, getAreaApi, getStreetApi } = useConfigStore();
import FileBox from '@/views/components/uploadfile.vue';

const breadStore = useBreadStore();
const detailData = ref('');
provide('detailData', detailData);
// const { organizations } = useAuthStore().userInfo;
const { schoolId, userClass } = useAuthStore();
// const { level, city } = organizations;
// console.log('政府等级', level);
let seeShow = ref(false);
const route = useRoute();
let baseUrl = "/api/back/invoicemanage";
let baseName = ref('发票');
let currentYear = new Date().getFullYear();
let year_arr = ref([currentYear - 1, currentYear, currentYear + 1]);
let status_arr = ref([{
  id: 0,
  name: '未发布'
},
{
  id: 1,
  name: '已发布'
}]);
let submit_status_arr = ref([]);
let plan_type = ref([]);
let dialogShow = ref(false);
let pageLoading = ref(true);
// let areaList = ref([]);
// async function getArea() {
//   areaList.value = await getAreaApi(city);
// }
let tablePage = reactive({
  total: 0,
  page: 1,
  per_page: 10,
});
let formEl = ref(null);
let startendtime = computed({
  get() {
    return [formData.valid_begin_time, formData.valid_end_time];
  },
  set(newValue) {
    [formData.valid_begin_time, formData.valid_end_time] = [...newValue];
  },
});
let formData = reactive({
  id: '',
  class_id: '',
  student_id: '',
  class_name: '',
  student_name: '',
  img_url: '',
  price: '',
  remark: '',
  end_time: '',
  submit_status: '',
});
let apiLoading = ref(false);
let tableSearch = reactive({
  created_at: '',
  class_id: '',
  student_name: '',
  submit_status: ''
});
let seachOpen = ref(false);
let tableData = ref([]);


function exceed(files) {
  imageUpload({ raw: files[0] });
}

let uploadmodel = ref('');
async function imageUpload(file) {
  let rawFile = file.raw;
  if (rawFile.size / 1024 / 1024 > 100) {
    ElMessage.error("文件大小不可超过100M");
    return false;
  }
  if (!rawFile.name.endsWith(".wps") && !rawFile.name.endsWith(".png") && !rawFile.name.endsWith(".jpg")) {
    ElMessage.info('请上传格式为.wps，.png，.jpg的文件')
    uploadmodel.value.clearFiles();
    return;
  } 
  pageLoading.value = true;
  console.log(rawFile.name);
  formData.img_url = await upload(rawFile, `invoice/${formData.id}`);
  pageLoading.value = false;
}

let student_arr = ref([]);
const getStudent = async (id = 0) => {
  if (id == 0) {
    return;
  }
  student_arr.value = [];
  await api({
    method: 'get',
    url: `/api/back/smartcup/getstudent?class_id=${id}`,
  }).then((res) => {
    if (res) {
      student_arr.value = res;
    }
  });
};


function dd1(date) { if (formData.status != 1) { return dayjs(date) >= dayjs(formData.end_time) } else { return dayjs(date) <= dayjs(formData.begin_time) } }
function dd2(date) { return dayjs(date) <= dayjs(formData.begin_time) }
let rules = {
  class_id: [
    { required: true, message: '请选择班级', trigger: 'change' },
  ],
  student_id: [
    { required: true, message: '请选择婴幼儿', trigger: 'change' },
  ],
  price: [
    { required: true, message: '请输入发票金额', trigger: 'change' },
  ],
  img_url: [
    { required: true, message: '请上传发票', trigger: 'change' },
  ],
}

const add = async (formEL) => {
  setFormData();
  dialogShow.value = true;
};

const edit = async (data) => {
  setFormData(data);
  dialogShow.value = true;
};


const setFormData = async (data = {}) => {
  formData.id = data.id || '';
  formData.class_id = data.class_id || '';
  formData.student_id = data.student_id || '';
  formData.class_name = data.class ? data.class.name : '';
  formData.student_name = data.student ? data.student.name : '';
  formData.img_url = data.img_url || '';
  formData.price = data.price || '';
  formData.remark = data.remark || '';
  formData.end_time = data.end_time || '';
  formData.submit_status = data.submit_status;
  if(data.id) {
    getStudent(data.class.id);
  }
}

function indexSort(n) {
  let sort = tablePage.total - (((tablePage.page - 1) * (tablePage.per_page)) + n)
  if (sort > 0) {
    return sort
  }
}

function stock(data) {
  setFormData(data);
  stockNum.value = data.stock;
  stockShow.value = true;
}

const list = async (data) => {
  pageLoading.value = true;
  let params = '';
  for (const key in tableSearch) {
    const element = tableSearch[key];
    if (element !== '') {
      params += `&${key}=${element}`
    }
  }
  await api({
    url: `${baseUrl}/list?school_id=${schoolId}&per_page=${tablePage.per_page}&page=${tablePage.page}${params}`,
  }).then((res) => {
    if (res) {
      tableData.value = res.data;
      tablePage.total = res.total;
    }
  });
  pageLoading.value = false;
};

const refresh = async () => {
  tablePage.page = 1;
  list();
};
const search = async () => {
  tablePage.page = 1;
  list();
};

const handleClose = (done) => {
  ElMessageBox.confirm('尚有编辑的内容未保存，是否退出？', '提示')
    .then(() => {
      done()
    })
    .catch(() => {
    })
}

const del = async (data) => {
  ElMessageBox.confirm(`确认删除该${baseName.value}吗？`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        method: "post",
        url: `${baseUrl}/del`,
        data: {
          id: data.id
        },
      });
      if (res) {
        ElMessage.success("删除成功");
        list();
      }
    })
    .catch(() => { });
};

const handleSizeChange = (val) => {
  tablePage.per_page = val;
  list();
};
const handleCurrentChange = (val) => {
  tablePage.page = val;
  list();
};

const see = async (data) => {
  setFormData(data);
  seeShow.value = true;
};

const resetSearch = async () => {
  tableSearch.created_at = '';
  tableSearch.class_id = '';
  tableSearch.student_name = '';
  tableSearch.submit_status = '';
  tablePage.page = 1;
  list();
};

const onSubmit = async (formEl) => {
  if (!formEl) return
  let valid = await formEl.validate((valid) => { })
  if (!valid) {
    return false;
  }

  let data = {};
  for (const key in formData) {
    data[key] = formData[key];
  }

  data.school_id = schoolId;
  data.price *= 1;

  apiLoading.value = true;

  if (data.id == 0) {
    let res = await api({
      url: `${baseUrl}/add`,
      method: "post",
      data,
    });
    if (res) {
      ElMessage.success("新增成功");
      await list();
      dialogShow.value = false;
    }
  } else {
    let res = await api({
      url: `${baseUrl}/edit`,
      method: "post",
      data,
    });
    if (res) {
      ElMessage.success("修改成功");
      await list();
      dialogShow.value = false;
    }
  }

  apiLoading.value = false;
};



const push = async (data) => {
  ElMessageBox.confirm(`发布后不可修改，确认发布吗`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      let res = await api({
        url: `${baseUrl}/submit`,
        method: 'POST',
        data: {
          id: data.id
        },
      });
      if (res) {
        ElMessage.success("发布成功");
        list();
      }
    })
    .catch(() => { });
};
let street_arr = ref([]);

onMounted(() => {
  // getArea();
  list();
})

watch(
  () => route,
  (newVal) => {
  },
  { immediate: true, deep: true }
);

watch(
  () => detailData.value,
  async (newVal) => {
    // await config();
    if (newVal) {
      // breadStore.breadcrumbTitle = level == 3 ? detailData.value.plan_name + ' - 各区县奖补信息' : '激励奖补填报详情';
      breadStore.breadcrumbBack = true;
      breadStore.breadcrumbBackFun = () => {
        detailData.value = '';
      };
    } else {
      breadStore.breadcrumbTitle = '';
      breadStore.breadcrumbBack = false;
      breadStore.breadcrumbBackFun = null;
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.dialog-p {
  line-height: 20px;
  color: #262626;
  margin-bottom: 16px;

  span {
    color: rgba(0, 0, 0, 0.65);
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  margin: 0;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader img.avatar {
  width: 280px;
  height: 158px;
}

.avatar-uploader video.avatar {
  width: 280px;
  height: 158px;
}

.el-icon.avatar-uploader-icon {
  font-size: 14px;
  font-style: normal;
  color: #8c939d;
  width: 280px;
  height: 158px;
  text-align: center;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
}

.remove {
  position: absolute;
  right: 6px;
  top: 6px;
  z-index: 10;
}
</style>