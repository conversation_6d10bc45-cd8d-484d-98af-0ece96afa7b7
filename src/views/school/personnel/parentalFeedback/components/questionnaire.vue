<template>
  <div class="page" v-loading="pageLoading">
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>问卷调查</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-bt" style="width: 100%;">
        <el-page-header :icon="ArrowLeft" class="mt16" @back="router.go(-1)">
          <template #content>
            <p class="b20">问卷调查详情</p>
          </template>
        </el-page-header>
        <div class="submit flex" style="margin-top: 20px;">
          <el-button @click="preview" :disabled="isShow">预览</el-button>
          <el-button type="primary" @click="submit(true)" :disabled="isShow">保存</el-button>
          <el-button type="primary" @click="publish" :disabled="isShow">发布</el-button>
        </div>
      </div>
    </div>
    <el-scrollbar class="detail-warp">
      <div class="detail-content">
        <div class="left">
          <el-radio-group class="radio" v-model="leftRadio">
            <el-radio-button label="题型选择">题型选择</el-radio-button>
            <el-radio-button label="题目大纲">题目大纲</el-radio-button>
          </el-radio-group>
          <el-scrollbar class="left-content" v-show="leftRadio == '题型选择'">
            <b class="item-title">满意类型-评分题型</b>
            <div class="item-group">
              <div class="item-type" v-for="(item,index) in item_arr1" :key="index" @click="addItem(item)">
                {{item.type_name}}</div>
            </div>
            <b class="item-title">建议类型-普通题型</b>
            <div class="item-group">
              <div class="item-type" v-for="(item,index) in item_arr2" :key="index" @click="addItem(item)">
                {{item.type_name}}</div>
            </div>
            <b class="item-title">意见类型-主观题型</b>
            <div class="item-group">
              <div class="item-type" v-for="(item,index) in item_arr3" :key="index" @click="addItem(item)">
                {{item.type_name}}</div>
            </div>
            <b class="item-title">统计题型</b>
            <div class="item-group">
              <div class="item-type" v-for="(item,index) in item_arr4" :key="index" @click="addItem(item)">
                {{item.type_name}}</div>
            </div>
          </el-scrollbar>
          <el-scrollbar class="left-content" v-show="leftRadio == '题目大纲'">
            <div class="item-group" v-if="mainData.form.length">
              <div class="item-type2 line-1" style="flex:0 0 100% !important;justify-content: flex-start;"
                v-for="(item,index) in mainData.form" :key="index">
                {{index+1}}：{{item.title}}
              </div>
            </div>
            <div v-else class="flex-ct" style="height: 400px; color: #666">
              当前暂无题目
            </div>
          </el-scrollbar>
        </div>
        <el-scrollbar class="main">
          <div class="main-item main-title-box">
            <div class="title">
              <el-form-item label-width="100px" required label="问卷名称：">
                <el-input v-model="mainData.title" placeholder="请输入问卷名称" clearable :disabled="isShow" />
              </el-form-item>
              <el-form-item label-width="100px" required label="问卷描述：">
                <el-input type="textarea" rows="4" v-model="mainData.desc" placeholder="请输入问卷描述" clearable :disabled="isShow"/>
              </el-form-item>
            </div>
            <div class="setting">
              <el-form-item required label="开始时间：">
                <el-date-picker v-model="mainData.start_time" :disabled-date="disabledDate1" type="datetime"
                  placeholder="开始时间" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :disabled="isShow"/>
              </el-form-item>
              <div class="flex-bt">
                <el-form-item required label="结束时间：">
                  <el-date-picker v-model="mainData.end_time" :disabled-date="disabledDate2" type="datetime"
                    placeholder="不限" format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :disabled="isShow"/>
                </el-form-item>
                <el-tooltip placement="top">
                  <template #content><span
                      style="font-size: 14px;">问卷结束时间：超过问卷结束时间后，问卷停止接收答卷，<br />同时问卷变更为已结束状态，可查看问卷统计。</span></template>
                  <el-icon color="#409eff" size="20" style="margin-bottom: 24px; margin-left: 8px">
                    <Warning />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="flex-bt" v-if="identity_id == 2">
                <el-form-item required label="调研对象：">
                  <el-radio-group v-model="mainData.research_subject">
                    <el-radio label="1">托育机构</el-radio>
                    <el-radio label="2">机构班级</el-radio>
                    <el-radio label="3">机构从业人员</el-radio>
                    <el-radio label="4">机构管理人员</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <!-- <div class="flex-bt" v-if="identity_id == 2">
                <el-form-item required label="同步到：">
                  <el-checkbox-group v-model="sync_sites">
                    <el-checkbox :label="1">机构端</el-checkbox>
                    <el-checkbox :label="2">家长端</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div> -->
              <!-- <div class="flex-bt">
                <el-form-item required label="登录身份验证：">
                  <el-switch :active-value="1" :inactive-value="0" v-model="mainData.is_auth" :disabled="isShow"/>
                </el-form-item>
                <el-tooltip placement="top">
                  <template #content><span
                      style="font-size: 14px;">登录身份验证：答卷开始前验证身份填写信息，收集填写者的<br />姓名及手机号信息，用于答卷统计。</span></template>
                  <el-icon color="#409eff" size="20" style="margin-bottom: 24px; margin-left: 8px">
                    <Warning />
                  </el-icon>
                </el-tooltip>
              </div> -->
            </div>
          </div>
          <div class="main-item" @click="active_id = index" v-for="(item,index) in mainData.form"
            :class="{'active --el-box-shadow-dark': active_id == index}" :key="index">
            <div class="item-title">
              <b>{{item.type_name}}</b>
              <div class="icon">
                <el-button @click.stop="sort('up', index)" size="small" :disabled="index == 0 || isShow" link
                  type="primary">上移</el-button>
                <el-button @click.stop="sort('down', index)" size="small" :disabled="index == mainData.form.length - 1 || isShow"
                  link type="primary" style="margin-right: 6px;">下移</el-button>
                <el-icon @click.stop="copyItem(item)">
                  <DocumentCopy />
                </el-icon>
                <el-icon color="red" @click.stop="delItem(index)" :disabled="isShow">
                  <Delete />
                </el-icon>
              </div>
            </div>
            <el-form-item :required="Boolean(item.is_required)" :label="item.is_show_order ? ((index+1)+'：') : ' '">
              <el-input :disabled="active_id != index || isShow" v-model="item.title" placeholder="请输入标题" clearable />
            </el-form-item>
            <el-form-item label="题干说明：" v-if="item.is_show_desc">
              <el-input :disabled="active_id != index || isShow" v-model="item.desc" placeholder="请输入题干" clearable />
            </el-form-item>
            <!-- 单选题 -->
            <template v-if="item.type == 101 || item.type == 303">
              <el-radio-group readonly>
                <el-radio style="width: 100%" size="large" v-for="(option,option_index) in item.option"
                  :key="option_index" :label="option.value">
                  <el-input v-if="option.input_status === undefined" :disabled="active_id != index || isShow" style="width: 400px;" v-model="option.title" placeholder="请输入" clearable />
                  <template v-else>
                    {{ option.title }}
                    <el-input style="margin-left: 8px; width: 360px;" :disabled="active_id != index || isShow" v-if="option.input_status == 1" v-model="option.input_text" placeholder="请输入其他内容" clearable />
                  </template>
                  <el-button v-show="item.option.length > 1" v-if="active_id == index" type="danger" style="margin-left: 6px;" @click="delOption(item, option_index)" link :disabled="isShow">删除</el-button>
                </el-radio>
              </el-radio-group>
              <div class="item-handle" v-if="active_id == index">
                <el-button @click="addOption(item)" type="primary" link :disabled="isShow">添加选项</el-button>
                <el-button @click="addOther(item)" type="primary" link :disabled="isShow">添加其他</el-button>
              </div>
            </template>
            <template v-if="item.type == 102">
              <el-checkbox-group readonly>
                <el-checkbox style="width: 100%" size="large" v-for="(option,option_index) in item.option"
                  :key="option_index" :label="option.value">
                  <el-input v-if="option.input_status === undefined" :disabled="active_id != index || isShow" style="width: 400px;" v-model="option.title" placeholder="请输入" clearable />
                  <template v-else>
                    {{ option.title }}
                    <el-input style="margin-left: 8px; width: 360px;" :disabled="active_id != index || isShow" v-if="option.input_status == 1" v-model="option.input_text" placeholder="请输入其他内容" clearable />
                  </template>
                  <el-button v-show="item.option.length > 1" v-if="active_id == index" type="danger" style="margin-left: 6px;" @click="delOption(item, option_index)" link :disabled="isShow">删除</el-button>
                </el-checkbox>
              </el-checkbox-group>
              <div class="item-handle" v-if="active_id == index">
                <el-button @click="addOption(item)" type="primary" link :disabled="isShow">添加选项</el-button>
                <el-button @click="addOther(item)" type="primary" link :disabled="isShow">添加其他</el-button>
              </div>
            </template>
            <template v-if="item.type == 201 || item.type == 301 || item.type == 302">
              <el-input readonly show-word-limit :disabled="active_id != index || isShow" :maxlength="item.max" :placeholder="item.placeholder" clearable />
            </template>
            <template v-if="item.type == 202">
              <el-input readonly show-word-limit :disabled="active_id != index" :maxlength="item.max" type="textarea"
                :placeholder="item.placeholder" clearable />
            </template>
            <template v-if="item.type == 304">
              <el-date-picker readonly :disabled="active_id != index || isShow" value-format="YYYY-MM-DD" format="YYYY-MM-DD" type="date" :placeholder="item.placeholder" />
            </template>
            <template v-if="item.type == 305">
              <el-input readonly :disabled="active_id != index" style="margin-bottom: 12px;width: 400px;;"
                v-model="item.province" placeholder="请选择省份">
              </el-input>
              <br />
              <el-input v-if="item.level > 1" readonly :disabled="active_id != index"
                style="margin-bottom: 12px;width: 400px;;" v-model="item.province" placeholder="请选择城市">
              </el-input>
              <br />
              <el-input v-if="item.level > 2" readonly :disabled="active_id != index"
                style="margin-bottom: 12px;width: 400px;;" v-model="item.province" placeholder="请选择区县">
              </el-input>
              <br />
              <el-input v-if="item.level > 3" readonly :disabled="active_id != index"
                style="margin-bottom: 12px;width: 400px;;" v-model="item.province" placeholder="请选择街道">
              </el-input>
              <br />
              <el-input v-if="item.level > 4" readonly :disabled="active_id != index"
                style="margin-bottom: 12px;width: 400px;;" type="textarea" placeholder="请输入详细地址" clearable />
            </template>
            <template v-if="item.type == 306">
              <div class="sign-card">
                电子签名
              </div>
            </template>
            <template v-if="item.type == 401">
              <el-upload readonly v-model:file-list="item.value" disabled :limit="item.max" :auto-upload="false"
                list-type="picture-card">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
            </template>
            <template v-if="item.type == 402">
              <el-rate readonly v-model="value" size="large" />
              <el-input readonly v-if="item.is_show_text" show-word-limit :disabled="active_id != index || isShow" :maxlength="item.max" type="textarea" :placeholder="item.placeholder" clearable />
            </template>
          </div>
          <div class="add-tip" v-if="mainData.form.length == 0">
            请点击左侧【题型选择】，进行问卷内容设置
          </div>
          <div class="main-item after-box" v-else>
            <el-form-item label="提交问卷后处理方式：">
              <el-select v-model="mainData.deal_way" placeholder="请选择" :disabled="isShow">
                <el-option :value="1" label="显示提示语"></el-option>
                <el-option :value="2" label="跳转页面"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="提示语：" v-if="mainData.deal_way == 1">
              <el-input v-model="mainData.deal_way_msg" placeholder="请输入提示语" clearable :disabled="isShow" />
            </el-form-item>
            <el-form-item label="跳转链接：" v-if="mainData.deal_way == 2">
              <el-input v-model="mainData.deal_way_url" placeholder="请输入跳转链接" clearable :disabled="isShow" />
            </el-form-item>
          </div>
        </el-scrollbar>
        <el-scrollbar class="right right-form">
          <template v-if="active_item">
            <b class="item-title">序号：{{ active_id + 1 }}</b>
            <b class="item-title">题目：{{ active_item.title }}</b>
            <br />
            <b class="item-title">基础题目设置</b>
            <div style="padding: 0 24px;">
              <el-form-item label="此题必答：">
                <el-switch :active-value="1" :inactive-value="0" v-model="active_item.is_required" :disabled="isShow" />
              </el-form-item>
              <el-form-item label="显示序号：">
                <el-switch :active-value="1" :inactive-value="0" v-model="active_item.is_show_order" :disabled="isShow" />
              </el-form-item>
              <el-form-item label="题干说明：">
                <el-switch :active-value="1" :inactive-value="0" v-model="active_item.is_show_desc" :disabled="isShow" />
              </el-form-item>
              <el-form-item label="跳题设置：" v-if="active_item.type == 101 || active_item.type == 303">
                <el-button @click="setJump(item)" type="primary" link >设置</el-button>
              </el-form-item>
            </div>
            <template
              v-if="active_item.type == 201 || active_item.type == 202 || active_item.type == 301 || active_item.type == 302">
              <b class="item-title" style="margin-top: 24px;">文本题目设置</b>
              <div style="padding: 0 24px;">
                <el-form-item label="最少填写字数：" v-if="active_item.type != 302">
                  <el-input :min="0" v-model.number="active_item.min" :controls="true" :disabled="isShow" />
                </el-form-item>
                <el-form-item label="最多填写字数：" v-if="active_item.type != 302">
                  <el-input :min="0" v-model.number="active_item.max" :controls="true" :disabled="isShow" />
                </el-form-item>
                <el-form label-position="top">
                  <el-form-item label="输入区默认文本：">
                    <el-input v-model="active_item.placeholder" :disabled="isShow" />
                  </el-form-item>
                </el-form>
              </div>
            </template>
            <template v-if="active_item.type == 401">
              <b class="item-title" style="margin-top: 24px;">图片题目设置</b>
              <div style="padding: 0 24px;">
                <el-form-item label="图片上传最多数量：">
                  <el-input :min="0" v-model.number="active_item.max" :controls="true" :disabled="isShow" />
                </el-form-item>
              </div>
            </template>
            <template v-if="active_item.type == 302">
              <b class="item-title" style="margin-top: 24px;">手机号模板设置</b>
              <div style="padding: 0 24px;">
                <el-form-item label="手机号只能提交一次：">
                  <el-switch :active-value="1" :inactive-value="0" v-model="active_item.is_unique_phone" :disabled="isShow" />
                </el-form-item>
              </div>
            </template>
            <template v-if="active_item.type == 305">
              <b class="item-title" style="margin-top: 24px;">地址模板设置</b>
              <div style="padding: 0 24px;">
                <el-radio-group v-model="active_item.level" :disabled="isShow">
                  <el-radio style="width: 100%" size="large" :label="5">
                    详细到地址
                  </el-radio>
                  <el-radio style="width: 100%" size="large" :label="4">
                    详细到街道
                  </el-radio>
                  <el-radio style="width: 100%" size="large" :label="3">
                    详细到区县
                  </el-radio>
                  <el-radio style="width: 100%" size="large" :label="2">
                    详细到城市
                  </el-radio>
                  <el-radio style="width: 100%" size="large" :label="1">
                    详细到省份
                  </el-radio>
                </el-radio-group>
              </div>
            </template>
            <template v-if="active_item.type == 402">
              <b class="item-title" style="margin-top: 24px;">评分题目设置</b>
              <div style="padding: 0 24px;">
                <el-form-item label="显示评价：">
                  <el-switch :active-value="1" :inactive-value="0" v-model="active_item.is_show_text" :disabled="isShow" />
                </el-form-item>
                <el-form-item label="评价必答：">
                  <el-switch :active-value="1" :inactive-value="0" v-model="active_item.is_required_text" :disabled="isShow" />
                </el-form-item>
                <el-form-item label="最少填写字数：">
                  <el-input :min="0" v-model.number="active_item.min" :controls="true" :disabled="isShow" />
                </el-form-item>
                <el-form-item label="最多填写字数：">
                  <el-input :min="0" v-model.number="active_item.max" :controls="true" />
                </el-form-item>
                <el-form label-position="top">
                  <el-form-item label="输入区默认文本：">
                    <el-input v-model="active_item.placeholder" :disabled="isShow" />
                  </el-form-item>
                </el-form>
              </div>
            </template>
          </template>
          <div v-else class="flex-ct" style="height: 400px; color: #666">
            当前暂无题目
          </div>
        </el-scrollbar>
      </div>
    </el-scrollbar>
  </div>
  <el-dialog v-model="dialogShow" :title="'题目逻辑设置（'+jump_data.type_name+'：按选项跳题）'" width="600px">
    <el-form-item label="当前设置题目：">
      {{active_id + 1}}：{{ jump_data.title }}
    </el-form-item>
    <el-form-item label="选择逻辑类型：">
      <el-radio-group v-model="jump_data.jump_type" @change="jumpTypeChange" :disabled="isShow">
        <el-radio :label="1">按选项跳题</el-radio>
        <el-radio :label="2">无条件跳题</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-descriptions :column="1" border v-if="jump_data.jump_type == 1">
      <el-descriptions-item v-for="(item,index) in jump_data.option" :key="index">
        <template #label>
          {{item.title}}
        </template>
        <el-select v-model="item.jump_to" placeholder="不跳转，按顺序展示下一题" :disabled="isShow">
          <el-option :value="q.index" :label="q.title" v-for="(q,qi) in jump_arr" :key="qi"></el-option>
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
    <el-form-item label="跳转到：" v-if="jump_data.jump_type == 2">
      <el-select v-model="jump_data.jump_to" placeholder="不跳转，按顺序展示下一题" :disabled="isShow">
        <el-option :value="q.index" :label="q.title" v-for="(q,qi) in jump_arr" :key="qi"></el-option>
      </el-select>
    </el-form-item>
    <div class="tip">
      <p><b>提示：</b></p>
      <p>1、跳题逻辑 <b>按选项跳题</b> 为根据填写者选择问题的选项，关联该选项后续的结果和题目，答题后可直接跳转。</p>
      <p>2、跳题逻辑 <b>无条件跳题</b> 为无论填写者选择哪一选项，均统一关联该选项后续的结果和题目，答题后可直接跳转。</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogShow = false" >取消</el-button>
        <el-button type="primary" @click="jumpSubmit" :disabled="isShow">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog style="width: 423px" title="预览" v-model="previewShow">
    <object v-if="previewShow" :data="previewUrl" width="375" height="667"
      type="text/html"></object>
  </el-dialog>˝
</template>

<script setup>
import {
  Plus,
  DocumentCopy,
  Delete,
  Sort,
  RefreshRight,
  ArrowLeft,
  Warning
} from "@element-plus/icons-vue";
import Breadcrumb from "@/layout/components/Breadcrumb.vue";
import { ElLoading, dayjs, ElMessage } from 'element-plus'
import { watch, onMounted, reactive, ref, nextTick, computed } from "vue";
import api from "@/axios.js";
import { useAuthStore } from "@/stores/auth-store";
import { useRoute, useRouter } from "vue-router";
import { str2arr,  } from "@/config/select";
import { useBreadStore } from "@/stores/bread-store";
import { usePlatformStore } from "@/stores/platform-store";

const breadStore = useBreadStore();
const route = useRoute();
const router = useRouter();
const { organizations_id, identity_id } = useAuthStore().userInfo;
const { h5_url } = usePlatformStore().platformData;

const { isSuperAdmin, userServiceIds, schoolId, isSingleService } = useAuthStore();
const schoolData = useAuthStore().userSchool;
const getAssetsFile = (url) => {
  return new URL(`../../assets/images/inspect/${url}`, import.meta.url).href
}

let previewUrl = computed(() => {
  return h5_url + '/h5/wenjuan/preview.html?time=' + new Date().getTime() + '&id=' + mainData.id
})

let dialogShow = ref(false);

let jump_data = ref('');
let jump_arr = ref([]);

let active_item = computed(() => {
  if (active_id !== '') {
    return mainData.form[active_id.value]
  }
  return {}
});


// let sync_sites = computed({
//   get() {
//     return str2arr(mainData.sync_sites, true);
//   },
//   set(newValue) {
//     mainData.sync_sites = newValue.join(',');
//   },
// });
let color = ['', '#F04134', '#FFBA01', '#1ABE6B', '#0079FE']
let pageLoading = ref(true);
let baseUrl = "/api/back/jyform";
let item_arr1 = [
  {
    type_name: '评分单选',
    type: 101,
  },
  {
    type_name: '评分多选',
    type: 102,
  },
  // {
  //   type_name: '量表题',
  //   type: 103,
  // },
]
let item_arr2 = [
  {
    type_name: '普通单选',
    type: 201,
  },
  {
    type_name: '普通多选',
    type: 202,
  },
  {
    type_name: '下拉列表',
    type: 202,
  },
]
let item_arr3 = [
  {
    type_name: '单行填空',
    type: 301,
  },
  {
    type_name: '多行填空',
    type: 302,
  },
  {
    type_name: '文件上传',
    type: 303,
  },
  {
    type_name: '日期',
    type: 304,
  },
  {
    type_name: '城市/地址',
    type: 305,
  }, {
    type_name: '电子签名',
    type: 306,
  },
]
let item_arr4 = [
  {
    type_name: '图片上传',
    type: 401,
  },
  {
    type_name: '评分',
    type: 402,
  },
]
let leftRadio = ref('题型选择')
let main_arr = ref([]);
let inspect_info = ref('');
let stars_list = ref([]);
let opinion = ref('');
let stars_num = ref('');
let detail_id = ref('');
let loading = ref(false);
let imgSrc = ref('');
let chart = ref('');
let tableData = ref('');
let previewShow = ref(false);
let qrCodeData = computed(() => {
  return shareUrl + '?time=' + new Date().getTime() + '&id=' + mainData.id
})

let shareUrl = h5_url + '/h5/wenjuan/index.html'
let mainData = reactive({
  tagIndex: 0,
  title: '问卷调查',
  desc: '感谢您能抽出几分钟来完成本次问卷答题，感谢您。让我们开始吧！',
  start_time: dayjs().format('YYYY-MM-DD HH:mm'),
  end_time: '',
  is_auth: 0,
  form: [],
  deal_way: 1,
  deal_way_msg: '',
  deal_way_url: '',
  sync_sites: '1,2',
  research_subject: 0,
})

let active_id = ref(0);

function addItem(item) {
  if (isShow) {
    ElMessage({
      message: '当前为查看状态，不允许操作',
      type: 'warning',
    })
    return
  }
  mainData.tagIndex++;
  //1普通文本2多行文本3单选4多选5下拉框6单项填空7多项填空8上传图片9星级评价
  if (item.type == 101) {
    // 单选
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      value: '',
      type_name: item.type_name,
      title: '',
      desc: '',
      placeholder: '请选择',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      option: [{
        title: '',
        value: 1,
        jump_to: '',
      }]
    })
  } else if (item.type == 102) {
    // 多选
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      placeholder: '请选择',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      value: [],
      option: [{
        title: '',
        value: 1,
        jump_to: '',
      }]
    })
  } else if (item.type == 201 || item.type == 202) {
    // 单行文本
    // 多行文本
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      value: '',
      placeholder: '请输入',
      min: 1,
      max: 100,
    })
  } else if (item.type == 301 || item.type == 302) {
    // 多行文本
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      value: '',
      placeholder: '请输入' + item.type_name,
      input_type: item.type == 302 ? 'number' : 'text',
      min: item.type == 302 ? 11 : 1,
      max: item.type == 302 ? 11 : 20,
      is_unique_phone: false
    })
  } else if (item.type == 303) {
    // 单选
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      value: '',
      placeholder: '请选择性别',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      option: [{
        title: '男',
        value: mainData.tagIndex + 1,
        jump_to: '',
      }, {
        title: '女',
        value: mainData.tagIndex + 2,
        jump_to: '',
      }]
    })
    mainData.tagIndex += 2;
  } else if (item.type == 304) {
    // 日期
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      placeholder: '请选择日期',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      value: '',
    })
  } else if (item.type == 305) {
    // 单选
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      level: 5,
      placeholder: '请选择城市',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      province: '',
      value: '',
      city: '',
      area: '',
      street: '',
      address: '',
    })
  } else if (item.type == 306) {
    // 签名
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      desc: '',
      placeholder: '请填写电子签名',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      value: '',
    })
  } else if (item.type == 401) {
    // 图片
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      placeholder: '请上传图片',
      desc: '',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      value: [],
      max: 3,
    })
  } else if (item.type == 402) {
    // 评分
    mainData.form.push({
      index: mainData.tagIndex,
      type: item.type,
      type_name: item.type_name,
      title: '',
      placeholder: '请进行评分及评价',
      desc: '',
      is_required: 1,//是否必填
      is_show_order: 1,//是否显示序号
      is_show_desc: 0,//是否显示题干描述
      jump_to: '',//跳题
      jump_type: 1,
      text: '',
      is_show_text: 1,
      is_required_text: 1,
      min: 1,
      max: 100,
      value: '',
    })
  }
}

function addOption(item) {
  mainData.tagIndex++;

  item.option.push({
    title: '',
    value: mainData.tagIndex,
    jump_to: ''
  })
  nextTick(() => {
    active_id.value = item.option.length;
  })
}

function addOther(item) {
  mainData.tagIndex++;
  item.option.push({
    title: '其他',
    value: mainData.tagIndex,
    input_status: 1,
    input_text: '',
    jump_to: ''
  })
}

function delOption(item, option_index) {
  if (item.option.length == 1) {
    ElMessage.info('至少需要保留一个选项');
    return;
  }
  // 单选
  item.option.splice(option_index, 1)
}

function delItem(item_index) {
  if (isShow) {
    ElMessage({
      message: '当前为查看状态，不允许操作',
      type: 'warning',
    })
    return
  }
  ElMessageBox.confirm(`确认删除该题吗？`, "提示", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      console.log(item_index);
      mainData.form.splice(item_index, 1)
    })
}

function copyItem(item) {
  if (isShow) {
    ElMessage({
      message: '当前为查看状态，不允许操作',
      type: 'warning',
    })
    return
  }
  mainData.tagIndex++;
  let newItem = JSON.parse(JSON.stringify(item));
  newItem.index = mainData.tagIndex;
  mainData.form.push(newItem)
}

function disabledDate1(date) {
  let now = new Date().getTime();
  if (mainData.end_time) {
    let before = new Date(mainData.end_time).getTime();
    return new Date(date).getTime() < now || new Date(date).getTime() > before;
  } else {
    return new Date(date).getTime() < now;
  }
}

function disabledDate2(date) {
  let now = new Date().getTime();
  if (mainData.start_time) {
    let before = new Date(mainData.start_time).getTime();
    return new Date(date).getTime() < before
  } else {
    return new Date(date).getTime() < now;
  }
}

async function submit(message = false) {
  for (const iterator of mainData.form) {
    if (!iterator.title) {
      ElMessage.info('请检查标题不能为空');
      return
    }
  }
  pageLoading.value = true;
  let data = { ...mainData };
  console.log(data);
  data.form = JSON.stringify(data.form);
  await api({
    url: `${baseUrl}/${data.id ? 'edit' : 'add'}`,
    method: 'post',
    data: data
  }).then((res) => {
    pageLoading.value = false;
    if (res) {
      if (data.id == 0) {
        if (message) {
          ElMessage.success('保存成功');
        }
        router.back()
      } else {
        if (message) {
          ElMessage.success('保存成功');
        }
        list();
      }
    }
  });
}

async function preview() {
  await submit();
  previewShow = true;
}

function jumpSubmit() {
  console.log(jump_data.value);
  console.log(mainData.form);
  mainData.form[active_id.value] = JSON.parse(JSON.stringify(jump_data.value));
  dialogShow.value = false;
}

function jumpTypeChange(value) {
  console.log(value);
  if (value == 1) {
    jump_data.value.jump_to = '';
  } else if (value == 2) {
    for (const iterator of jump_data.value.option) {
      iterator.jump_to = '';
    }
  }
  console.log(jump_data.value);
}

function setJump() {
  let arr = [];
  if (active_item.value) {
    jump_data.value = JSON.parse(JSON.stringify(active_item.value));
    mainData.form.forEach((e, i) => {
      if (i > active_id.value) {
        arr.push(e);
      }
    })
    jump_arr.value = arr;
    dialogShow.value = true;
  }
}

const list = async (data) => {
  pageLoading.value = true;
  await api({
    url: `${baseUrl}/detail?id=${detail_id.value}`,
  }).then((res) => {
    if (res) {
      let { tagIndex, id, title, desc, start_time, end_time, is_auth, form, deal_way_msg, deal_way_url, sync_sites, research_subject } = res;
      mainData.id = id;
      mainData.title = title;
      mainData.tagIndex = tagIndex || 0;
      mainData.desc = desc;
      mainData.start_time = start_time;
      mainData.end_time = end_time;
      mainData.is_auth = is_auth;
      mainData.form = form || [];
      mainData.deal_way = deal_way_msg ? 2 : 1;
      mainData.deal_way_msg = deal_way_msg;
      mainData.deal_way_url = deal_way_url;
      // mainData.sync_sites = sync_sites;
      mainData.research_subject = research_subject;
    }
    console.log(mainData);
    pageLoading.value = false;
  });
};

function sort(type, index) {
  if (type == 'up') {
    mainData.form.splice(index, 1, ...mainData.form.splice(index - 1, 1, mainData.form[index]))
  } else if (type == 'down') {
    mainData.form.splice(index, 1, ...mainData.form.splice(index + 1, 1, mainData.form[index]))
  }
  nextTick(() => {
    active_id.value = type == 'up' ? index - 1 : index + 1;
  })
}

const publish = async () => {
  ElMessageBox.confirm(`发布后用户可提交答卷`, "是否发布", {
    type: "warning",
    confirmButtonText: "确认",
    cancelButtonText: "取消",
  })
    .then(async () => {
      await submit();
      let res = await api({
        method: "post",
        url: `${baseUrl}/release`,
        data: {
          id: mainData.id
        },
      });
      if (res) {
        ElMessage.success("发布成功");
        router.back();
      }
    })
    .catch(() => { });
};

const isShow = ref(false);
watch(
  () => route,
  (newVal) => {
    detail_id.value = newVal.query.id;
    isShow.value = newVal.query.isShow === 'true';
    pageLoading.value = false;
    if(identity_id == 2) {
      baseUrl = '/api/back/schooljyform'
    }
    if (detail_id.value != 0) {
      list();
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.page {
  height: calc(100vh);
  overflow: hidden;

  .breadcrumb {
    height: $breadcrumbHeight;
    width: 100%;
    background: #ffffff;
    // position: fixed;
    // left: 0;
    // top: $headerHeight;
    z-index: 10;
    padding: 0 24px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    // box-shadow: 0px 8px 20px rgba(0, 0, 0, .08);
    // border-bottom: 1px solid rgb(244, 244, 244);
  }
}

.sign-card {
  height: 250px;
  border: 1px solid;
  width: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  background: #f5f5f5;
  display: flex;
  justify-content: space-between;
  height: calc(100vh - 98px);
  min-width: 1200px;

  .left,
  .right {
    width: 234px;
    background: #fff;
    margin: 12px;
    flex: 0 0 234px;
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;

    .left-content {
      height: calc(100% - 80px);
    }

    .el-radio-group {
      margin: 20px;
      width: 100%;
    }

    .item-title {
      width: 100%;
      padding: 0 20px;
    }

    .item-group {
      margin: 20px auto;
      display: flex;
      flex-wrap: wrap;
      padding: 0 20px;

      .item-type {
        width: 90px;
        flex: 0 0 90px;
        margin: 0 12px 12px 0;
        height: 34px;
        border-radius: 4px;
        background: #f5f5f5;
        padding: 0 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border: 1px solid #fff;

        &:hover {
          background: #409eff;
          color: #fff;
        }

        &:nth-child(2n) {
          margin: 0 0 12px 0;
        }
      }

      .item-type2 {
        margin: 0 12px 12px 0;
        height: 34px;
        border-radius: 4px;
        background: #f5f5f5;
        padding: 0 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #fff;
      }
    }
  }

  .main {
    overflow: hidden;
    height: calc(100vh - 98px - 24px);

    .el-radio-group {
      width: 100%;
    }
  }

  .right {
    overflow: hidden;
    padding-top: 24px;
    height: calc(100vh - 98px - 24px);
  }

  .right-form .el-form-item {
    margin-bottom: 6px !important;
  }

  .item-title {
    width: 100%;
    display: flex;
    margin-bottom: 24px;
    justify-content: space-between;

    .icon {
      display: flex;
      overflow: hidden;
      width: 140px;
      cursor: pointer;

      .el-icon {
        display: block;
        margin: 0 10px;
      }
    }
  }


  .main {
    flex: auto;
    margin: 12px;
    border-radius: 4px;

    .main-title-box {
      display: flex;

      .title {
        flex: auto;
      }

      .setting {
        flex: 0 0 300px;
        margin-left: 24px;
      }
    }

    .main-item {
      border-radius: 4px;
      padding: 24px;
      margin-bottom: 12px;
      background: #fff;
      border: 1px dashed transparent;
    }

    .active {
      box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.08);
      background: #fff;
      border: 1px dashed #409eff;

      .item-title b {
        color: #409eff;
      }

      .active_hide {
        display: block;
      }
    }

    .active_hide {
      display: none;
    }

    .add-tip {
      line-height: 80px;
      border: 1px dashed #999;
      border-radius: 4px;
      margin-top: 24px;
      text-align: center;
    }

    .item-handle {
      margin-top: 24px;
    }
  }
}

.noborderinput {
  border: none;
  line-height: 2;
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding-left: 4px;
  border: 1px dashed rgba(204, 204, 204, 0);

  &:hover {
    border: 1px dashed rgb(204, 204, 204);
  }

  &:focus {
    border: 1px solid rgb(204, 204, 204);
  }
}

.noborderinput_title {
  font-size: 16px;
}

.tip {
  margin-top: 24px;
  line-height: 1.5;

  p {
    margin-top: 6px;
  }
}
</style>
